<?php
/**
 * FRP 服务器配置文件
 * 
 * 请根据您的 FRP 服务器实际情况修改以下配置
 */

// FRP API 服务器配置
$frp_config = [
    // FRP API 服务器地址（您的 frps-api 插件运行地址）
    'server_url' => 'http://localhost:7300',
    
    // FRP API 访问令牌（与您的 frps-api 插件中设置的 API_TOKEN 一致）
    'api_token' => 'frps-api-token-2024',
    
    // 连接超时时间（秒）
    'timeout' => 30,
    
    // 连接超时时间（秒）
    'connect_timeout' => 10,
    
    // 是否启用 SSL 验证（如果使用 HTTPS）
    'ssl_verify' => false,
    
    // 调试模式（开启后会记录更多日志信息）
    'debug' => false
];

/**
 * 使用说明：
 * 
 * 1. 确保您的 FRP 服务器已经安装并运行了 frps-api 插件
 * 2. 修改上面的 server_url 为您的 FRP API 服务器实际地址
 * 3. 修改 api_token 为您在 frps-api 插件中设置的令牌
 * 4. 如果您的 FRP API 服务器使用了 HTTPS，请将 server_url 改为 https:// 开头
 * 5. 如果遇到 SSL 证书问题，可以将 ssl_verify 设置为 false
 * 
 * 配置示例：
 * 
 * 本地测试环境：
 * 'server_url' => 'http://localhost:7300'
 * 'api_token' => 'frps-api-token-2024'
 * 
 * 远程服务器环境：
 * 'server_url' => 'http://your-frp-server.com:7300'
 * 'api_token' => 'your-custom-secure-token'
 * 
 * HTTPS 环境：
 * 'server_url' => 'https://your-frp-server.com:7300'
 * 'api_token' => 'your-custom-secure-token'
 * 'ssl_verify' => true
 */
?>
