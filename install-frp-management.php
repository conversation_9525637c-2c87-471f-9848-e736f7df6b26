<?php
/**
 * FRP 管理功能安装向导
 * 
 * 帮助用户快速配置 FRP 管理功能
 */

// 检查是否已经安装
$configFile = __DIR__ . '/frp-config.php';
$isInstalled = file_exists($configFile);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    $serverUrl = trim($_POST['server_url']);
    $apiToken = trim($_POST['api_token']);
    $timeout = intval($_POST['timeout']) ?: 30;
    $connectTimeout = intval($_POST['connect_timeout']) ?: 10;
    $sslVerify = isset($_POST['ssl_verify']) ? 'true' : 'false';
    $debug = isset($_POST['debug']) ? 'true' : 'false';
    
    // 验证输入
    $errors = [];
    if (empty($serverUrl)) {
        $errors[] = '服务器地址不能为空';
    }
    if (empty($apiToken)) {
        $errors[] = 'API Token 不能为空';
    }
    if (!filter_var($serverUrl, FILTER_VALIDATE_URL)) {
        $errors[] = '服务器地址格式不正确';
    }
    
    if (empty($errors)) {
        // 生成配置文件内容
        $configContent = "<?php\n";
        $configContent .= "/**\n";
        $configContent .= " * FRP 服务器配置文件\n";
        $configContent .= " * \n";
        $configContent .= " * 自动生成于: " . date('Y-m-d H:i:s') . "\n";
        $configContent .= " */\n\n";
        $configContent .= "// FRP API 服务器配置\n";
        $configContent .= "\$frp_config = [\n";
        $configContent .= "    // FRP API 服务器地址\n";
        $configContent .= "    'server_url' => '" . addslashes($serverUrl) . "',\n";
        $configContent .= "    \n";
        $configContent .= "    // FRP API 访问令牌\n";
        $configContent .= "    'api_token' => '" . addslashes($apiToken) . "',\n";
        $configContent .= "    \n";
        $configContent .= "    // 连接超时时间（秒）\n";
        $configContent .= "    'timeout' => " . $timeout . ",\n";
        $configContent .= "    \n";
        $configContent .= "    // 连接超时时间（秒）\n";
        $configContent .= "    'connect_timeout' => " . $connectTimeout . ",\n";
        $configContent .= "    \n";
        $configContent .= "    // 是否启用 SSL 验证\n";
        $configContent .= "    'ssl_verify' => " . $sslVerify . ",\n";
        $configContent .= "    \n";
        $configContent .= "    // 调试模式\n";
        $configContent .= "    'debug' => " . $debug . "\n";
        $configContent .= "];\n";
        $configContent .= "?>";
        
        // 写入配置文件
        if (file_put_contents($configFile, $configContent)) {
            $success = true;
            $isInstalled = true;
        } else {
            $errors[] = '无法写入配置文件，请检查文件权限';
        }
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FRP 管理功能安装向导</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"], input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="checkbox"] {
            margin-right: 8px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
        }
        .help-text {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .status-installed {
            color: #28a745;
            font-weight: bold;
        }
        .status-not-installed {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FRP 管理功能安装向导</h1>
        
        <?php if (isset($success) && $success): ?>
            <div class="alert alert-success">
                <strong>安装成功！</strong> FRP 管理功能已配置完成。
            </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <strong>配置失败：</strong>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="alert alert-info">
            <strong>安装状态：</strong>
            <?php if ($isInstalled): ?>
                <span class="status-installed">✓ 已安装</span>
            <?php else: ?>
                <span class="status-not-installed">✗ 未安装</span>
            <?php endif; ?>
        </div>
        
        <?php if ($isInstalled): ?>
            <h2>配置完成</h2>
            <p>FRP 管理功能已经配置完成，您现在可以：</p>
            <ul>
                <li><a href="test-frp-api.php" target="_blank">测试 API 连接</a></li>
                <li><a href="?page=panel&module=frpusers">进入 FRP 用户管理</a></li>
                <li>重新配置（填写下面的表单）</li>
            </ul>
            
            <h3>当前配置</h3>
            <?php
            include($configFile);
            ?>
            <ul>
                <li><strong>服务器地址：</strong> <?php echo htmlspecialchars($frp_config['server_url']); ?></li>
                <li><strong>API Token：</strong> <?php echo htmlspecialchars(substr($frp_config['api_token'], 0, 10) . '...'); ?></li>
                <li><strong>超时时间：</strong> <?php echo $frp_config['timeout']; ?> 秒</li>
            </ul>
        <?php endif; ?>
        
        <h2><?php echo $isInstalled ? '重新配置' : '初始配置'; ?></h2>
        
        <form method="post">
            <div class="form-group">
                <label for="server_url">FRP API 服务器地址 *</label>
                <input type="url" id="server_url" name="server_url" 
                       value="<?php echo isset($_POST['server_url']) ? htmlspecialchars($_POST['server_url']) : 'http://localhost:7300'; ?>" 
                       required>
                <div class="help-text">您的 frps-api 插件运行地址，例如：http://your-server.com:7300</div>
            </div>
            
            <div class="form-group">
                <label for="api_token">API Token *</label>
                <input type="text" id="api_token" name="api_token" 
                       value="<?php echo isset($_POST['api_token']) ? htmlspecialchars($_POST['api_token']) : 'frps-api-token-2024'; ?>" 
                       required>
                <div class="help-text">与您的 frps-api 插件中设置的 API_TOKEN 一致</div>
            </div>
            
            <div class="form-group">
                <label for="timeout">请求超时时间（秒）</label>
                <input type="number" id="timeout" name="timeout" 
                       value="<?php echo isset($_POST['timeout']) ? intval($_POST['timeout']) : 30; ?>" 
                       min="5" max="300">
                <div class="help-text">API 请求的超时时间，建议 30 秒</div>
            </div>
            
            <div class="form-group">
                <label for="connect_timeout">连接超时时间（秒）</label>
                <input type="number" id="connect_timeout" name="connect_timeout" 
                       value="<?php echo isset($_POST['connect_timeout']) ? intval($_POST['connect_timeout']) : 10; ?>" 
                       min="1" max="60">
                <div class="help-text">建立连接的超时时间，建议 10 秒</div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="ssl_verify" 
                           <?php echo (isset($_POST['ssl_verify']) || !isset($_POST['install'])) ? 'checked' : ''; ?>>
                    启用 SSL 证书验证
                </label>
                <div class="help-text">如果使用 HTTPS 且证书有效，请勾选此项</div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="debug" 
                           <?php echo isset($_POST['debug']) ? 'checked' : ''; ?>>
                    启用调试模式
                </label>
                <div class="help-text">开启后会记录更多日志信息，用于故障排除</div>
            </div>
            
            <button type="submit" name="install" class="btn <?php echo $isInstalled ? '' : 'btn-success'; ?>">
                <?php echo $isInstalled ? '更新配置' : '安装配置'; ?>
            </button>
        </form>
        
        <h2>安装说明</h2>
        <div class="alert alert-info">
            <p><strong>安装前请确保：</strong></p>
            <ol>
                <li>您的 FRP 服务器已安装并运行 frps-api 插件</li>
                <li>插件正在监听指定的地址和端口</li>
                <li>API Token 已正确设置</li>
                <li>网络连接正常，无防火墙阻拦</li>
            </ol>
            
            <p><strong>安装后：</strong></p>
            <ol>
                <li>使用 <a href="test-frp-api.php" target="_blank">API 连接测试</a> 验证配置</li>
                <li>以管理员身份登录面板</li>
                <li>在侧边栏找到 "FRP用户管理" 菜单</li>
                <li>开始管理您的 FRP 用户</li>
            </ol>
        </div>
    </div>
</body>
</html>
