{"version": 3, "sources": ["../ekko-lightbox.js"], "names": [], "mappings": ";;;;;;AAAA,IAAM,QAAQ,GAAG,CAAC,UAAC,CAAC,EAAK;;AAExB,KAAM,IAAI,GAAG,cAAc,CAAA;AAC3B,KAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;;AAErC,KAAM,OAAO,GAAG;AACf,OAAK,EAAE,EAAE;AACT,QAAM,EAAE,EAAE;AACV,UAAQ,EAAE,IAAI;AACd,WAAS,EAAE,IAAI;AACf,YAAU,EAAE,IAAI;AAChB,UAAQ,EAAE,IAAI;AACd,MAAI,EAAE,IAAI;AACV,iBAAe,EAAE,KAAK;AACtB,gBAAc,EAAE,2EAA2E;AAC3F,WAAS,EAAE,uBAAuB;AAClC,YAAU,EAAE,uBAAuB;AACnC,SAAO,EAAE;AACR,QAAK,EAAE,OAAO;AACd,OAAI,EAAE,uBAAuB;AAC7B,OAAI,EAAE,qEAAqE;GAC3E;AACD,KAAG,EAAE,QAAQ;AACb,QAAM,EAAA,kBAAG,EAAE;AACX,SAAO,EAAA,mBAAG,EAAE;AACZ,QAAM,EAAA,kBAAG,EAAE;AACX,UAAQ,EAAA,oBAAG,EAAE;AACb,YAAU,EAAA,sBAAG,EAAE;AACf,iBAAe,EAAA,2BAAG,EAAE;EACpB,CAAA;;KAEK,QAAQ;eAAR,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;QA4BK,eAAG;AACpB,WAAO,OAAO,CAAA;IACd;;;AAEU,WAhCN,QAAQ,CAgCD,QAAQ,EAAE,MAAM,EAAE;;;yBAhCzB,QAAQ;;AAiCZ,OAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;AAC5C,OAAI,CAAC,aAAa,GAAG,IAAI,CAAA;AACzB,OAAI,CAAC,aAAa,GAAG,CAAC,CAAA;AACtB,OAAI,CAAC,YAAY,GAAG,IAAI,CAAA;AACxB,OAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;AACpB,OAAI,CAAC,OAAO,GAAG,IAAI,CAAA;AACnB,OAAI,CAAC,aAAa,GAAG,KAAK,CAAA;AAC1B,OAAI,CAAC,cAAc,GAAG,KAAK,CAAA;AAC3B,OAAI,CAAC,YAAY,GAAG,CAAC,CAAA;AACrB,OAAI,CAAC,aAAa,GAAG,CAAC,CAAA;AACtB,OAAI,CAAC,YAAY,GAAG,CAAC,CAAA;AACrB,OAAI,CAAC,UAAU,GAAG,CAAC,CAAA;;AAEnB,OAAI,CAAC,QAAQ,qBAAmB,IAAI,CAAC,KAAK,CAAC,AAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAI,CAAC,CAAC,AAAE,CAAC;AACzE,OAAI,CAAC,SAAS,GAAG,QAAQ,YAAY,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;;AAEpE,OAAI,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;AAE5D,OAAI,EAAE,iCAA8B,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAA,UAAO,CAAC;AAC1E,OAAI,GAAG,6EAA2E,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,uDAAoD,CAAC;;AAEjK,OAAI,MAAM,GAAG,8BAA2B,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,EAAE,GAAG,OAAO,CAAA,WAAM,IAAI,CAAC,aAAa,GAAG,GAAG,GAAC,EAAE,GAAG,EAAE,GAAC,GAAG,CAAA,AAAC,WAAS,CAAC;AAC9J,OAAI,MAAM,iCAA8B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAA,WAAK,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAA,WAAQ,CAAC;AACvH,OAAI,IAAI,GAAG,yKAAyK,CAAA;AACpL,OAAI,MAAM,6EAA2E,MAAM,GAAG,IAAI,GAAG,MAAM,iBAAc,CAAA;AACzH,IAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,eAAa,IAAI,CAAC,QAAQ,wGAAmG,MAAM,YAAS,CAAA;;AAE3K,OAAI,CAAC,OAAO,GAAG,CAAC,OAAK,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AACvD,OAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAA;AAC/D,OAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAA;AACjE,OAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,CAAA;AAC3D,OAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAA;AAC/D,OAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAA;;AAE/D,OAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,KAAK,EAAE,CAAA;AACpF,OAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,EAAE,CAAA;AACnF,OAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,EAAE,CAAA;;AAElF,OAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;AACvC,OAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;;AAExC,OAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AAClD,OAAI,IAAI,CAAC,YAAY,EAAE;AACtB,QAAI,CAAC,cAAc,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,sBAAoB,IAAI,CAAC,YAAY,QAAK,CAAA;AACrF,QAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AAC9D,KAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;;;AAG3E,QAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9D,SAAI,CAAC,mBAAmB,CAAC,MAAM,yDAAuD,IAAI,CAAC,OAAO,CAAC,SAAS,wBAAmB,IAAI,CAAC,OAAO,CAAC,UAAU,gBAAa,CAAA;AACnK,SAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,CAAA;AAC3F,SAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,UAAA,KAAK,EAAI;AAC9D,WAAK,CAAC,cAAc,EAAE,CAAA;AACtB,aAAO,MAAK,YAAY,EAAE,CAAA;MAC1B,CAAC,CAAA;AACF,SAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,UAAA,KAAK,EAAI;AAC7D,WAAK,CAAC,cAAc,EAAE,CAAA;AACtB,aAAO,MAAK,aAAa,EAAE,CAAA;MAC3B,CAAC,CAAA;AACF,SAAI,CAAC,gBAAgB,EAAE,CAAA;KACvB;IACD;;AAED,OAAI,CAAC,OAAO,CACX,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACnD,EAAE,CAAC,gBAAgB,EAAE,YAAM;AAC3B,UAAK,cAAc,CAAC,IAAI,CAAC,CAAA;AACzB,UAAK,OAAO,EAAE,CAAA;AACd,WAAO,MAAK,OAAO,CAAC,OAAO,CAAC,IAAI,OAAM,CAAA;IACtC,CAAC,CACD,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACnD,EAAE,CAAC,iBAAiB,EAAE,YAAM;AAC5B,QAAI,MAAK,YAAY,EAAE;AACtB,MAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;AACvC,MAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;KACpC;AACD,UAAK,OAAO,CAAC,MAAM,EAAE,CAAA;AACrB,WAAO,MAAK,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAM,CAAA;IACvC,CAAC,CACD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;;AAEpB,IAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,qBAAqB,EAAE,YAAM;AACzC,UAAK,OAAO,CAAC,MAAK,YAAY,EAAE,MAAK,aAAa,CAAC,CAAA;IACnD,CAAC,CAAA;AACF,OAAI,CAAC,mBAAmB,CACvB,EAAE,CAAC,YAAY,EAAE,YAAM;AACvB,UAAK,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAEpD,CAAC,CACD,EAAE,CAAC,UAAU,EAAE,YAAM;AACrB,UAAK,UAAU,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAC/C,UAAK,YAAY,EAAE,CAAC;IACvB,CAAC,CAAA;GACF;;eA9HI,QAAQ;;UAgIN,mBAAG;AACT,WAAO,IAAI,CAAC,SAAS,CAAC;IACtB;;;UAEI,iBAAG;AACP,WAAO,IAAI,CAAC,OAAO,CAAC;IACpB;;;UAES,oBAAC,KAAK,EAAE;;AAEjB,QAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAC,CAAC,EACpD,OAAO,IAAI,CAAA;;AAEZ,QAAI,CAAC,aAAa,GAAG,KAAK,CAAA;;AAE1B,QAAI,CAAC,gBAAgB,EAAE,CAAA;;AAEvB,QAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;AAC/D,QAAI,CAAC,OAAO,EAAE,CAAC;IACf;;;UAEW,wBAAG;;AAEd,QAAG,CAAC,IAAI,CAAC,cAAc,EACtB,OAAO;;AAER,QAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EACnC,OAAM;;AAEP,QAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;AAC7B,SAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA,KAEnD,OAAM;KACP;AAEA,SAAI,CAAC,aAAa,EAAE,CAAA;;AAErB,QAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;AAC9D,WAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IAC1C;;;UAEY,yBAAG;;AAEf,QAAG,CAAC,IAAI,CAAC,cAAc,EACtB,OAAO;;AAER,QAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EACnC,OAAM;;AAEP,QAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1D,SAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACxB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA,KAEtB,OAAM;KACP;AAEA,SAAI,CAAC,aAAa,EAAE,CAAA;;AAErB,QAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;AAC/D,WAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IAC1C;;;UAEe,4BAAG;AAClB,QAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AAC3B,SAAI,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAA;AACzE,SAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAC3B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA,KAE/C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;;AAEnD,SAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA,KAE9C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;KAClD;IACD;;;UAEI,iBAAG;AACP,WAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAClC;;;;;UAGkB,6BAAC,KAAK,EAAE;AAC1B,SAAK,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;AAC9B,QAAI,KAAK,CAAC,OAAO,KAAK,EAAE,EACvB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;AAC5B,QAAI,KAAK,CAAC,OAAO,KAAK,EAAE,EACvB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAA;IAC3B;;;;;UAGgB,2BAAC,GAAG,EAAE,IAAI,EAAE;;AAE5B,QAAI,GAAG,IAAI,IAAI,KAAK,CAAC;;AAErB,QAAG,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC7B,IAAI,GAAG,OAAO,CAAC;AAChB,QAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAClC,IAAI,GAAG,SAAS,CAAC;AAClB,QAAG,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAChC,IAAI,GAAG,OAAO,CAAC;AAChB,QAAG,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EACpC,IAAI,GAAG,WAAW,CAAC;;AAEpB,QAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EACvF,IAAI,GAAG,KAAK,CAAC;;AAEd,WAAO,IAAI,CAAC;IACZ;;;UAEO,kBAAC,MAAM,EAAE;AAChB,WAAO,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAA;IACtG;;;UAEc,2BAAG;;;;AAEjB,QAAI,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAA;AACnC,QAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAA;;AAErC,QAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzC,WAAM,GAAG,IAAI,CAAC,iBAAiB,CAAA;AAC/B,aAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAA;KACjC;;AAED,YAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;AAC/B,cAAU,CAAC,YAAM;AAChB,SAAG,CAAC,OAAK,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EACxC,OAAK,iBAAiB,CAAC,KAAK,EAAE,CAAA;AAC/B,SAAG,CAAC,OAAK,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EACxC,OAAK,iBAAiB,CAAC,KAAK,EAAE,CAAA;KAC/B,EAAE,GAAG,CAAC,CAAA;;AAEP,UAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;AAC1B,WAAO,MAAM,CAAA;IACb;;;UAEM,mBAAG;;AAET,QAAI,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;AACnC,QAAI,CAAC,qBAAqB,EAAE,CAAA;;AAE5B,QAAI,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;AACrF,QAAI,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAA;;AAElG,QAAG,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EACrF,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;;AAE9C,YAAO,WAAW;AACjB,UAAK,OAAO;AACX,UAAI,CAAC,aAAa,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;AACzC,UAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;AAChD,YAAM;AAAA,AACP,UAAK,SAAS;AACb,UAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC9C,YAAM;AAAA,AACP,UAAK,OAAO;AACX,UAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9D,YAAM;AAAA,AACP,UAAK,WAAW;AACf,UAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,CAAC;AACtE,YAAM;AAAA,AACP,UAAK,OAAO;AACX,UAAI,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC5C,YAAM;AAAA,AACP;;AACC,UAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC/C,YAAM;AAAA,KACP;;AAED,WAAO,IAAI,CAAC;IACZ;;;UAEY,uBAAC,MAAM,EAAE;AACrB,QAAG,CAAC,MAAM,EACT,OAAO,KAAK,CAAC;AACd,QAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAA;AAC7F,WAAO,AAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,GAAI,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;IACjE;;;UAEU,qBAAC,MAAM,EAAE;AACnB,WAAO,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,CAAA;IAC7D;;;UAEc,yBAAC,MAAM,EAAE;AACvB,WAAO,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,CAAA;IACjE;;;;;UAGa,wBAAC,IAAI,EAAE;AACpB,QAAI,GAAG,IAAI,IAAI,KAAK,CAAA;AACpB,QAAG,IAAI,EAAE;AACR,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;AACzC,SAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;AACnC,MAAC,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;KACxD,MACI;AACJ,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;AAC1C,SAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;AAChC,MAAC,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,MAAM,EAAE,CAAA;KAC3D;AACD,WAAO,IAAI,CAAC;IACZ;;;UAEgB,6BAAG;AACnB,WAAO;AACN,QAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;AAClD,UAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;AACtD,WAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC;AACxD,SAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC;KACpD,CAAA;IACD;;;UAEgB,6BAAG;AACnB,WAAO;AACN,QAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;AAC7C,UAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC;AACjD,WAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC;AACnD,SAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC;KAC/C,CAAA;IACD;;;UAEmB,8BAAC,SAAS,EAAE;AAC/B,WAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,GACrD,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,GAChD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAA;IAC9C;;;UAEoB,iCAAG;AACvB,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;AAC9C,QAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;;AAEjD,QAAI,CAAC,aAAa,GAAG,KAAK,CAAA;AAC1B,QAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;AAC1C,SAAI,CAAC,aAAa,GAAG,IAAI,CAAA;AACzB,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,CAAA;KAClF,MAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;;AAE1C,QAAI,CAAC,cAAc,GAAG,KAAK,CAAA;AAC3B,QAAI,OAAO,EAAE;AACZ,SAAI,CAAC,cAAc,GAAG,IAAI,CAAA;AAC1B,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;KACnD,MAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;;AAE1C,WAAO,IAAI,CAAC;IACZ;;;UAEgB,2BAAC,MAAM,EAAE,oBAAoB,EAAE;AAC/C,QAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;AACnC,QAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAA;AAC7E,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;AAC/C,QAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,KAAK,IAAK,GAAG,GAAC,GAAG,CAAA,AAAE,CAAA;AAClE,WAAO,IAAI,CAAC,gBAAgB,8BACA,EAAE,mCAA8B,KAAK,EAChE,KAAK,EACL,MAAM,EACN,oBAAoB,CACpB,CAAC;IACF;;;UAEc,yBAAC,EAAE,EAAE,oBAAoB,EAAE;AACzC,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;AAC/C,QAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,KAAK,IAAK,GAAG,GAAC,GAAG,CAAA,AAAE,CAAA;AAClE,WAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,GAAG,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAA;IACrF;;;UAEkB,6BAAC,EAAE,EAAE,oBAAoB,EAAE;;AAE7C,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;AAC/C,QAAI,MAAM,GAAG,KAAK,GAAG,EAAE,CAAC;AACxB,MAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,wBAAoB,CAAC,IAAI,qBAAmB,KAAK,kBAAa,MAAM,eAAU,EAAE,uDAAoD,CAAC;AACrI,QAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,QAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,QAAI,IAAI,CAAC,aAAa;AACrB,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,WAAO,IAAI,CAAC;IACZ;;;UAEe,0BAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,oBAAoB,EAAE;;AAC1D,UAAM,GAAG,MAAM,IAAI,KAAK,CAAC;AACzB,wBAAoB,CAAC,IAAI,0EAAwE,KAAK,kBAAa,MAAM,eAAU,GAAG,qFAAkF,CAAC;AACzN,QAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,QAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,QAAI,IAAI,CAAC,aAAa,EACrB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,WAAO,IAAI,CAAC;IACZ;;;UAEc,yBAAC,GAAG,EAAE,oBAAoB,EAAE;;AAC1C,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;AAC/C,QAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,KAAK,IAAK,GAAG,GAAC,GAAG,CAAA,AAAE,CAAA;AAClE,wBAAoB,CAAC,IAAI,yEAAuE,KAAK,kBAAa,MAAM,eAAU,GAAG,qFAAkF,CAAC;AACxN,QAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,QAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,QAAI,IAAI,CAAC,aAAa,EACrB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,WAAO,IAAI,CAAC;IACZ;;;UAEiB,4BAAC,GAAG,EAAE,oBAAoB,EAAE;;;AAC7C,QAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;AAChD,QAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;;AAElD,QAAI,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC;AAChF,QAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;;;;AAI3B,QAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;AACpD,yBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,YAAM;AAC5C,aAAO,OAAK,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;MAClD,CAAC,CAAC,CAAC;KAEJ,MAAM;AACN,yBAAoB,CAAC,IAAI,mBAAiB,GAAG,iDAA8C,CAAC;AAC5F,SAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxC;;AAED,QAAI,IAAI,CAAC,aAAa;AACrB,SAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;;AAE1C,QAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,WAAO,IAAI,CAAC;IACZ;;;UAEU,qBAAC,GAAG,EAAE;AAChB,QAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;AACpF,QAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,QAAQ,EACtG,OAAO,IAAI,CAAC;;AAEb,QAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,MAAM,QAAM,CAAA;AAC1F,YAAO,EAAE,EAAE;AACX,aAAQ,EAAE,GAAG;MACb,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAM,EAAE,EAAE,CAAC,KAAK,QAAQ,CAAC,IAAI,EACjD,OAAO,IAAI,CAAC;;AAEb,WAAO,KAAK,CAAC;IACb;;;UAEK,gBAAE,OAAO,EAAG;AACjB,WAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvB,QAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,QAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACvB,WAAO,IAAI,CAAC;IACZ;;;UAEmB,8BAAC,UAAU,EAAE,aAAa,EAAE;;AAE/C,QAAG,CAAC,IAAI,CAAC,cAAc,EACtB,OAAO;;AAER,QAAI,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAA;AACxD,QAAG,OAAO,IAAI,IAAI,WAAW,EAC5B,OAAM;;AAEP,QAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;AACvD,QAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC3D,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;;AAE/B,QAAG,aAAa,GAAG,CAAC,EACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,GAAG,CAAC,EAAE,aAAa,GAAC,CAAC,CAAC,CAAC;IACnE;;;UAEY,uBAAE,GAAG,EAAE,kBAAkB,EAAE;;;AAEvC,sBAAkB,GAAG,kBAAkB,IAAI,KAAK,CAAA;;AAEhD,QAAI,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;AACtB,QAAI,kBAAkB,EAAE;;;;AAGvB,UAAI,cAAc,GAAG,UAAU,CAAC,YAAM;AACrC,yBAAkB,CAAC,MAAM,CAAC,OAAK,OAAO,CAAC,cAAc,CAAC,CAAA;OACtD,EAAE,GAAG,CAAC,CAAA;;AAEP,SAAG,CAAC,MAAM,GAAG,YAAM;AAClB,WAAG,cAAc,EAChB,YAAY,CAAC,cAAc,CAAC,CAAA;AAC7B,qBAAc,GAAG,IAAI,CAAC;AACtB,WAAI,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;AACzB,YAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,YAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;;;AAG5B,YAAK,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;;AAE3B,yBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,WAAI,OAAK,aAAa,EACrB,OAAK,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;;AAEtC,cAAK,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACpC,cAAK,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,cAAO,OAAK,OAAO,CAAC,eAAe,CAAC,IAAI,QAAM,CAAC;OAC/C,CAAC;AACF,SAAG,CAAC,OAAO,GAAG,YAAM;AACnB,cAAK,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,cAAO,OAAK,MAAM,CAAC,OAAK,OAAO,CAAC,OAAO,CAAC,IAAI,WAAM,GAAG,CAAE,CAAC,CAAC;OACzD,CAAC;;KACF;;AAED,OAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACd,WAAO,GAAG,CAAC;IACX;;;UAEW,wBAAG;AACX,QAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;AACrC,YAAO,IAAI,CAAC,aAAa,EAAE,CAAC;KAC/B;AACD,QAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;AACrC,YAAO,IAAI,CAAC,YAAY,EAAE,CAAC;KAC9B;IACJ;;;UAEM,iBAAE,KAAK,EAAE,MAAM,EAAG;;AAExB,UAAM,GAAG,MAAM,IAAI,KAAK,CAAA;AACxB,QAAI,CAAC,YAAY,GAAG,KAAK,CAAA;AACzB,QAAI,CAAC,aAAa,GAAG,MAAM,CAAA;;AAE3B,QAAI,eAAe,GAAG,KAAK,GAAG,MAAM,CAAC;;;AAGrC,QAAI,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;;;AAG7G,QAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;AAChE,QAAI,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAA;;AAErE,QAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;;AAE5H,QAAG,AAAC,KAAK,GAAG,qBAAqB,GAAI,QAAQ,EAAE;AAC9C,WAAM,GAAG,CAAC,QAAQ,GAAG,qBAAqB,GAAG,cAAc,CAAA,GAAI,eAAe,CAAC;AAC/E,UAAK,GAAG,QAAQ,CAAA;KAChB,MACA,KAAK,GAAI,KAAK,GAAG,qBAAqB,AAAC,CAAA;;AAExC,QAAI,YAAY,GAAG,CAAC;QAChB,YAAY,GAAG,CAAC,CAAA;;;;AAIpB,QAAI,IAAI,CAAC,cAAc,EACtB,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;;AAE1D,QAAI,IAAI,CAAC,aAAa,EACrB,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;;AAE1D,QAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;;;AAGrG,QAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;;AAErH,QAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,aAAa,GAAG,OAAO,GAAG,YAAY,GAAG,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC;;AAEnL,QAAG,MAAM,GAAG,SAAS,EAAE;;AAEtB,UAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,qBAAqB,CAAC;KACvE;;AAED,QAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;AACjD,QAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;;AAEzD,QAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,QAAI,KAAK,EAAE;;AAEV,SAAI;AACH,WAAK,CAAC,aAAa,EAAE,CAAC;MACtB,CAAC,OAAM,SAAS,EAAE;AAClB,WAAK,CAAC,YAAY,EAAE,CAAC;MACrB;KACD;AACD,WAAO,IAAI,CAAC;IACZ;;;UAEsB,0BAAC,MAAM,EAAE;;;AAC/B,UAAM,GAAG,MAAM,IAAI,EAAE,CAAA;AACrB,WAAO,IAAI,CAAC,IAAI,CAAC,YAAM;AACtB,SAAI,KAAK,GAAG,CAAC,QAAM,CAAA;AACnB,SAAI,OAAO,GAAG,CAAC,CAAC,MAAM,CACrB,EAAE,EACF,QAAQ,CAAC,OAAO,EAChB,KAAK,CAAC,IAAI,EAAE,EACZ,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CACpC,CAAA;;AAED,SAAI,QAAQ,SAAO,OAAO,CAAC,CAAA;KAC3B,CAAC,CAAA;IACF;;;SA/mBI,QAAQ;;;AAonBd,EAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAe,QAAQ,CAAC,gBAAgB,CAAA;AAClD,EAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,QAAQ,CAAA;AACjC,EAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,GAAI,YAAM;AAC9B,GAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAA;AAC/B,SAAO,QAAQ,CAAC,gBAAgB,CAAA;EAChC,CAAA;;AAED,QAAO,QAAQ,CAAA;CAEf,CAAA,CAAE,MAAM,CAAC,CAAA", "file": "ekko-lightbox.js", "sourcesContent": ["const Lightbox = (($) => {\n\n\tconst NAME = 'ekkoLightbox'\n\tconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\n\tconst Default = {\n\t\ttitle: '',\n\t\tfooter: '',\n\t\tmaxWidth: 9999,\n\t\tmaxHeight: 9999,\n\t\tshowArrows: true, //display the left / right arrows or not\n\t\twrapping: true, //if true, gallery loops infinitely\n\t\ttype: null, //force the lightbox into image / youtube mode. if null, or not image|youtube|vimeo; detect it\n\t\talwaysShowClose: false, //always show the close button, even if there is no title\n\t\tloadingMessage: '<div class=\"ekko-lightbox-loader\"><div><div></div><div></div></div></div>', // http://tobiasahlin.com/spinkit/\n\t\tleftArrow: '<span>&#10094;</span>',\n\t\trightArrow: '<span>&#10095;</span>',\n\t\tstrings: {\n\t\t\tclose: 'Close',\n\t\t\tfail: 'Failed to load image:',\n\t\t\ttype: 'Could not detect remote target type. Force the type using data-type',\n\t\t},\n\t\tdoc: document, // if in an iframe can specify top.document\n\t\tonShow() {},\n\t\tonShown() {},\n\t\tonHide() {},\n\t\tonHidden() {},\n\t\tonNavigate() {},\n\t\tonContentLoaded() {}\n\t}\n\n\tclass Lightbox {\n\n\t\t/**\n\n\t    Class properties:\n\n\t\t _$element: null -> the <a> element currently being displayed\n\t\t _$modal: The bootstrap modal generated\n\t\t    _$modalDialog: The .modal-dialog\n\t\t    _$modalContent: The .modal-content\n\t\t    _$modalBody: The .modal-body\n\t\t    _$modalHeader: The .modal-header\n\t\t    _$modalFooter: The .modal-footer\n\t\t _$lightboxContainerOne: Container of the first lightbox element\n\t\t _$lightboxContainerTwo: Container of the second lightbox element\n\t\t _$lightboxBody: First element in the container\n\t\t _$modalArrows: The overlayed arrows container\n\n\t\t _$galleryItems: Other <a>'s available for this gallery\n\t\t _galleryName: Name of the current data('gallery') showing\n\t\t _galleryIndex: The current index of the _$galleryItems being shown\n\n\t\t _config: {} the options for the modal\n\t\t _modalId: unique id for the current lightbox\n\t\t _padding / _border: CSS properties for the modal container; these are used to calculate the available space for the content\n\n\t\t */\n\n\t\tstatic get Default() {\n\t\t\treturn Default\n\t\t}\n\n\t\tconstructor($element, config) {\n\t\t\tthis._config = $.extend({}, Default, config)\n\t\t\tthis._$modalArrows = null\n\t\t\tthis._galleryIndex = 0\n\t\t\tthis._galleryName = null\n\t\t\tthis._padding = null\n\t\t\tthis._border = null\n\t\t\tthis._titleIsShown = false\n\t\t\tthis._footerIsShown = false\n\t\t\tthis._wantedWidth = 0\n\t\t\tthis._wantedHeight = 0\n\t\t\tthis._touchstartX = 0\n\t\t\tthis._touchendX = 0\n\n\t\t\tthis._modalId = `ekkoLightbox-${Math.floor((Math.random() * 1000) + 1)}`;\n\t\t\tthis._$element = $element instanceof jQuery ? $element : $($element)\n\n\t\t\tthis._isBootstrap3 = $.fn.modal.Constructor.VERSION[0] == 3;\n\n\t\t\tlet h4 = `<h4 class=\"modal-title\">${this._config.title || \"&nbsp;\"}</h4>`;\n\t\t\tlet btn = `<button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"${this._config.strings.close}\"><span aria-hidden=\"true\">&times;</span></button>`;\n\n\t\t\tlet header = `<div class=\"modal-header${this._config.title || this._config.alwaysShowClose ? '' : ' hide'}\">`+(this._isBootstrap3 ? btn+h4 : h4+btn)+`</div>`;\n\t\t\tlet footer = `<div class=\"modal-footer${this._config.footer ? '' : ' hide'}\">${this._config.footer || \"&nbsp;\"}</div>`;\n\t\t\tlet body = '<div class=\"modal-body\"><div class=\"ekko-lightbox-container\"><div class=\"ekko-lightbox-item fade in show\"></div><div class=\"ekko-lightbox-item fade\"></div></div></div>'\n\t\t\tlet dialog = `<div class=\"modal-dialog\" role=\"document\"><div class=\"modal-content\">${header}${body}${footer}</div></div>`\n\t\t\t$(this._config.doc.body).append(`<div id=\"${this._modalId}\" class=\"ekko-lightbox modal fade\" tabindex=\"-1\" tabindex=\"-1\" role=\"dialog\" aria-hidden=\"true\">${dialog}</div>`)\n\n\t\t\tthis._$modal = $(`#${this._modalId}`, this._config.doc)\n\t\t\tthis._$modalDialog = this._$modal.find('.modal-dialog').first()\n\t\t\tthis._$modalContent = this._$modal.find('.modal-content').first()\n\t\t\tthis._$modalBody = this._$modal.find('.modal-body').first()\n\t\t\tthis._$modalHeader = this._$modal.find('.modal-header').first()\n\t\t\tthis._$modalFooter = this._$modal.find('.modal-footer').first()\n\n\t\t\tthis._$lightboxContainer = this._$modalBody.find('.ekko-lightbox-container').first()\n\t\t\tthis._$lightboxBodyOne = this._$lightboxContainer.find('> div:first-child').first()\n\t\t\tthis._$lightboxBodyTwo = this._$lightboxContainer.find('> div:last-child').first()\n\n\t\t\tthis._border = this._calculateBorders()\n\t\t\tthis._padding = this._calculatePadding()\n\n\t\t\tthis._galleryName = this._$element.data('gallery')\n\t\t\tif (this._galleryName) {\n\t\t\t\tthis._$galleryItems = $(document.body).find(`*[data-gallery=\"${this._galleryName}\"]`)\n\t\t\t\tthis._galleryIndex = this._$galleryItems.index(this._$element)\n\t\t\t\t$(document).on('keydown.ekkoLightbox', this._navigationalBinder.bind(this))\n\n\t\t\t\t// add the directional arrows to the modal\n\t\t\t\tif (this._config.showArrows && this._$galleryItems.length > 1) {\n\t\t\t\t\tthis._$lightboxContainer.append(`<div class=\"ekko-lightbox-nav-overlay\"><a href=\"#\">${this._config.leftArrow}</a><a href=\"#\">${this._config.rightArrow}</a></div>`)\n\t\t\t\t\tthis._$modalArrows = this._$lightboxContainer.find('div.ekko-lightbox-nav-overlay').first()\n\t\t\t\t\tthis._$lightboxContainer.on('click', 'a:first-child', event => {\n\t\t\t\t\t\tevent.preventDefault()\n\t\t\t\t\t\treturn this.navigateLeft()\n\t\t\t\t\t})\n\t\t\t\t\tthis._$lightboxContainer.on('click', 'a:last-child', event => {\n\t\t\t\t\t\tevent.preventDefault()\n\t\t\t\t\t\treturn this.navigateRight()\n\t\t\t\t\t})\n\t\t\t\t\tthis.updateNavigation()\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._$modal\n\t\t\t.on('show.bs.modal', this._config.onShow.bind(this))\n\t\t\t.on('shown.bs.modal', () => {\n\t\t\t\tthis._toggleLoading(true)\n\t\t\t\tthis._handle()\n\t\t\t\treturn this._config.onShown.call(this)\n\t\t\t})\n\t\t\t.on('hide.bs.modal', this._config.onHide.bind(this))\n\t\t\t.on('hidden.bs.modal', () => {\n\t\t\t\tif (this._galleryName) {\n\t\t\t\t\t$(document).off('keydown.ekkoLightbox')\n\t\t\t\t\t$(window).off('resize.ekkoLightbox')\n\t\t\t\t}\n\t\t\t\tthis._$modal.remove()\n\t\t\t\treturn this._config.onHidden.call(this)\n\t\t\t})\n\t\t\t.modal(this._config)\n\n\t\t\t$(window).on('resize.ekkoLightbox', () => {\n\t\t\t\tthis._resize(this._wantedWidth, this._wantedHeight)\n\t\t\t})\n\t\t\tthis._$lightboxContainer\n\t\t\t.on('touchstart', () => {\n\t\t\t\tthis._touchstartX = event.changedTouches[0].screenX;\n\n\t\t\t})\n\t\t\t.on('touchend', () => {\n\t\t\t\tthis._touchendX = event.changedTouches[0].screenX;\n\t\t\t    this._swipeGesure();\n\t\t\t})\n\t\t}\n\n\t\telement() {\n\t\t\treturn this._$element;\n\t\t}\n\n\t\tmodal() {\n\t\t\treturn this._$modal;\n\t\t}\n\n\t\tnavigateTo(index) {\n\n\t\t\tif (index < 0 || index > this._$galleryItems.length-1)\n\t\t\t\treturn this\n\n\t\t\tthis._galleryIndex = index\n\n\t\t\tthis.updateNavigation()\n\n\t\t\tthis._$element = $(this._$galleryItems.get(this._galleryIndex))\n\t\t\tthis._handle();\n\t\t}\n\n\t\tnavigateLeft() {\n\n\t\t\tif(!this._$galleryItems)\n\t\t\t\treturn;\n\n\t\t\tif (this._$galleryItems.length === 1)\n\t\t\t\treturn\n\n\t\t\tif (this._galleryIndex === 0) {\n\t\t\t\tif (this._config.wrapping)\n\t\t\t\t\tthis._galleryIndex = this._$galleryItems.length - 1\n\t\t\t\telse\n\t\t\t\t\treturn\n\t\t\t}\n\t\t\telse //circular\n\t\t\t\tthis._galleryIndex--\n\n\t\t\tthis._config.onNavigate.call(this, 'left', this._galleryIndex)\n\t\t\treturn this.navigateTo(this._galleryIndex)\n\t\t}\n\n\t\tnavigateRight() {\n\n\t\t\tif(!this._$galleryItems)\n\t\t\t\treturn;\n\n\t\t\tif (this._$galleryItems.length === 1)\n\t\t\t\treturn\n\n\t\t\tif (this._galleryIndex === this._$galleryItems.length - 1) {\n\t\t\t\tif (this._config.wrapping)\n\t\t\t\t\tthis._galleryIndex = 0\n\t\t\t\telse\n\t\t\t\t\treturn\n\t\t\t}\n\t\t\telse //circular\n\t\t\t\tthis._galleryIndex++\n\n\t\t\tthis._config.onNavigate.call(this, 'right', this._galleryIndex)\n\t\t\treturn this.navigateTo(this._galleryIndex)\n\t\t}\n\n\t\tupdateNavigation() {\n\t\t\tif (!this._config.wrapping) {\n\t\t\t\tlet $nav = this._$lightboxContainer.find('div.ekko-lightbox-nav-overlay')\n\t\t\t\tif (this._galleryIndex === 0)\n\t\t\t\t\t$nav.find('a:first-child').addClass('disabled')\n\t\t\t\telse\n\t\t\t\t\t$nav.find('a:first-child').removeClass('disabled')\n\n\t\t\t\tif (this._galleryIndex === this._$galleryItems.length - 1)\n\t\t\t\t\t$nav.find('a:last-child').addClass('disabled')\n\t\t\t\telse\n\t\t\t\t\t$nav.find('a:last-child').removeClass('disabled')\n\t\t\t}\n\t\t}\n\n\t\tclose() {\n\t\t\treturn this._$modal.modal('hide');\n\t\t}\n\n\t\t// helper private methods\n\t\t_navigationalBinder(event) {\n\t\t\tevent = event || window.event;\n\t\t\tif (event.keyCode === 39)\n\t\t\t\treturn this.navigateRight()\n\t\t\tif (event.keyCode === 37)\n\t\t\t\treturn this.navigateLeft()\n\t\t}\n\n\t\t// type detection private methods\n\t\t_detectRemoteType(src, type) {\n\n\t\t\ttype = type || false;\n\n\t\t\tif(!type && this._isImage(src))\n\t\t\t\ttype = 'image';\n\t\t\tif(!type && this._getYoutubeId(src))\n\t\t\t\ttype = 'youtube';\n\t\t\tif(!type && this._getVimeoId(src))\n\t\t\t\ttype = 'vimeo';\n\t\t\tif(!type && this._getInstagramId(src))\n\t\t\t\ttype = 'instagram';\n\n\t\t\tif(!type || ['image', 'youtube', 'vimeo', 'instagram', 'video', 'url'].indexOf(type) < 0)\n\t\t\t\ttype = 'url';\n\n\t\t\treturn type;\n\t\t}\n\n\t\t_isImage(string) {\n\t\t\treturn string && string.match(/(^data:image\\/.*,)|(\\.(jp(e|g|eg)|gif|png|bmp|webp|svg)((\\?|#).*)?$)/i)\n\t\t}\n\n\t\t_containerToUse() {\n\t\t\t// if currently showing an image, fade it out and remove\n\t\t\tlet $toUse = this._$lightboxBodyTwo\n\t\t\tlet $current = this._$lightboxBodyOne\n\n\t\t\tif(this._$lightboxBodyTwo.hasClass('in')) {\n\t\t\t\t$toUse = this._$lightboxBodyOne\n\t\t\t\t$current = this._$lightboxBodyTwo\n\t\t\t}\n\n\t\t\t$current.removeClass('in show')\n\t\t\tsetTimeout(() => {\n\t\t\t\tif(!this._$lightboxBodyTwo.hasClass('in'))\n\t\t\t\t\tthis._$lightboxBodyTwo.empty()\n\t\t\t\tif(!this._$lightboxBodyOne.hasClass('in'))\n\t\t\t\t\tthis._$lightboxBodyOne.empty()\n\t\t\t}, 500)\n\n\t\t\t$toUse.addClass('in show')\n\t\t\treturn $toUse\n\t\t}\n\n\t\t_handle() {\n\n\t\t\tlet $toUse = this._containerToUse()\n\t\t\tthis._updateTitleAndFooter()\n\n\t\t\tlet currentRemote = this._$element.attr('data-remote') || this._$element.attr('href')\n\t\t\tlet currentType = this._detectRemoteType(currentRemote, this._$element.attr('data-type') || false)\n\n\t\t\tif(['image', 'youtube', 'vimeo', 'instagram', 'video', 'url'].indexOf(currentType) < 0)\n\t\t\t\treturn this._error(this._config.strings.type)\n\n\t\t\tswitch(currentType) {\n\t\t\t\tcase 'image':\n\t\t\t\t\tthis._preloadImage(currentRemote, $toUse)\n\t\t\t\t\tthis._preloadImageByIndex(this._galleryIndex, 3)\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'youtube':\n\t\t\t\t\tthis._showYoutubeVideo(currentRemote, $toUse);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'vimeo':\n\t\t\t\t\tthis._showVimeoVideo(this._getVimeoId(currentRemote), $toUse);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'instagram':\n\t\t\t\t\tthis._showInstagramVideo(this._getInstagramId(currentRemote), $toUse);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'video':\n\t\t\t\t\tthis._showHtml5Video(currentRemote, $toUse);\n\t\t\t\t\tbreak;\n\t\t\t\tdefault: // url\n\t\t\t\t\tthis._loadRemoteContent(currentRemote, $toUse);\n\t\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\treturn this;\n\t\t}\n\n\t\t_getYoutubeId(string) {\n\t\t\tif(!string)\n\t\t\t\treturn false;\n\t\t\tlet matches = string.match(/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/)\n\t\t\treturn (matches && matches[2].length === 11) ? matches[2] : false\n\t\t}\n\n\t\t_getVimeoId(string) {\n\t\t\treturn string && string.indexOf('vimeo') > 0 ? string : false\n\t\t}\n\n\t\t_getInstagramId(string) {\n\t\t\treturn string && string.indexOf('instagram') > 0 ? string : false\n\t\t}\n\n\t\t// layout private methods\n\t\t_toggleLoading(show) {\n\t\t\tshow = show || false\n\t\t\tif(show) {\n\t\t\t\tthis._$modalDialog.css('display', 'none')\n\t\t\t\tthis._$modal.removeClass('in show')\n\t\t\t\t$('.modal-backdrop').append(this._config.loadingMessage)\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis._$modalDialog.css('display', 'block')\n\t\t\t\tthis._$modal.addClass('in show')\n\t\t\t\t$('.modal-backdrop').find('.ekko-lightbox-loader').remove()\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\n\t\t_calculateBorders() {\n\t\t\treturn {\n\t\t\t\ttop: this._totalCssByAttribute('border-top-width'),\n\t\t\t\tright: this._totalCssByAttribute('border-right-width'),\n\t\t\t\tbottom: this._totalCssByAttribute('border-bottom-width'),\n\t\t\t\tleft: this._totalCssByAttribute('border-left-width'),\n\t\t\t}\n\t\t}\n\n\t\t_calculatePadding() {\n\t\t\treturn {\n\t\t\t\ttop: this._totalCssByAttribute('padding-top'),\n\t\t\t\tright: this._totalCssByAttribute('padding-right'),\n\t\t\t\tbottom: this._totalCssByAttribute('padding-bottom'),\n\t\t\t\tleft: this._totalCssByAttribute('padding-left'),\n\t\t\t}\n\t\t}\n\n\t\t_totalCssByAttribute(attribute) {\n\t\t\treturn parseInt(this._$modalDialog.css(attribute), 10) +\n\t\t\t\tparseInt(this._$modalContent.css(attribute), 10) +\n\t\t\t\tparseInt(this._$modalBody.css(attribute), 10)\n\t\t}\n\n\t\t_updateTitleAndFooter() {\n\t\t\tlet title = this._$element.data('title') || \"\"\n\t\t\tlet caption = this._$element.data('footer') || \"\"\n\n\t\t\tthis._titleIsShown = false\n\t\t\tif (title || this._config.alwaysShowClose) {\n\t\t\t\tthis._titleIsShown = true\n\t\t\t\tthis._$modalHeader.css('display', '').find('.modal-title').html(title || \"&nbsp;\")\n\t\t\t}\n\t\t\telse\n\t\t\t\tthis._$modalHeader.css('display', 'none')\n\n\t\t\tthis._footerIsShown = false\n\t\t\tif (caption) {\n\t\t\t\tthis._footerIsShown = true\n\t\t\t\tthis._$modalFooter.css('display', '').html(caption)\n\t\t\t}\n\t\t\telse\n\t\t\t\tthis._$modalFooter.css('display', 'none')\n\n\t\t\treturn this;\n\t\t}\n\n\t\t_showYoutubeVideo(remote, $containerForElement) {\n\t\t\tlet id = this._getYoutubeId(remote)\n\t\t\tlet query = remote.indexOf('&') > 0 ? remote.substr(remote.indexOf('&')) : ''\n\t\t\tlet width = this._$element.data('width') || 560\n\t\t\tlet height = this._$element.data('height') ||  width / ( 560/315 )\n\t\t\treturn this._showVideoIframe(\n\t\t\t\t`//www.youtube.com/embed/${id}?badge=0&autoplay=1&html5=1${query}`,\n\t\t\t\twidth,\n\t\t\t\theight,\n\t\t\t\t$containerForElement\n\t\t\t);\n\t\t}\n\n\t\t_showVimeoVideo(id, $containerForElement) {\n\t\t\tlet width = this._$element.data('width') || 500\n\t\t\tlet height = this._$element.data('height') ||  width / ( 560/315 )\n\t\t\treturn this._showVideoIframe(id + '?autoplay=1', width, height, $containerForElement)\n\t\t}\n\n\t\t_showInstagramVideo(id, $containerForElement) {\n\t\t\t// instagram load their content into iframe's so this can be put straight into the element\n\t\t\tlet width = this._$element.data('width') || 612\n\t\t\tlet height = width + 80;\n\t\t\tid = id.substr(-1) !== '/' ? id + '/' : id; // ensure id has trailing slash\n\t\t\t$containerForElement.html(`<iframe width=\"${width}\" height=\"${height}\" src=\"${id}embed/\" frameborder=\"0\" allowfullscreen></iframe>`);\n\t\t\tthis._resize(width, height);\n\t\t\tthis._config.onContentLoaded.call(this);\n\t\t\tif (this._$modalArrows) //hide the arrows when showing video\n\t\t\t\tthis._$modalArrows.css('display', 'none');\n\t\t\tthis._toggleLoading(false);\n\t\t\treturn this;\n\t\t}\n\n\t\t_showVideoIframe(url, width, height, $containerForElement) { // should be used for videos only. for remote content use loadRemoteContent (data-type=url)\n\t\t\theight = height || width; // default to square\n\t\t\t$containerForElement.html(`<div class=\"embed-responsive embed-responsive-16by9\"><iframe width=\"${width}\" height=\"${height}\" src=\"${url}\" frameborder=\"0\" allowfullscreen class=\"embed-responsive-item\"></iframe></div>`);\n\t\t\tthis._resize(width, height);\n\t\t\tthis._config.onContentLoaded.call(this);\n\t\t\tif (this._$modalArrows)\n\t\t\t\tthis._$modalArrows.css('display', 'none'); //hide the arrows when showing video\n\t\t\tthis._toggleLoading(false);\n\t\t\treturn this;\n\t\t}\n\n\t\t_showHtml5Video(url, $containerForElement) { // should be used for videos only. for remote content use loadRemoteContent (data-type=url)\n\t\t\tlet width = this._$element.data('width') || 560\n\t\t\tlet height = this._$element.data('height') ||  width / ( 560/315 )\n\t\t\t$containerForElement.html(`<div class=\"embed-responsive embed-responsive-16by9\"><video width=\"${width}\" height=\"${height}\" src=\"${url}\" preload=\"auto\" autoplay controls class=\"embed-responsive-item\"></video></div>`);\n\t\t\tthis._resize(width, height);\n\t\t\tthis._config.onContentLoaded.call(this);\n\t\t\tif (this._$modalArrows)\n\t\t\t\tthis._$modalArrows.css('display', 'none'); //hide the arrows when showing video\n\t\t\tthis._toggleLoading(false);\n\t\t\treturn this;\n\t\t}\n\n\t\t_loadRemoteContent(url, $containerForElement) {\n\t\t\tlet width = this._$element.data('width') || 560;\n\t\t\tlet height = this._$element.data('height') || 560;\n\n\t\t\tlet disableExternalCheck = this._$element.data('disableExternalCheck') || false;\n\t\t\tthis._toggleLoading(false);\n\n\t\t\t// external urls are loading into an iframe\n\t\t\t// local ajax can be loaded into the container itself\n\t\t\tif (!disableExternalCheck && !this._isExternal(url)) {\n\t\t\t\t$containerForElement.load(url, $.proxy(() => {\n\t\t\t\t\treturn this._$element.trigger('loaded.bs.modal');l\n\t\t\t\t}));\n\n\t\t\t} else {\n\t\t\t\t$containerForElement.html(`<iframe src=\"${url}\" frameborder=\"0\" allowfullscreen></iframe>`);\n\t\t\t\tthis._config.onContentLoaded.call(this);\n\t\t\t}\n\n\t\t\tif (this._$modalArrows) //hide the arrows when remote content\n\t\t\t\tthis._$modalArrows.css('display', 'none')\n\n\t\t\tthis._resize(width, height);\n\t\t\treturn this;\n\t\t}\n\n\t\t_isExternal(url) {\n\t\t\tlet match = url.match(/^([^:\\/?#]+:)?(?:\\/\\/([^\\/?#]*))?([^?#]+)?(\\?[^#]*)?(#.*)?/);\n\t\t\tif (typeof match[1] === \"string\" && match[1].length > 0 && match[1].toLowerCase() !== location.protocol)\n\t\t\t\treturn true;\n\n\t\t\tif (typeof match[2] === \"string\" && match[2].length > 0 && match[2].replace(new RegExp(`:(${{\n\t\t\t\t\t\"http:\": 80,\n\t\t\t\t\t\"https:\": 443\n\t\t\t\t}[location.protocol]})?$`), \"\") !== location.host)\n\t\t\t\treturn true;\n\n\t\t\treturn false;\n\t\t}\n\n\t\t_error( message ) {\n\t\t\tconsole.error(message);\n\t\t\tthis._containerToUse().html(message);\n\t\t\tthis._resize(300, 300);\n\t\t\treturn this;\n\t\t}\n\n\t\t_preloadImageByIndex(startIndex, numberOfTimes) {\n\n\t\t\tif(!this._$galleryItems)\n\t\t\t\treturn;\n\n\t\t\tlet next = $(this._$galleryItems.get(startIndex), false)\n\t\t\tif(typeof next == 'undefined')\n\t\t\t\treturn\n\n\t\t\tlet src = next.attr('data-remote') || next.attr('href')\n\t\t\tif (next.attr('data-type') === 'image' || this._isImage(src))\n\t\t\t\tthis._preloadImage(src, false)\n\n\t\t\tif(numberOfTimes > 0)\n\t\t\t\treturn this._preloadImageByIndex(startIndex + 1, numberOfTimes-1);\n\t\t}\n\n\t\t_preloadImage( src, $containerForImage) {\n\n\t\t\t$containerForImage = $containerForImage || false\n\n\t\t\tlet img = new Image();\n\t\t\tif ($containerForImage) {\n\n\t\t\t\t// if loading takes > 200ms show a loader\n\t\t\t\tlet loadingTimeout = setTimeout(() => {\n\t\t\t\t\t$containerForImage.append(this._config.loadingMessage)\n\t\t\t\t}, 200)\n\n\t\t\t\timg.onload = () => {\n\t\t\t\t\tif(loadingTimeout)\n\t\t\t\t\t\tclearTimeout(loadingTimeout)\n\t\t\t\t\tloadingTimeout = null;\n\t\t\t\t\tlet image = $('<img />');\n\t\t\t\t\timage.attr('src', img.src);\n\t\t\t\t\timage.addClass('img-fluid');\n\n\t\t\t\t\t// backward compatibility for bootstrap v3\n\t\t\t\t\timage.css('width', '100%');\n\n\t\t\t\t\t$containerForImage.html(image);\n\t\t\t\t\tif (this._$modalArrows)\n\t\t\t\t\t\tthis._$modalArrows.css('display', '') // remove display to default to css property\n\n\t\t\t\t\tthis._resize(img.width, img.height);\n\t\t\t\t\tthis._toggleLoading(false);\n\t\t\t\t\treturn this._config.onContentLoaded.call(this);\n\t\t\t\t};\n\t\t\t\timg.onerror = () => {\n\t\t\t\t\tthis._toggleLoading(false);\n\t\t\t\t\treturn this._error(this._config.strings.fail+`  ${src}`);\n\t\t\t\t};\n\t\t\t}\n\n\t\t\timg.src = src;\n\t\t\treturn img;\n\t\t}\n\n\t\t_swipeGesure() {\n\t\t    if (this._touchendX < this._touchstartX) {\n\t\t        return this.navigateRight();\n\t\t    }\n\t\t    if (this._touchendX > this._touchstartX) {\n\t\t        return this.navigateLeft();\n\t\t    }\n\t\t}\n\n\t\t_resize( width, height ) {\n\n\t\t\theight = height || width\n\t\t\tthis._wantedWidth = width\n\t\t\tthis._wantedHeight = height\n\n\t\t\tlet imageAspecRatio = width / height;\n\n\t\t\t// if width > the available space, scale down the expected width and height\n\t\t\tlet widthBorderAndPadding = this._padding.left + this._padding.right + this._border.left + this._border.right\n\n\t\t\t// force 10px margin if window size > 575px\n\t\t\tlet addMargin = this._config.doc.body.clientWidth > 575 ? 20 : 0\n\t\t\tlet discountMargin = this._config.doc.body.clientWidth > 575 ? 0 : 20\n\n\t\t\tlet maxWidth = Math.min(width + widthBorderAndPadding, this._config.doc.body.clientWidth - addMargin, this._config.maxWidth)\n\n\t\t\tif((width + widthBorderAndPadding) > maxWidth) {\n\t\t\t\theight = (maxWidth - widthBorderAndPadding - discountMargin) / imageAspecRatio;\n\t\t\t\twidth = maxWidth\n\t\t\t} else\n\t\t\t\twidth = (width + widthBorderAndPadding)\n\n\t\t\tlet headerHeight = 0,\n\t\t\t    footerHeight = 0\n\n\t\t\t// as the resize is performed the modal is show, the calculate might fail\n\t\t\t// if so, default to the default sizes\n\t\t\tif (this._footerIsShown)\n\t\t\t\tfooterHeight = this._$modalFooter.outerHeight(true) || 55\n\n\t\t\tif (this._titleIsShown)\n\t\t\t\theaderHeight = this._$modalHeader.outerHeight(true) || 67\n\n\t\t\tlet borderPadding = this._padding.top + this._padding.bottom + this._border.bottom + this._border.top\n\n\t\t\t//calculated each time as resizing the window can cause them to change due to Bootstraps fluid margins\n\t\t\tlet margins = parseFloat(this._$modalDialog.css('margin-top')) + parseFloat(this._$modalDialog.css('margin-bottom'));\n\n\t\t\tlet maxHeight = Math.min(height, $(window).height() - borderPadding - margins - headerHeight - footerHeight, this._config.maxHeight - borderPadding - headerHeight - footerHeight);\n\n\t\t\tif(height > maxHeight) {\n\t\t\t\t// if height > the available height, scale down the width\n\t\t\t\twidth = Math.ceil(maxHeight * imageAspecRatio) + widthBorderAndPadding;\n\t\t\t}\n\n\t\t\tthis._$lightboxContainer.css('height', maxHeight)\n\t\t\tthis._$modalDialog.css('flex', 1).css('maxWidth', width);\n\n\t\t\tlet modal = this._$modal.data('bs.modal');\n\t\t\tif (modal) {\n\t\t\t\t// v4 method is mistakenly protected\n\t\t\t\ttry {\n\t\t\t\t\tmodal._handleUpdate();\n\t\t\t\t} catch(Exception) {\n\t\t\t\t\tmodal.handleUpdate();\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\n\t\tstatic _jQueryInterface(config) {\n\t\t\tconfig = config || {}\n\t\t\treturn this.each(() => {\n\t\t\t\tlet $this = $(this)\n\t\t\t\tlet _config = $.extend(\n\t\t\t\t\t{},\n\t\t\t\t\tLightbox.Default,\n\t\t\t\t\t$this.data(),\n\t\t\t\t\ttypeof config === 'object' && config\n\t\t\t\t)\n\n\t\t\t\tnew Lightbox(this, _config)\n\t\t\t})\n\t\t}\n\t}\n\n\n\n\t$.fn[NAME]             = Lightbox._jQueryInterface\n\t$.fn[NAME].Constructor = Lightbox\n\t$.fn[NAME].noConflict  = () => {\n\t\t$.fn[NAME] = JQUERY_NO_CONFLICT\n\t\treturn Lightbox._jQueryInterface\n\t}\n\n\treturn Lightbox\n\n})(jQuery)\n\nexport default Lightbox\n"]}