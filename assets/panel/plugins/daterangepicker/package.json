{"_args": [["daterangepicker@3.0.5", "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE"]], "_from": "daterangepicker@3.0.5", "_id": "daterangepicker@3.0.5", "_inBundle": false, "_integrity": "sha512-BoVV+OjVARWNE15iF+3Y2QIMioAD2UODHvJwIq+NtG0vxh61dXRmOMXlw2dsvxS8KY4n5uvIcBfIPiEiiGJcBg==", "_location": "/daterangepicker", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "daterangepicker@3.0.5", "name": "daterangepicker", "escapedName": "daterangepicker", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/daterangepicker/-/daterangepicker-3.0.5.tgz", "_spec": "3.0.5", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.dangrossman.info"}, "bugs": {"url": "https://github.com/dangrossman/daterangepicker/issues"}, "dependencies": {"jquery": ">=1.10", "moment": "^2.9.0"}, "description": "Date range picker component for Bootstrap", "homepage": "https://github.com/dangrossman/daterangepicker", "license": "MIT", "main": "daterangepicker.js", "name": "daterangepicker", "repository": {"type": "git", "url": "git+https://github.com/dangrossman/daterangepicker.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "style": "daterangepicker.css", "version": "3.0.5"}