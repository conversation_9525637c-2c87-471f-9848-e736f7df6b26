{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///bootstrap-colorpicker.min.js", "webpack:///webpack/bootstrap e5fc9649974c93b0b79b", "webpack:///external {\"root\":\"jQ<PERSON>y\",\"commonjs2\":\"jquery\",\"commonjs\":\"jquery\",\"amd\":\"jquery\"}", "webpack:///./src/js/Extension.js", "webpack:///./src/js/ColorItem.js", "webpack:///./src/js/options.js", "webpack:///./src/js/extensions/Palette.js", "webpack:///./node_modules/color-name/index.js", "webpack:///./node_modules/color-convert/conversions.js", "webpack:///./src/js/plugin.js", "webpack:///./src/js/Colorpicker.js", "webpack:///./src/js/extensions/index.js", "webpack:///./src/js/extensions/Debugger.js", "webpack:///./src/js/extensions/Preview.js", "webpack:///./src/js/extensions/Swatches.js", "webpack:///./src/js/SliderHandler.js", "webpack:///./src/js/PopupHandler.js", "webpack:///./src/js/InputHandler.js", "webpack:///./node_modules/color/index.js", "webpack:///./node_modules/color-string/index.js", "webpack:///./node_modules/simple-swizzle/index.js", "webpack:///./node_modules/is-arrayish/index.js", "webpack:///./node_modules/color-convert/index.js", "webpack:///./node_modules/color-convert/route.js", "webpack:///./src/js/ColorHandler.js", "webpack:///./src/js/PickerHandler.js", "webpack:///./src/js/AddonHandler.js"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "self", "this", "__WEBPACK_EXTERNAL_MODULE_0__", "modules", "__webpack_require__", "moduleId", "installedModules", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "value", "_createClass", "defineProperties", "target", "props", "length", "descriptor", "writable", "key", "protoProps", "staticProps", "_j<PERSON>y", "_jquery2", "obj", "default", "Extension", "colorpicker", "options", "arguments", "undefined", "element", "Error", "on", "$", "proxy", "onCreate", "onDestroy", "onUpdate", "onChange", "onInvalid", "onShow", "onHide", "onEnable", "onDisable", "color", "event", "off", "ColorItem", "HSVAColor", "_color", "_color2", "h", "v", "a", "isNaN", "format", "replace", "fn", "_len", "args", "Array", "_key", "result", "apply", "QixColor", "_original", "sanitizeFormat", "valid", "parse", "_format", "isHex", "model", "hue", "saturation", "alpha", "has<PERSON><PERSON><PERSON>", "toObject", "string", "round", "str", "<PERSON><PERSON><PERSON><PERSON>", "isDark", "isLight", "formula", "hues", "isArray", "colorFormulas", "colors", "mainColor", "for<PERSON>ach", "levels", "saturationv", "push", "set", "Math", "sanitizeString", "e", "String", "match", "toLowerCase", "complementary", "triad", "tetrad", "splitcomplement", "sassVars", "bar_size_short", "base_margin", "columns", "sliderSize", "customClass", "fallbackColor", "horizontal", "inline", "container", "popover", "animation", "placement", "fallbackPlacement", "debug", "input", "addon", "autoInputFallback", "useHashPrefix", "useAlpha", "template", "extensions", "showText", "sliders", "selector", "maxLeft", "maxTop", "callLeft", "callTop", "childSelector", "slidersHorz", "_interopRequireDefault", "_possibleConstructorReturn", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "_typeof", "Symbol", "iterator", "_Extension2", "_Extension3", "defaults", "namesAsValues", "Palette", "_Extension", "_this", "getPrototypeOf", "extend", "keys", "realColor", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "toUpperCase", "getValue", "getName", "defaultValue", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "comparativeDistance", "x", "y", "pow", "cssKeywords", "reverseKeywords", "convert", "rgb", "channels", "labels", "hsl", "hsv", "hwb", "cmyk", "xyz", "lab", "lch", "hex", "keyword", "ansi16", "ansi256", "hcg", "apple", "r", "g", "b", "min", "max", "delta", "rdif", "gdif", "bdif", "diff", "diffc", "w", "k", "reversed", "currentClosestKeyword", "currentClosestDistance", "Infinity", "distance", "z", "t1", "t2", "t3", "val", "sv", "smin", "lmin", "hi", "floor", "f", "q", "t", "sl", "vmin", "wh", "bl", "ratio", "y2", "x2", "z2", "hr", "atan2", "PI", "sqrt", "cos", "sin", "ansi", "mult", "rem", "integer", "toString", "substring", "colorString", "split", "map", "char", "join", "parseInt", "grayscale", "chroma", "pure", "mg", "_Colorpicker", "_Colorpicker2", "plugin", "Colorpicker", "option", "fnArgs", "slice", "isSingleElement", "returnValue", "$elements", "each", "$this", "inst", "data", "isFunction", "_options", "_options2", "_extensions", "_extensions2", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_SliderHandler2", "_Popup<PERSON>andler", "_PopupHandler2", "_InputHandler", "_InputHandler2", "_ColorHandler", "_ColorHandler2", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_PickerHandler2", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_AddonHandler2", "_ColorItem", "_ColorItem2", "colorPickerIdCounter", "id", "lastEvent", "alias", "addClass", "attr", "disabled", "inputHandler", "InputHandler", "colorHandler", "ColorHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addon<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "trigger", "picker", "bind", "initExtensions", "attach", "update", "isDisabled", "disable", "ext", "registerExtension", "ExtensionClass", "config", "unbind", "removeClass", "removeData", "show", "hide", "toggle", "ch", "hasColor", "equals", "createColor", "assureColor", "enable", "eventName", "type", "coreExtensions", "Swatches", "Preview", "Debugger", "_Debugger", "_Debugger2", "_Preview", "_Preview2", "_Swatches", "_Swatches2", "_Palette", "_Palette2", "debugger", "preview", "swatches", "palette", "_get", "receiver", "Function", "desc", "getOwnPropertyDescriptor", "parent", "eventCounter", "hasInput", "onChangeInput", "_console", "logMessage", "console", "concat", "logArgs", "log", "elementInner", "find", "append", "css", "html", "toRgbString", "_Palette3", "barTemplate", "swatchTemplate", "isEnabled", "load", "_this2", "swatchContainer", "isAliased", "empty", "$swatch", "$sw", "setValue", "currentSlider", "mousePointer", "left", "top", "onMove", "defaultOnMove", "slider", "cp", "getClone", "getFallbackColor", "guideStyle", "focus", "sliderClasses", "slider<PERSON><PERSON>", "pressed", "mousemove.colorpicker", "moved", "touchmove.colorpicker", "mouseup.colorpicker", "released", "touchend.colorpicker", "pageX", "pageY", "originalEvent", "touches", "zone", "closest", "is", "guide", "offset", "style", "preventDefault", "popoverTarget", "popoverTip", "clicking", "hidding", "showing", "hasAddon", "createPopover", "mousedown.colorpicker touchstart.colorpicker", "focus.colorpicker", "focusout.colorpicker", "reposition", "document", "onClickingInside", "isOrIsInside", "currentTarget", "isClickingInside", "_defaults", "content", "tip", "fireShow", "fireHide", "isVisible", "stopPropagation", "isPopover", "isHidden", "hasClass", "_initValue", "keyup.colorpicker", "onkeyup", "change.colorpicker", "onchange", "item", "getFormattedColor", "prop", "inputVal", "getColorString", "resolveColorDelegate", "isInvalidColor", "Color", "skippedModels", "valpha", "newArr", "_slice", "zeroArray", "splice", "hashedKeys", "sort", "hashedModelKeys", "JSON", "stringify", "limiters", "limit", "freeze", "roundTo", "num", "places", "Number", "toFixed", "roundToPlace", "getset", "channel", "modifier", "maxfn", "assertArray", "arr", "toJSON", "to", "percentString", "percent", "array", "unitArray", "unitObject", "saturationl", "lightness", "wblack", "rgbNumber", "luminosity", "lum", "chan", "contrast", "color2", "lum1", "lum2", "level", "contrastRatio", "negate", "lighten", "darken", "saturate", "desaturate", "whiten", "blacken", "fade", "opaquer", "rotate", "degrees", "mix", "mixinColor", "weight", "color1", "w1", "w2", "newAlpha", "raw", "clamp", "hexDouble", "colorNames", "swizzle", "reverseNames", "cs", "prefix", "hexAlpha", "abbr", "rgba", "per", "i2", "parseFloat", "hsla", "hwba", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "results", "len", "arg", "wrap", "wrapRaw", "wrappedFn", "conversion", "wrapRounded", "conversions", "route", "fromModel", "routes", "toModel", "buildGraph", "graph", "models", "deriveBFS", "queue", "current", "pop", "adjacents", "adjacent", "node", "unshift", "link", "from", "wrapConversion", "path", "cur", "fallbackOnInvalid", "isAlphaEnabled", "fallback", "warn", "extResolvedColor", "resolveColor", "hasTransparency", "_supportsAlphaBar", "pickerParent", "appendTo", "remove", "vertical", "saturationGuide", "hueGuide", "alphaGuide", "hsva", "toHsvaRatio", "getCloneHueOnly", "toHexString", "hexColor", "alphaBg", "colorStr", "styles", "background", "icn", "eq"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,EAAAG,QAAA,WACA,kBAAAC,gBAAAC,IACAD,OAAA,mCAAAJ,GACA,gBAAAC,SACAA,QAAA,yBAAAD,EAAAG,QAAA,WAEAJ,EAAA,yBAAAC,EAAAD,EAAA,SACC,mBAAAO,WAAAC,KAAA,SAAAC,GACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAV,OAGA,IAAAC,GAAAU,EAAAD,IACAE,EAAAF,EACAG,GAAA,EACAb,WAUA,OANAQ,GAAAE,GAAAI,KAAAb,EAAAD,QAAAC,IAAAD,QAAAS,GAGAR,EAAAY,GAAA,EAGAZ,EAAAD,QAvBA,GAAAW,KA4DA,OAhCAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,SAAAjB,EAAAkB,EAAAC,GACAV,EAAAW,EAAApB,EAAAkB,IACAG,OAAAC,eAAAtB,EAAAkB,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAzB,GACA,GAAAkB,GAAAlB,KAAA0B,WACA,WAA2B,MAAA1B,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAQ,GAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDpB,EAAAuB,EAAA,GAGAvB,IAAAwB,EAAA,KDgBM,SAAUhC,EAAQD,GE7ExBC,EAAAD,QAAAO,GFmFM,SAAUN,EAAQD,EAASS,GAEjC,YAeA,SAASyB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAZhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MG1FhiBa,EAAAxC,EAAA,GH8FIyC,EAEJ,SAAgCC,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,IAFjDF,GGzFhCI,EHkGU,WG7Fd,QAAAA,GAAYC,GAA2B,GAAdC,GAAcC,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,KAarC,IAbqCtB,EAAA5B,KAAA+C,GAKrC/C,KAAKgD,YAAcA,EAMnBhD,KAAKiD,QAAUA,GAETjD,KAAKgD,YAAYI,UAAWpD,KAAKgD,YAAYI,QAAQf,OACzD,KAAM,IAAIgB,OAAM,mDAGlBrD,MAAKgD,YAAYI,QAAQE,GAAG,oCAAqCC,UAAEC,MAAMxD,KAAKyD,SAAUzD,OACxFA,KAAKgD,YAAYI,QAAQE,GAAG,qCAAsCC,UAAEC,MAAMxD,KAAK0D,UAAW1D,OAC1FA,KAAKgD,YAAYI,QAAQE,GAAG,oCAAqCC,UAAEC,MAAMxD,KAAK2D,SAAU3D,OACxFA,KAAKgD,YAAYI,QAAQE,GAAG,oCAAqCC,UAAEC,MAAMxD,KAAK4D,SAAU5D,OACxFA,KAAKgD,YAAYI,QAAQE,GAAG,qCAAsCC,UAAEC,MAAMxD,KAAK6D,UAAW7D,OAC1FA,KAAKgD,YAAYI,QAAQE,GAAG,kCAAmCC,UAAEC,MAAMxD,KAAK8D,OAAQ9D,OACpFA,KAAKgD,YAAYI,QAAQE,GAAG,kCAAmCC,UAAEC,MAAMxD,KAAK+D,OAAQ/D,OACpFA,KAAKgD,YAAYI,QAAQE,GAAG,oCAAqCC,UAAEC,MAAMxD,KAAKgE,SAAUhE,OACxFA,KAAKgD,YAAYI,QAAQE,GAAG,qCAAsCC,UAAEC,MAAMxD,KAAKiE,UAAWjE,OHkP5F,MA9HAiC,GAAac,IACXP,IAAK,eACLR,MAAO,SG1GIkC,KAAyBhB,UAAAb,OAAA,OAAAc,KAAAD,UAAA,KAAAA,UAAA,EACpC,QAAO,KHuHPV,IAAK,WACLR,MAAO,SG/GAmC,OH2HP3B,IAAK,YACLR,MAAO,SGlHCmC,GACRnE,KAAKgD,YAAYI,QAAQgB,IAAI,uBH6H7B5B,IAAK,WACLR,MAAO,SGrHAmC,OHiIP3B,IAAK,WACLR,MAAO,SGxHAmC,OHoIP3B,IAAK,YACLR,MAAO,SG3HCmC,OHuIR3B,IAAK,SACLR,MAAO,SG9HFmC,OH0IL3B,IAAK,SACLR,MAAO,SGjIFmC,OH6IL3B,IAAK,YACLR,MAAO,SGpICmC,OHgJR3B,IAAK,WACLR,MAAO,SGvIAmC,QH4IFpB,IAGTrD,GAAQoD,QG1IOC,GH8IT,SAAUpD,EAAQD,EAASS,GAEjC,YAmBA,SAASyB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAhBhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,IAETtC,EAAQ2E,UAAY3E,EAAQ4E,cAAYnB,EAExC,IAAIlB,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MIrShiByC,EAAApE,EAAA,IJ4SIqE,EAEJ,SAAgC3B,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,IAFlD0B,GItS/BD,EJgTU,WIzSd,QAAAA,GAAYG,EAAG9C,EAAG+C,EAAGC,GAAG/C,EAAA5B,KAAAsE,GACtBtE,KAAKyE,EAAIG,MAAMH,GAAK,EAAIA,EACxBzE,KAAK2B,EAAIiD,MAAMjD,GAAK,EAAIA,EACxB3B,KAAK0E,EAAIE,MAAMF,GAAK,EAAIA,EACxB1E,KAAK2E,EAAIC,MAAMH,GAAK,EAAIE,EJ4T1B,MAPA1C,GAAaqC,IACX9B,IAAK,WACLR,MAAO,WInTP,MAAUhC,MAAKyE,EAAf,KAAqBzE,KAAK2B,EAA1B,MAAiC3B,KAAK0E,EAAtC,MAA6C1E,KAAK2E,MJwT7CL,KIjTHD,EJyTU,WIjQd,QAAAA,KAAyC,GAA7BH,GAA6BhB,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAArB,KAAM2B,EAAe3B,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,IAAMtB,GAAA5B,KAAAqE,GACvCrE,KAAK8E,QAAQZ,EAAOW,GJi9BtB,MAhtBA5C,GAAaoC,IACX7B,IAAK,MAiBLR,MAAO,SIjTL+C,GAAa,OAAAC,GAAA9B,UAAAb,OAAN4C,EAAMC,MAAAF,EAAA,EAAAA,EAAA,KAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAANF,EAAME,EAAA,GAAAjC,UAAAiC,EACf,IAAyB,IAArBjC,UAAUb,OACZ,MAAOrC,MAAKuE,MAGd,IAAIa,GAASpF,KAAKuE,OAAOQ,GAAIM,MAAMrF,KAAKuE,OAAQU,EAEhD,OAAMG,aAAkBE,WAKjB,GAAIjB,GAAUe,EAAQpF,KAAK6E,QAHzBO,KJmUT5C,IAAK,WACLrB,IAAK,WIvTL,MAAOnB,MAAKuF,eJiUZ/C,IAAK,YAULrB,IAAK,WIlXL,MAAOmD,OJ2YTrC,EAAaoC,IACX7B,IAAK,UACLR,MAAO,SIlVDkC,GAAsB,GAAfW,GAAe3B,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,IAkBtB,IAjBA2B,EAASR,EAAUmB,eAAeX,GAMlC7E,KAAKuF,WACHrB,MAAOA,EACPW,OAAQA,EACRY,OAAO,GAMTzF,KAAKuE,OAASF,EAAUqB,MAAMxB,GAEV,OAAhBlE,KAAKuE,OAGP,MAFAvE,MAAKuE,QAAS,EAAAC,EAAA1B,gBACd9C,KAAKuF,UAAUE,OAAQ,EAQzBzF,MAAK2F,QAAUd,IACZR,EAAUuB,MAAM1B,GAAS,MAAQlE,KAAKuE,OAAOsB,UJiWhDrD,IAAK,UAQLR,MAAO,WIxOP,OAAgC,IAAzBhC,KAAKuF,UAAUE,SJmPtBjD,IAAK,cASLR,MAAO,SI3LGyC,GACVzE,KAAK8F,IAAiB,KAAT,EAAIrB,MJqMjBjC,IAAK,qBASLR,MAAO,SI5LUL,GACjB3B,KAAK+F,WAAkB,IAAJpE,KJsMnBa,IAAK,gBASLR,MAAO,SI7LK0C,GACZ1E,KAAKgC,MAAmB,KAAT,EAAI0C,MJuMnBlC,IAAK,gBASLR,MAAO,SI7LK2C,GACZ3E,KAAKgG,MAAQ,EAAIrB,KJuMjBnC,IAAK,gBAQLR,MAAO,WI7LP,MAA2B,KAApBhC,KAAK+F,cJwMZvD,IAAK,gBACLR,MAAO,WIhMP,MAAsB,KAAfhC,KAAKgG,SJ2MZxD,IAAK,kBACLR,MAAO,WInMP,MAAOhC,MAAKiG,YAAejG,KAAKgG,MAAQ,KJ8MxCxD,IAAK,WACLR,MAAO,WItMP,OAAQ4C,MAAM5E,KAAKgG,UJiNnBxD,IAAK,WACLR,MAAO,WIzMP,MAAO,IAAIsC,GAAUtE,KAAK8F,IAAK9F,KAAK+F,WAAY/F,KAAKgC,MAAOhC,KAAKgG,UJoNjExD,IAAK,SACLR,MAAO,WI5MP,MAAOhC,MAAKkG,cJyNZ1D,IAAK,cACLR,MAAO,WI/MP,MAAO,IAAIsC,GACTtE,KAAK8F,IAAM,IACX9F,KAAK+F,WAAa,IAClB/F,KAAKgC,MAAQ,IACbhC,KAAKgG,UJuNPxD,IAAK,WACLR,MAAO,WI7MP,MAAOhC,MAAKmG,YJ0NZ3D,IAAK,SACLR,MAAO,WIjNa,GAAf6C,GAAe3B,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,IAGd,MAFA2B,EAASR,EAAUmB,eAAeX,GAAkB7E,KAAK6E,SAGvD,MAAO7E,MAAKuE,OAAO6B,QAAQD,QAG7B,QAA4BhD,KAAxBnD,KAAKuE,OAAOM,GACd,KAAM,IAAIxB,OAAJ,8BAAwCwB,EAAxC,IAGR,IAAIwB,GAAMrG,KAAKuE,OAAOM,IAEtB,OAAOwB,GAAID,MAAQC,EAAID,QAAQD,SAAWE,KJiO1C7D,IAAK,SACLR,MAAO,SItNFkC,GAGL,MAFAA,GAASA,YAAiBG,GAAaH,EAAQ,GAAIG,GAAUH,MAExDA,EAAMoC,YAActG,KAAKsG,aAK5BtG,KAAK8F,MAAQ5B,EAAM4B,KACnB9F,KAAK+F,aAAe7B,EAAM6B,YAC1B/F,KAAKgC,QAAUkC,EAAMlC,OACrBhC,KAAKgG,QAAU9B,EAAM8B,UJ4NvBxD,IAAK,WACLR,MAAO,WInNP,MAAO,IAAIqC,GAAUrE,KAAKuE,OAAQvE,KAAK6E,WJ+NvCrC,IAAK,kBACLR,MAAO,WItNP,MAAO,IAAIqC,IAAWrE,KAAK8F,IAAK,IAAK,IAAK,GAAI9F,KAAK6E,WJiOnDrC,IAAK,iBACLR,MAAO,WIzNP,MAAO,IAAIqC,GAAUrE,KAAKuE,OAAOyB,MAAM,GAAIhG,KAAK6E,WJoOhDrC,IAAK,cACLR,MAAO,WI5NP,MAAOhC,MAAKmG,OAAO,UJuOnB3D,IAAK,cACLR,MAAO,WI/NP,MAAOhC,MAAKmG,OAAO,UJ0OnB3D,IAAK,cACLR,MAAO,WIlOP,MAAOhC,MAAKmG,OAAO,UJ8OnB3D,IAAK,SACLR,MAAO,WIrOP,MAAOhC,MAAKuE,OAAOgC,YJiPnB/D,IAAK,UACLR,MAAO,WIxOP,MAAOhC,MAAKuE,OAAOiC,aJuPnBhE,IAAK,WACLR,MAAO,SI5OAyE,GACP,GAAIC,KAEJ,IAAIxB,MAAMyB,QAAQF,GAChBC,EAAOD,MACF,KAAKpC,EAAUuC,cAAcnF,eAAegF,GACjD,KAAM,IAAIpD,OAAJ,yCAAmDoD,EAAnD,KAENC,GAAOrC,EAAUuC,cAAcH,GAGjC,GAAII,MAAaC,EAAY9G,KAAKuE,OAAQM,EAAS7E,KAAK6E,MAaxD,OAXA6B,GAAKK,QAAQ,SAAUjB,GACrB,GAAIkB,IACFlB,GAAQgB,EAAUhB,MAAQA,GAAO,IAAOgB,EAAUhB,MAClDgB,EAAUG,cACVH,EAAU9E,QACV8E,EAAUd,QAGZa,GAAOK,KAAK,GAAI7C,GAAU2C,EAAQnC,MAG7BgC,KJ4OPrE,IAAK,MACLrB,IAAK,WIvlBL,MAAOnB,MAAKuE,OAAOuB,OJwmBnBqB,IAAK,SI1jBCnF,GACNhC,KAAKuE,OAASvE,KAAKuE,OAAOuB,IAAI9D,MJ6jB9BQ,IAAK,aACLrB,IAAK,WIpmBL,MAAOnB,MAAKuE,OAAO0C,eJ8mBnBE,IAAK,SItjBQnF,GACbhC,KAAKuE,OAASvE,KAAKuE,OAAO0C,YAAYjF,MJyjBtCQ,IAAK,QACLrB,IAAK,WI1mBL,MAAOnB,MAAKuE,OAAOvC,SJonBnBmF,IAAK,SIljBGnF,GACRhC,KAAKuE,OAASvE,KAAKuE,OAAOvC,MAAMA,MJqjBhCQ,IAAK,QACLrB,IAAK,WIhnBL,GAAIwD,GAAI3E,KAAKuE,OAAOyB,OAEpB,OAAOpB,OAAMD,GAAK,EAAIA,GJ0nBtBwC,IAAK,SIhjBGnF,GAERhC,KAAKuE,OAASvE,KAAKuE,OAAOyB,MAAMoB,KAAKhB,MAAc,IAARpE,GAAe,QJmjB1DQ,IAAK,SACLrB,IAAK,WIvnBL,MAAOnB,MAAK2F,QAAU3F,KAAK2F,QAAU3F,KAAKuE,OAAOsB,OJ0nBjDsB,IAAK,SIriBInF,GACThC,KAAK2F,QAAUtB,EAAUmB,eAAexD,QJwiBxCQ,IAAK,QACLR,MAAO,SInyBIkC,GACX,GAAIA,YAAiBoB,WACnB,MAAOpB,EAGT,IAAIA,YAAiBG,GACnB,MAAOH,GAAMK,MAGf,IAAIM,GAAS,IAQb,IAAc,QALZX,EADEA,YAAiBI,IACVJ,EAAMO,EAAGP,EAAMvC,EAAGuC,EAAMQ,EAAGE,MAAMV,EAAMS,GAAK,EAAIT,EAAMS,GAEvDN,EAAUgD,eAAenD,IAIjC,MAAO,KAGLgB,OAAMyB,QAAQzC,KAChBW,EAAS,MAGX,KACE,OAAO,EAAAL,EAAA1B,SAASoB,EAAOW,GACvB,MAAOyC,GACP,MAAO,UJkzBT9E,IAAK,iBACLR,MAAO,SItyBaqE,GACpB,MAAqB,gBAARA,IAAoBA,YAAekB,QAI5ClB,EAAImB,MAAM,mBACZ,IAAWnB,EAGa,gBAAtBA,EAAIoB,cACC,YAGFpB,EAXEA,KJg0BT7D,IAAK,QACLR,MAAO,SIzyBIqE,GACX,OAAqB,gBAARA,IAAoBA,YAAekB,YAIvClB,EAAImB,MAAM,wBJyzBnBhF,IAAK,iBACLR,MAAO,SI5yBa6C,GACpB,OAAQA,GACN,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,OACH,MAAO,KACT,KAAK,MACL,IAAK,OACL,IAAK,UACL,IAAK,OACH,MAAO,KACT,KAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,OACH,MAAO,KACT,SACE,MAAO,QJizBNR,II1aTA,GAAUuC,eACRc,eAAgB,KAChBC,OAAQ,EAAG,IAAK,KAChBC,QAAS,EAAG,GAAI,IAAK,KACrBC,iBAAkB,EAAG,GAAI,MJwb3BnI,EAAQoD,QIrbOuB,EJsbf3E,EInbE4E,YJobF5E,EInbE2E,aJubI,SAAU1E,EAAQD,EAASS,GAEjC,YAQAY,QAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GKtkCT,IAAI8F,IACFC,eAAkB,GAClBC,YAAe,EACfC,QAAW,GAGTC,EAAcJ,EAASC,eAAiBD,EAASG,QAAYH,EAASE,aAAeF,EAASG,QAAU,EL6kC5GvI,GAAQoD,SKjkCNqF,YAAa,KAObjE,OAAO,EAQPkE,eAAe,EAWfvD,OAAQ,OASRwD,YAAY,EAUZC,QAAQ,EAYRC,WAAW,EAQXC,SACEC,WAAW,EACXC,UAAW,SACXC,kBAAmB,QAOrBC,OAAO,EAOPC,MAAO,QAQPC,MAAO,2BASPC,mBAAmB,EASnBC,eAAe,EAafC,UAAU,EAeVC,qWA+BAC,aAEIvI,KAAM,UACNqC,SACEmG,UAAU,KAQhBC,SACEtD,YACEuD,SAAU,0BACVC,QAASrB,EACTsB,OAAQtB,EACRuB,SAAU,qBACVC,QAAS,iBAEX5D,KACEwD,SAAU,mBACVC,QAAS,EACTC,OAAQtB,EACRuB,UAAU,EACVC,QAAS,eAEX1D,OACEsD,SAAU,qBACVK,cAAe,2BACfJ,QAAS,EACTC,OAAQtB,EACRuB,UAAU,EACVC,QAAS,kBAObE,aACE7D,YACEuD,SAAU,0BACVC,QAASrB,EACTsB,OAAQtB,EACRuB,SAAU,qBACVC,QAAS,iBAEX5D,KACEwD,SAAU,mBACVC,QAASrB,EACTsB,OAAQ,EACRC,SAAU,cACVC,SAAS,GAEX1D,OACEsD,SAAU,qBACVK,cAAe,2BACfJ,QAASrB,EACTsB,OAAQ,EACRC,SAAU,gBACVC,SAAS,MLskCT,SAAU/J,EAAQD,EAASS,GAEjC,YAmBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAEhH,QAAS+H,GAA2B/J,EAAMS,GAAQ,IAAKT,EAAQ,KAAM,IAAIgK,gBAAe,4DAAgE,QAAOvJ,GAAyB,gBAATA,IAAqC,kBAATA,GAA8BT,EAAPS,EAElO,QAASwJ,GAAUC,EAAUC,GAAc,GAA0B,kBAAfA,IAA4C,OAAfA,EAAuB,KAAM,IAAInI,WAAU,iEAAoEmI,GAAeD,GAASzI,UAAYT,OAAOoJ,OAAOD,GAAcA,EAAW1I,WAAa4I,aAAepI,MAAOiI,EAAU/I,YAAY,EAAOqB,UAAU,EAAMtB,cAAc,KAAeiJ,IAAYnJ,OAAOsJ,eAAiBtJ,OAAOsJ,eAAeJ,EAAUC,GAAcD,EAASK,UAAYJ,GAtBjenJ,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIuI,GAA4B,kBAAXC,SAAoD,gBAApBA,QAAOC,SAAwB,SAAU5H,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAX2H,SAAyB3H,EAAIuH,cAAgBI,QAAU3H,IAAQ2H,OAAOhJ,UAAY,eAAkBqB,IAElQZ,EAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MM30ChiB4I,EAAAvK,EAAA,GN+0CIwK,EAAcd,EAAuBa,GM90CzC/H,EAAAxC,EAAA,GNk1CIyC,EAAWiH,EAAuBlH,GMh1ClCiI,GAuBF/D,OAAQ,KAQRgE,eAAe,GAOXC,EN21CQ,SAAUC,GMl1CtB,QAAAD,GAAY9H,GAA2B,GAAdC,GAAcC,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,KAAAtB,GAAA5B,KAAA8K,EAAA,IAAAE,GAAAlB,EAAA9J,MAAA8K,EAAAR,WAAAvJ,OAAAkK,eAAAH,IAAAtK,KAAAR,KAC/BgD,EAAaO,UAAE2H,QAAO,KAAUN,EAAU3H,IADX,OAG/BiC,OAAMyB,QAAQqE,EAAK/H,QAAQ4D,SAA4C,WAA/B0D,EAAOS,EAAK/H,QAAQ4D,UAChEmE,EAAK/H,QAAQ4D,OAAS,MAJamE,ENw9CvC,MArIAhB,GAAUc,EAASC,GAEnB9I,EAAa6I,IACXtI,IAAK,SAMLrB,IAAK,WM/1CL,MAAOnB,MAAKiD,QAAQ4D,WNs3CtB5E,EAAa6I,IACXtI,IAAK,YACLR,MAAO,WMz2CP,MAAKhC,MAAKiD,QAAQ4D,OAId3B,MAAMyB,QAAQ3G,KAAKiD,QAAQ4D,QACtB7G,KAAKiD,QAAQ4D,OAAOxE,OAGM,WAA/BkI,EAAOvK,KAAKiD,QAAQ4D,QACf9F,OAAOoK,KAAKnL,KAAKiD,QAAQ4D,QAAQxE,OAGnC,EAXE,KNw3CTG,IAAK,eACLR,MAAO,SM32CIkC,GAAyB,GAAlBkH,KAAkBlI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,KAAAA,UAAA,EACpC,SAAIlD,KAAKqL,aAAe,KAKpBnG,MAAMyB,QAAQ3G,KAAKiD,QAAQ4D,QACzB7G,KAAKiD,QAAQ4D,OAAOyE,QAAQpH,IAAU,EACjCA,EAELlE,KAAKiD,QAAQ4D,OAAOyE,QAAQpH,EAAMqH,gBAAkB,EAC/CrH,EAAMqH,cAEXvL,KAAKiD,QAAQ4D,OAAOyE,QAAQpH,EAAMuD,gBAAkB,GAC/CvD,EAAMuD,cAKkB,WAA/B8C,EAAOvK,KAAKiD,QAAQ4D,WAKnB7G,KAAKiD,QAAQ4H,eAAiBO,EAC1BpL,KAAKwL,SAAStH,GAAO,GAEvBlE,KAAKyL,QAAQvH,EAAOlE,KAAKyL,QAAQ,IAAMvH,SNy3C9C1B,IAAK,UACLR,MAAO,SMh3CDA,GAA6B,GAAtB0J,GAAsBxI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,IAAAA,UAAA,EACnC,IAAuB,gBAAVlB,KAAwBhC,KAAKiD,QAAQ4D,OAChD,MAAO6E,EAET,KAAK,GAAI9K,KAAQZ,MAAKiD,QAAQ4D,OAC5B,GAAK7G,KAAKiD,QAAQ4D,OAAOpF,eAAeb,IAGpCZ,KAAKiD,QAAQ4D,OAAOjG,GAAM6G,gBAAkBzF,EAAMyF,cACpD,MAAO7G,EAGX,OAAO8K,MN83CPlJ,IAAK,WACLR,MAAO,SMr3CApB,GAA4B,GAAtB8K,GAAsBxI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,IAAAA,UAAA,EACnC,OAAsB,gBAATtC,IAAuBZ,KAAKiD,QAAQ4D,QAG7C7G,KAAKiD,QAAQ4D,OAAOpF,eAAeb,GAC9BZ,KAAKiD,QAAQ4D,OAAOjG,GAHpB8K,MNg4CJZ,GMj+Ca/H,UNo+CtBrD,GAAQoD,QM13COgI,GN83CT,SAAUnL,EAAQD,EAASS,GAEjC,YOnhDAR,GAAAD,SACAiM,WAAA,aACAC,cAAA,aACAC,MAAA,WACAC,YAAA,aACAC,OAAA,aACAC,OAAA,aACAC,QAAA,aACAC,OAAA,OACAC,gBAAA,aACAC,MAAA,SACAC,YAAA,YACAC,OAAA,WACAC,WAAA,aACAC,WAAA,YACAC,YAAA,WACAC,WAAA,YACAC,OAAA,YACAC,gBAAA,aACAC,UAAA,aACAC,SAAA,WACAC,MAAA,WACAC,UAAA,SACAC,UAAA,WACAC,eAAA,YACAC,UAAA,aACAC,WAAA,SACAC,UAAA,aACAC,WAAA,aACAC,aAAA,WACAC,gBAAA,WACAC,YAAA,WACAC,YAAA,YACAC,SAAA,SACAC,YAAA,aACAC,cAAA,aACAC,eAAA,WACAC,eAAA,UACAC,eAAA,UACAC,eAAA,WACAC,YAAA,WACAC,UAAA,YACAC,aAAA,WACAC,SAAA,aACAC,SAAA,aACAC,YAAA,YACAC,WAAA,WACAC,aAAA,aACAC,aAAA,WACAC,SAAA,WACAC,WAAA,aACAC,YAAA,aACAC,MAAA,WACAC,WAAA,YACAC,MAAA,aACAC,OAAA,SACAC,aAAA,YACAC,MAAA,aACAC,UAAA,aACAC,SAAA,aACAC,WAAA,WACAC,QAAA,UACAC,OAAA,aACAC,OAAA,aACAC,UAAA,aACAC,eAAA,aACAC,WAAA,WACAC,cAAA,aACAC,WAAA,aACAC,YAAA,aACAC,WAAA,aACAC,sBAAA,aACAC,WAAA,aACAC,YAAA,aACAC,WAAA,aACAC,WAAA,aACAC,aAAA,aACAC,eAAA,YACAC,cAAA,aACAC,gBAAA,aACAC,gBAAA,aACAC,gBAAA,aACAC,aAAA,aACAC,MAAA,SACAC,WAAA,WACAC,OAAA,aACAC,SAAA,WACAC,QAAA,SACAC,kBAAA,aACAC,YAAA,SACAC,cAAA,YACAC,cAAA,aACAC,gBAAA,YACAC,iBAAA,aACAC,mBAAA,WACAC,iBAAA,YACAC,iBAAA,YACAC,cAAA,WACAC,WAAA,aACAC,WAAA,aACAC,UAAA,aACAC,aAAA,aACAC,MAAA,SACAC,SAAA,aACAC,OAAA,WACAC,WAAA,YACAC,QAAA,WACAC,WAAA,UACAC,QAAA,aACAC,eAAA,aACAC,WAAA,aACAC,eAAA,aACAC,eAAA,aACAC,YAAA,aACAC,WAAA,aACAC,MAAA,YACAC,MAAA,aACAC,MAAA,aACAC,YAAA,aACAC,QAAA,WACAC,eAAA,YACAC,KAAA,SACAC,WAAA,aACAC,WAAA,YACAC,aAAA,WACAC,QAAA,aACAC,YAAA,YACAC,UAAA,WACAC,UAAA,aACAC,QAAA,WACAC,QAAA,aACAC,SAAA,aACAC,WAAA,YACAC,WAAA,aACAC,WAAA,aACAC,MAAA,aACAC,aAAA,WACAC,WAAA,YACAC,KAAA,aACAC,MAAA,WACAC,SAAA,aACAC,QAAA,WACAC,WAAA,YACAC,QAAA,aACAC,OAAA,aACAC,OAAA,aACAC,YAAA,aACAC,QAAA,WACAC,aAAA,cP4hDM,SAAUnV,EAAQD,EAASS,GQrgDjC,QAAA4U,GAAAC,EAAAC,GACA,MACA7N,MAAA8N,IAAAF,EAAA,GAAAC,EAAA,MACA7N,KAAA8N,IAAAF,EAAA,GAAAC,EAAA,MACA7N,KAAA8N,IAAAF,EAAA,GAAAC,EAAA,MAhLA,GAAAE,GAAkBhV,EAAQ,GAM1BiV,IACA,QAAA5S,KAAA2S,GACAA,EAAA1T,eAAAe,KACA4S,EAAAD,EAAA3S,MAIA,IAAA6S,GAAA1V,EAAAD,SACA4V,KAAOC,SAAA,EAAAC,OAAA,OACPC,KAAOF,SAAA,EAAAC,OAAA,OACPE,KAAOH,SAAA,EAAAC,OAAA,OACPG,KAAOJ,SAAA,EAAAC,OAAA,OACPI,MAAQL,SAAA,EAAAC,OAAA,QACRK,KAAON,SAAA,EAAAC,OAAA,OACPM,KAAOP,SAAA,EAAAC,OAAA,OACPO,KAAOR,SAAA,EAAAC,OAAA,OACPQ,KAAOT,SAAA,EAAAC,QAAA,QACPS,SAAWV,SAAA,EAAAC,QAAA,YACXU,QAAUX,SAAA,EAAAC,QAAA,WACVW,SAAWZ,SAAA,EAAAC,QAAA,YACXY,KAAOb,SAAA,EAAAC,QAAA,cACPa,OAASd,SAAA,EAAAC,QAAA,oBACTxG,MAAQuG,SAAA,EAAAC,QAAA,SAIR,QAAA3P,KAAAwP,GACA,GAAAA,EAAA5T,eAAAoE,GAAA,CACA,iBAAAwP,GAAAxP,IACA,SAAAxC,OAAA,8BAAAwC,EAGA,gBAAAwP,GAAAxP,IACA,SAAAxC,OAAA,oCAAAwC,EAGA,IAAAwP,EAAAxP,GAAA2P,OAAAnT,SAAAgT,EAAAxP,GAAA0P,SACA,SAAAlS,OAAA,sCAAAwC,EAGA,IAAA0P,GAAAF,EAAAxP,GAAA0P,SACAC,EAAAH,EAAAxP,GAAA2P,aACAH,GAAAxP,GAAA0P,eACAF,GAAAxP,GAAA2P,OACAzU,OAAAC,eAAAqU,EAAAxP,GAAA,YAAqD7D,MAAAuT,IACrDxU,OAAAC,eAAAqU,EAAAxP,GAAA,UAAmD7D,MAAAwT,IAInDH,EAAAC,IAAAG,IAAA,SAAAH,GACA,GAMA7Q,GACA9C,EACApB,EARA+V,EAAAhB,EAAA,OACAiB,EAAAjB,EAAA,OACAkB,EAAAlB,EAAA,OACAmB,EAAArP,KAAAqP,IAAAH,EAAAC,EAAAC,GACAE,EAAAtP,KAAAsP,IAAAJ,EAAAC,EAAAC,GACAG,EAAAD,EAAAD,CA+BA,OA1BAC,KAAAD,EACAhS,EAAA,EACE6R,IAAAI,EACFjS,GAAA8R,EAAAC,GAAAG,EACEJ,IAAAG,EACFjS,EAAA,GAAA+R,EAAAF,GAAAK,EACEH,IAAAE,IACFjS,EAAA,GAAA6R,EAAAC,GAAAI,GAGAlS,EAAA2C,KAAAqP,IAAA,GAAAhS,EAAA,KAEAA,EAAA,IACAA,GAAA,KAGAlE,GAAAkW,EAAAC,GAAA,EAGA/U,EADA+U,IAAAD,EACA,EACElW,GAAA,GACFoW,GAAAD,EAAAD,GAEAE,GAAA,EAAAD,EAAAD,IAGAhS,EAAA,IAAA9C,EAAA,IAAApB,IAGA8U,EAAAC,IAAAI,IAAA,SAAAJ,GACA,GAAAsB,GACAC,EACAC,EACArS,EACA9C,EAEA2U,EAAAhB,EAAA,OACAiB,EAAAjB,EAAA,OACAkB,EAAAlB,EAAA,OACA5Q,EAAA0C,KAAAsP,IAAAJ,EAAAC,EAAAC,GACAO,EAAArS,EAAA0C,KAAAqP,IAAAH,EAAAC,EAAAC,GACAQ,EAAA,SAAAtW,GACA,OAAAgE,EAAAhE,GAAA,EAAAqW,EAAA,GAyBA,OAtBA,KAAAA,EACAtS,EAAA9C,EAAA,GAEAA,EAAAoV,EAAArS,EACAkS,EAAAI,EAAAV,GACAO,EAAAG,EAAAT,GACAO,EAAAE,EAAAR,GAEAF,IAAA5R,EACAD,EAAAqS,EAAAD,EACGN,IAAA7R,EACHD,EAAA,IAAAmS,EAAAE,EACGN,IAAA9R,IACHD,EAAA,IAAAoS,EAAAD,GAEAnS,EAAA,EACAA,GAAA,EACGA,EAAA,IACHA,GAAA,KAKA,IAAAA,EACA,IAAA9C,EACA,IAAA+C,IAIA2Q,EAAAC,IAAAK,IAAA,SAAAL,GACA,GAAAgB,GAAAhB,EAAA,GACAiB,EAAAjB,EAAA,GACAkB,EAAAlB,EAAA,GACA7Q,EAAA4Q,EAAAC,IAAAG,IAAAH,GAAA,GACA2B,EAAA,MAAA7P,KAAAqP,IAAAH,EAAAlP,KAAAqP,IAAAF,EAAAC,GAIA,OAFAA,GAAA,QAAApP,KAAAsP,IAAAJ,EAAAlP,KAAAsP,IAAAH,EAAAC,KAEA/R,EAAA,IAAAwS,EAAA,IAAAT,IAGAnB,EAAAC,IAAAM,KAAA,SAAAN,GACA,GAGA5U,GACAD,EACAwU,EACAiC,EANAZ,EAAAhB,EAAA,OACAiB,EAAAjB,EAAA,OACAkB,EAAAlB,EAAA,MAWA,OALA4B,GAAA9P,KAAAqP,IAAA,EAAAH,EAAA,EAAAC,EAAA,EAAAC,GACA9V,GAAA,EAAA4V,EAAAY,IAAA,EAAAA,IAAA,EACAzW,GAAA,EAAA8V,EAAAW,IAAA,EAAAA,IAAA,EACAjC,GAAA,EAAAuB,EAAAU,IAAA,EAAAA,IAAA,GAEA,IAAAxW,EAAA,IAAAD,EAAA,IAAAwU,EAAA,IAAAiC,IAcA7B,EAAAC,IAAAW,QAAA,SAAAX,GACA,GAAA6B,GAAA/B,EAAAE,EACA,IAAA6B,EACA,MAAAA,EAGA,IACAC,GADAC,EAAAC,GAGA,QAAArB,KAAAd,GACA,GAAAA,EAAA1T,eAAAwU,GAAA,CACA,GAAAjU,GAAAmT,EAAAc,GAGAsB,EAAAxC,EAAAO,EAAAtT,EAGAuV,GAAAF,IACAA,EAAAE,EACAH,EAAAnB,GAKA,MAAAmB,IAGA/B,EAAAY,QAAAX,IAAA,SAAAW,GACA,MAAAd,GAAAc,IAGAZ,EAAAC,IAAAO,IAAA,SAAAP,GACA,GAAAgB,GAAAhB,EAAA,OACAiB,EAAAjB,EAAA,OACAkB,EAAAlB,EAAA,MAWA,OARAgB,KAAA,OAAAlP,KAAA8N,KAAAoB,EAAA,iBAAAA,EAAA,MACAC,IAAA,OAAAnP,KAAA8N,KAAAqB,EAAA,iBAAAA,EAAA,MACAC,IAAA,OAAApP,KAAA8N,KAAAsB,EAAA,iBAAAA,EAAA,OAMA,KAJA,MAAAF,EAAA,MAAAC,EAAA,MAAAC,GAIA,KAHA,MAAAF,EAAA,MAAAC,EAAA,MAAAC,GAGA,KAFA,MAAAF,EAAA,MAAAC,EAAA,MAAAC,KAKAnB,EAAAC,IAAAQ,IAAA,SAAAR,GACA,GAIA/U,GACAoE,EACA6R,EANAX,EAAAR,EAAAC,IAAAO,IAAAP,GACAN,EAAAa,EAAA,GACAZ,EAAAY,EAAA,GACA2B,EAAA3B,EAAA,EAiBA,OAZAb,IAAA,OACAC,GAAA,IACAuC,GAAA,QAEAxC,IAAA,QAAA5N,KAAA8N,IAAAF,EAAA,WAAAA,EAAA,OACAC,IAAA,QAAA7N,KAAA8N,IAAAD,EAAA,WAAAA,EAAA,OACAuC,IAAA,QAAApQ,KAAA8N,IAAAsC,EAAA,WAAAA,EAAA,OAEAjX,EAAA,IAAA0U,EAAA,GACAtQ,EAAA,KAAAqQ,EAAAC,GACAuB,EAAA,KAAAvB,EAAAuC,IAEAjX,EAAAoE,EAAA6R,IAGAnB,EAAAI,IAAAH,IAAA,SAAAG,GACA,GAGAgC,GACAC,EACAC,EACArC,EACAsC,EAPAnT,EAAAgR,EAAA,OACA9T,EAAA8T,EAAA,OACAlV,EAAAkV,EAAA,MAOA,QAAA9T,EAEA,MADAiW,GAAA,IAAArX,GACAqX,MAIAF,GADAnX,EAAA,GACAA,GAAA,EAAAoB,GAEApB,EAAAoB,EAAApB,EAAAoB,EAGA8V,EAAA,EAAAlX,EAAAmX,EAEApC,GAAA,MACA,QAAAhV,GAAA,EAAgBA,EAAA,EAAOA,IACvBqX,EAAAlT,EAAA,MAAAnE,EAAA,GACAqX,EAAA,GACAA,IAEAA,EAAA,GACAA,IAIAC,EADA,EAAAD,EAAA,EACAF,EAAA,GAAAC,EAAAD,GAAAE,EACG,EAAAA,EAAA,EACHD,EACG,EAAAC,EAAA,EACHF,GAAAC,EAAAD,IAAA,IAAAE,GAAA,EAEAF,EAGAnC,EAAAhV,GAAA,IAAAsX,CAGA,OAAAtC,IAGAD,EAAAI,IAAAC,IAAA,SAAAD,GACA,GAKAoC,GACAnT,EANAD,EAAAgR,EAAA,GACA9T,EAAA8T,EAAA,OACAlV,EAAAkV,EAAA,OACAqC,EAAAnW,EACAoW,EAAA3Q,KAAAsP,IAAAnW,EAAA,IAUA,OANAA,IAAA,EACAoB,GAAApB,GAAA,EAAAA,EAAA,EAAAA,EACAuX,GAAAC,GAAA,EAAAA,EAAA,EAAAA,EACArT,GAAAnE,EAAAoB,GAAA,EACAkW,EAAA,IAAAtX,EAAA,EAAAuX,GAAAC,EAAAD,GAAA,EAAAnW,GAAApB,EAAAoB,IAEA8C,EAAA,IAAAoT,EAAA,IAAAnT,IAGA2Q,EAAAK,IAAAJ,IAAA,SAAAI,GACA,GAAAjR,GAAAiR,EAAA,MACA/T,EAAA+T,EAAA,OACAhR,EAAAgR,EAAA,OACAsC,EAAA5Q,KAAA6Q,MAAAxT,GAAA,EAEAyT,EAAAzT,EAAA2C,KAAA6Q,MAAAxT,GACA/C,EAAA,IAAAgD,GAAA,EAAA/C,GACAwW,EAAA,IAAAzT,GAAA,EAAA/C,EAAAuW,GACAE,EAAA,IAAA1T,GAAA,EAAA/C,GAAA,EAAAuW,GAGA,QAFAxT,GAAA,IAEAsT,GACA,OACA,OAAAtT,EAAA0T,EAAA1W,EACA,QACA,OAAAyW,EAAAzT,EAAAhD,EACA,QACA,OAAAA,EAAAgD,EAAA0T,EACA,QACA,OAAA1W,EAAAyW,EAAAzT,EACA,QACA,OAAA0T,EAAA1W,EAAAgD,EACA,QACA,OAAAA,EAAAhD,EAAAyW,KAIA9C,EAAAK,IAAAD,IAAA,SAAAC,GACA,GAIAqC,GACAM,EACA9X,EANAkE,EAAAiR,EAAA,GACA/T,EAAA+T,EAAA,OACAhR,EAAAgR,EAAA,OACA4C,EAAAlR,KAAAsP,IAAAhS,EAAA,IAYA,OAPAnE,IAAA,EAAAoB,GAAA+C,EACAqT,GAAA,EAAApW,GAAA2W,EACAD,EAAA1W,EAAA2W,EACAD,GAAAN,GAAA,EAAAA,EAAA,EAAAA,EACAM,KAAA,EACA9X,GAAA,GAEAkE,EAAA,IAAA4T,EAAA,IAAA9X,IAIA8U,EAAAM,IAAAL,IAAA,SAAAK,GACA,GAIArV,GACAoE,EACAwT,EACA9W,EAPAqD,EAAAkR,EAAA,OACA4C,EAAA5C,EAAA,OACA6C,EAAA7C,EAAA,OACA8C,EAAAF,EAAAC,CAOAC,GAAA,IACAF,GAAAE,EACAD,GAAAC,GAGAnY,EAAA8G,KAAA6Q,MAAA,EAAAxT,GACAC,EAAA,EAAA8T,EACAN,EAAA,EAAAzT,EAAAnE,EAEA,MAAAA,KACA4X,EAAA,EAAAA,GAGA9W,EAAAmX,EAAAL,GAAAxT,EAAA6T,EAEA,IAAAjC,GACAC,EACAC,CACA,QAAAlW,GACA,QACA,OACA,OAAAgW,EAAA5R,EAAgB6R,EAAAnV,EAAOoV,EAAA+B,CAAQ,MAC/B,QAAAjC,EAAAlV,EAAgBmV,EAAA7R,EAAO8R,EAAA+B,CAAQ,MAC/B,QAAAjC,EAAAiC,EAAiBhC,EAAA7R,EAAO8R,EAAApV,CAAO,MAC/B,QAAAkV,EAAAiC,EAAiBhC,EAAAnV,EAAOoV,EAAA9R,CAAO,MAC/B,QAAA4R,EAAAlV,EAAgBmV,EAAAgC,EAAQ/B,EAAA9R,CAAO,MAC/B,QAAA4R,EAAA5R,EAAgB6R,EAAAgC,EAAQ/B,EAAApV,EAGxB,WAAAkV,EAAA,IAAAC,EAAA,IAAAC,IAGAnB,EAAAO,KAAAN,IAAA,SAAAM,GACA,GAIAU,GACAC,EACAC,EANA9V,EAAAkV,EAAA,OACAnV,EAAAmV,EAAA,OACAX,EAAAW,EAAA,OACAsB,EAAAtB,EAAA,MASA,OAJAU,GAAA,EAAAlP,KAAAqP,IAAA,EAAA/V,GAAA,EAAAwW,MACAX,EAAA,EAAAnP,KAAAqP,IAAA,EAAAhW,GAAA,EAAAyW,MACAV,EAAA,EAAApP,KAAAqP,IAAA,EAAAxB,GAAA,EAAAiC,OAEA,IAAAZ,EAAA,IAAAC,EAAA,IAAAC,IAGAnB,EAAAQ,IAAAP,IAAA,SAAAO,GACA,GAGAS,GACAC,EACAC,EALAxB,EAAAa,EAAA,OACAZ,EAAAY,EAAA,OACA2B,EAAA3B,EAAA,MA0BA,OArBAS,GAAA,OAAAtB,GAAA,OAAAC,GAAA,MAAAuC,EACAjB,GAAA,MAAAvB,EAAA,OAAAC,EAAA,MAAAuC,EACAhB,EAAA,MAAAxB,GAAA,KAAAC,EAAA,MAAAuC,EAGAlB,IAAA,SACA,MAAAlP,KAAA8N,IAAAoB,EAAA,YACA,MAAAA,EAEAC,IAAA,SACA,MAAAnP,KAAA8N,IAAAqB,EAAA,YACA,MAAAA,EAEAC,IAAA,SACA,MAAApP,KAAA8N,IAAAsB,EAAA,YACA,MAAAA,EAEAF,EAAAlP,KAAAqP,IAAArP,KAAAsP,IAAA,EAAAJ,GAAA,GACAC,EAAAnP,KAAAqP,IAAArP,KAAAsP,IAAA,EAAAH,GAAA,GACAC,EAAApP,KAAAqP,IAAArP,KAAAsP,IAAA,EAAAF,GAAA,IAEA,IAAAF,EAAA,IAAAC,EAAA,IAAAC,IAGAnB,EAAAQ,IAAAC,IAAA,SAAAD,GACA,GAGAtV,GACAoE,EACA6R,EALAxB,EAAAa,EAAA,GACAZ,EAAAY,EAAA,GACA2B,EAAA3B,EAAA,EAiBA,OAZAb,IAAA,OACAC,GAAA,IACAuC,GAAA,QAEAxC,IAAA,QAAA5N,KAAA8N,IAAAF,EAAA,WAAAA,EAAA,OACAC,IAAA,QAAA7N,KAAA8N,IAAAD,EAAA,WAAAA,EAAA,OACAuC,IAAA,QAAApQ,KAAA8N,IAAAsC,EAAA,WAAAA,EAAA,OAEAjX,EAAA,IAAA0U,EAAA,GACAtQ,EAAA,KAAAqQ,EAAAC,GACAuB,EAAA,KAAAvB,EAAAuC,IAEAjX,EAAAoE,EAAA6R,IAGAnB,EAAAS,IAAAD,IAAA,SAAAC,GACA,GAGAd,GACAC,EACAuC,EALAjX,EAAAuV,EAAA,GACAnR,EAAAmR,EAAA,GACAU,EAAAV,EAAA,EAKAb,IAAA1U,EAAA,QACAyU,EAAArQ,EAAA,IAAAsQ,EACAuC,EAAAvC,EAAAuB,EAAA,GAEA,IAAAkC,GAAAtR,KAAA8N,IAAAD,EAAA,GACA0D,EAAAvR,KAAA8N,IAAAF,EAAA,GACA4D,EAAAxR,KAAA8N,IAAAsC,EAAA,EASA,OARAvC,GAAAyD,EAAA,QAAAA,GAAAzD,EAAA,cACAD,EAAA2D,EAAA,QAAAA,GAAA3D,EAAA,cACAwC,EAAAoB,EAAA,QAAAA,GAAApB,EAAA,cAEAxC,GAAA,OACAC,GAAA,IACAuC,GAAA,SAEAxC,EAAAC,EAAAuC,IAGAnC,EAAAS,IAAAC,IAAA,SAAAD,GACA,GAGA+C,GACApU,EACA/D,EALAH,EAAAuV,EAAA,GACAnR,EAAAmR,EAAA,GACAU,EAAAV,EAAA,EAcA,OATA+C,GAAAzR,KAAA0R,MAAAtC,EAAA7R,GACAF,EAAA,IAAAoU,EAAA,EAAAzR,KAAA2R,GAEAtU,EAAA,IACAA,GAAA,KAGA/D,EAAA0G,KAAA4R,KAAArU,IAAA6R,MAEAjW,EAAAG,EAAA+D,IAGA4Q,EAAAU,IAAAD,IAAA,SAAAC,GACA,GAGApR,GACA6R,EACAqC,EALAtY,EAAAwV,EAAA,GACArV,EAAAqV,EAAA,GACAtR,EAAAsR,EAAA,EASA,OAJA8C,GAAApU,EAAA,MAAA2C,KAAA2R,GACApU,EAAAjE,EAAA0G,KAAA6R,IAAAJ,GACArC,EAAA9V,EAAA0G,KAAA8R,IAAAL,IAEAtY,EAAAoE,EAAA6R,IAGAnB,EAAAC,IAAAY,OAAA,SAAAjR,GACA,GAAAqR,GAAArR,EAAA,GACAsR,EAAAtR,EAAA,GACAuR,EAAAvR,EAAA,GACAjD,EAAA,IAAAkB,qBAAA,GAAAmS,EAAAC,IAAAI,IAAAzQ,GAAA,EAIA,SAFAjD,EAAAoF,KAAAhB,MAAApE,EAAA,KAGA,SAGA,IAAAmX,GAAA,IACA/R,KAAAhB,MAAAoQ,EAAA,QACApP,KAAAhB,MAAAmQ,EAAA,QACAnP,KAAAhB,MAAAkQ,EAAA,KAMA,OAJA,KAAAtU,IACAmX,GAAA,IAGAA,GAGA9D,EAAAK,IAAAQ,OAAA,SAAAjR,GAGA,MAAAoQ,GAAAC,IAAAY,OAAAb,EAAAK,IAAAJ,IAAArQ,KAAA,KAGAoQ,EAAAC,IAAAa,QAAA,SAAAlR,GACA,GAAAqR,GAAArR,EAAA,GACAsR,EAAAtR,EAAA,GACAuR,EAAAvR,EAAA,EAIA,OAAAqR,KAAAC,OAAAC,EACAF,EAAA,EACA,GAGAA,EAAA,IACA,IAGAlP,KAAAhB,OAAAkQ,EAAA,eAGA,GACA,GAAAlP,KAAAhB,MAAAkQ,EAAA,OACA,EAAAlP,KAAAhB,MAAAmQ,EAAA,OACAnP,KAAAhB,MAAAoQ,EAAA,QAKAnB,EAAAa,OAAAZ,IAAA,SAAArQ,GACA,GAAAf,GAAAe,EAAA,EAGA,QAAAf,GAAA,IAAAA,EAOA,MANAe,GAAA,KACAf,GAAA,KAGAA,IAAA,UAEAA,MAGA,IAAAkV,GAAA,SAAAnU,EAAA,IAKA,SAJA,EAAAf,GAAAkV,EAAA,KACAlV,GAAA,KAAAkV,EAAA,KACAlV,GAAA,KAAAkV,EAAA,MAKA/D,EAAAc,QAAAb,IAAA,SAAArQ,GAEA,GAAAA,GAAA,KACA,GAAAvE,GAAA,IAAAuE,EAAA,MACA,QAAAvE,OAGAuE,GAAA,EAEA,IAAAoU,EAKA,QAJAjS,KAAA6Q,MAAAhT,EAAA,UACAmC,KAAA6Q,OAAAoB,EAAApU,EAAA,aACAoU,EAAA,UAKAhE,EAAAC,IAAAU,IAAA,SAAA/Q,GACA,GAAAqU,KAAA,IAAAlS,KAAAhB,MAAAnB,EAAA,YACA,IAAAmC,KAAAhB,MAAAnB,EAAA,UACA,IAAAmC,KAAAhB,MAAAnB,EAAA,KAEAkB,EAAAmT,EAAAC,SAAA,IAAAhO,aACA,gBAAAiO,UAAArT,EAAA9D,QAAA8D,GAGAkP,EAAAW,IAAAV,IAAA,SAAArQ,GACA,GAAAuC,GAAAvC,EAAAsU,SAAA,IAAA/R,MAAA,2BACA,KAAAA,EACA,aAGA,IAAAiS,GAAAjS,EAAA,EAEA,KAAAA,EAAA,GAAAnF,SACAoX,IAAAC,MAAA,IAAAC,IAAA,SAAAC,GACA,MAAAA,OACGC,KAAA,IAGH,IAAAP,GAAAQ,SAAAL,EAAA,GAKA,QAJAH,GAAA,OACAA,GAAA,MACA,IAAAA,IAKAjE,EAAAC,IAAAc,IAAA,SAAAd,GACA,GAMAyE,GACAjU,EAPAwQ,EAAAhB,EAAA,OACAiB,EAAAjB,EAAA,OACAkB,EAAAlB,EAAA,OACAoB,EAAAtP,KAAAsP,IAAAtP,KAAAsP,IAAAJ,EAAAC,GAAAC,GACAC,EAAArP,KAAAqP,IAAArP,KAAAqP,IAAAH,EAAAC,GAAAC,GACAwD,EAAAtD,EAAAD,CAyBA,OApBAsD,GADAC,EAAA,EACAvD,GAAA,EAAAuD,GAEA,EAIAlU,EADAkU,GAAA,EACA,EAEAtD,IAAAJ,GACAC,EAAAC,GAAAwD,EAAA,EAEAtD,IAAAH,EACA,GAAAC,EAAAF,GAAA0D,EAEA,GAAA1D,EAAAC,GAAAyD,EAAA,EAGAlU,GAAA,EACAA,GAAA,GAEA,IAAAA,EAAA,IAAAkU,EAAA,IAAAD,IAGA1E,EAAAI,IAAAW,IAAA,SAAAX,GACA,GAAA9T,GAAA8T,EAAA,OACAlV,EAAAkV,EAAA,OACA/U,EAAA,EACAwX,EAAA,CAYA,OATAxX,GADAH,EAAA,GACA,EAAAoB,EAAApB,EAEA,EAAAoB,GAAA,EAAApB,GAGAG,EAAA,IACAwX,GAAA3X,EAAA,GAAAG,IAAA,EAAAA,KAGA+U,EAAA,OAAA/U,EAAA,IAAAwX,IAGA7C,EAAAK,IAAAU,IAAA,SAAAV,GACA,GAAA/T,GAAA+T,EAAA,OACAhR,EAAAgR,EAAA,OAEAhV,EAAAiB,EAAA+C,EACAwT,EAAA,CAMA,OAJAxX,GAAA,IACAwX,GAAAxT,EAAAhE,IAAA,EAAAA,KAGAgV,EAAA,OAAAhV,EAAA,IAAAwX,IAGA7C,EAAAe,IAAAd,IAAA,SAAAc,GACA,GAAA3R,GAAA2R,EAAA,OACA1V,EAAA0V,EAAA,OACAG,EAAAH,EAAA,MAEA,QAAA1V,EACA,WAAA6V,EAAA,IAAAA,EAAA,IAAAA,EAGA,IAAA0D,IAAA,OACAjC,EAAAvT,EAAA,IACAC,EAAAsT,EAAA,EACAf,EAAA,EAAAvS,EACAwV,EAAA,CAEA,QAAA9S,KAAA6Q,MAAAD,IACA,OACAiC,EAAA,KAAeA,EAAA,GAAAvV,EAAauV,EAAA,IAAa,MACzC,QACAA,EAAA,GAAAhD,EAAegD,EAAA,KAAaA,EAAA,IAAa,MACzC,QACAA,EAAA,KAAeA,EAAA,KAAaA,EAAA,GAAAvV,CAAa,MACzC,QACAuV,EAAA,KAAeA,EAAA,GAAAhD,EAAagD,EAAA,IAAa,MACzC,QACAA,EAAA,GAAAvV,EAAeuV,EAAA,KAAaA,EAAA,IAAa,MACzC,SACAA,EAAA,KAAeA,EAAA,KAAaA,EAAA,GAAAhD,EAK5B,MAFAiD,IAAA,EAAAxZ,GAAA6V,GAGA,KAAA7V,EAAAuZ,EAAA,GAAAC,GACA,KAAAxZ,EAAAuZ,EAAA,GAAAC,GACA,KAAAxZ,EAAAuZ,EAAA,GAAAC,KAIA7E,EAAAe,IAAAV,IAAA,SAAAU,GACA,GAAA1V,GAAA0V,EAAA,OACAG,EAAAH,EAAA,OAEA1R,EAAAhE,EAAA6V,GAAA,EAAA7V,GACAwX,EAAA,CAMA,OAJAxT,GAAA,IACAwT,EAAAxX,EAAAgE,IAGA0R,EAAA,OAAA8B,EAAA,IAAAxT,IAGA2Q,EAAAe,IAAAX,IAAA,SAAAW,GACA,GAAA1V,GAAA0V,EAAA,OACAG,EAAAH,EAAA,OAEA7V,EAAAgW,GAAA,EAAA7V,GAAA,GAAAA,EACAiB,EAAA,CASA,OAPApB,GAAA,GAAAA,EAAA,GACAoB,EAAAjB,GAAA,EAAAH,GAEAA,GAAA,IAAAA,EAAA,IACAoB,EAAAjB,GAAA,KAAAH,MAGA6V,EAAA,OAAAzU,EAAA,IAAApB,IAGA8U,EAAAe,IAAAT,IAAA,SAAAS,GACA,GAAA1V,GAAA0V,EAAA,OACAG,EAAAH,EAAA,OACA1R,EAAAhE,EAAA6V,GAAA,EAAA7V,EACA,QAAA0V,EAAA,QAAA1R,EAAAhE,GAAA,OAAAgE,KAGA2Q,EAAAM,IAAAS,IAAA,SAAAT,GACA,GAAAsB,GAAAtB,EAAA,OACAa,EAAAb,EAAA,OACAjR,EAAA,EAAA8R,EACA9V,EAAAgE,EAAAuS,EACAV,EAAA,CAMA,OAJA7V,GAAA,IACA6V,GAAA7R,EAAAhE,IAAA,EAAAA,KAGAiV,EAAA,OAAAjV,EAAA,IAAA6V,IAGAlB,EAAAgB,MAAAf,IAAA,SAAAe,GACA,OAAAA,EAAA,aAAAA,EAAA,aAAAA,EAAA,eAGAhB,EAAAC,IAAAe,MAAA,SAAAf,GACA,OAAAA,EAAA,aAAAA,EAAA,aAAAA,EAAA,eAGAD,EAAArG,KAAAsG,IAAA,SAAArQ,GACA,OAAAA,EAAA,WAAAA,EAAA,WAAAA,EAAA,aAGAoQ,EAAArG,KAAAyG,IAAAJ,EAAArG,KAAA0G,IAAA,SAAAzQ,GACA,WAAAA,EAAA,KAGAoQ,EAAArG,KAAA2G,IAAA,SAAA3G,GACA,aAAAA,EAAA,KAGAqG,EAAArG,KAAA4G,KAAA,SAAA5G,GACA,aAAAA,EAAA,KAGAqG,EAAArG,KAAA8G,IAAA,SAAA9G,GACA,OAAAA,EAAA,SAGAqG,EAAArG,KAAAgH,IAAA,SAAAhH,GACA,GAAA4I,GAAA,IAAAxQ,KAAAhB,MAAA4I,EAAA,YACAsK,GAAA1B,GAAA,KAAAA,GAAA,GAAAA,EAEAzR,EAAAmT,EAAAC,SAAA,IAAAhO,aACA,gBAAAiO,UAAArT,EAAA9D,QAAA8D,GAGAkP,EAAAC,IAAAtG,KAAA,SAAAsG,GAEA,QADAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,MACA,WR0rDM,SAAU3V,EAAQD,EAASS,GAEjC,YAaA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAVvF,GAAI0H,GAA4B,kBAAXC,SAAoD,gBAApBA,QAAOC,SAAwB,SAAU5H,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAX2H,SAAyB3H,EAAIuH,cAAgBI,QAAU3H,IAAQ2H,OAAOhJ,UAAY,eAAkBqB,IS/hFtQsX,EAAAha,EAAA,GTmiFIia,EAAgBvQ,EAAuBsQ,GSliF3CxX,EAAAxC,EAAA,GTsiFIyC,EAAWiH,EAAuBlH,GSpiFlC0X,EAAS,aAEb9W,WAAE8W,GAAUC,UAGZ/W,UAAEwB,GAAGsV,GAAU,SAAUE,GACvB,GAAIC,GAAStV,MAAM1D,UAAUiZ,MAAMja,KAAK0C,UAAW,GACjDwX,EAAmC,IAAhB1a,KAAKqC,OACxBsY,EAAc,KAEZC,EAAY5a,KAAK6a,KAAK,WACxB,GAAIC,IAAQ,EAAAlY,EAAAE,SAAE9C,MACZ+a,EAAOD,EAAME,KAAKX,GAClBpX,EAA8B,gBAAlB,KAAOsX,EAAP,YAAAhQ,EAAOgQ,IAAuBA,IAGvCQ,KACHA,EAAO,GAAIT,WAAYta,KAAMiD,GAC7B6X,EAAME,KAAKX,EAAQU,IAGhBL,IAILC,EAAcG,EAEQ,gBAAXP,KAGPI,EAFa,gBAAXJ,EAEYQ,EACLxX,UAAE0X,WAAWF,EAAKR,IAEbQ,EAAKR,GAAQlV,MAAM0V,EAAMP,GAGzBO,EAAKR,MAKzB,OAAOG,GAAkBC,EAAcC,GAGzCrX,UAAEwB,GAAGsV,GAAQjQ,YAAckQ,WT4iFrB,SAAU3a,EAAQD,EAASS,GAEjC,YAqDA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCApDhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MUpmFhiBiJ,EAAA5K,EAAA,GVwmFIuK,EAAcb,EAAuBkB,GUvmFzCmQ,EAAA/a,EAAA,GV2mFIgb,EAAYtR,EAAuBqR,GU1mFvCE,EAAAjb,EAAA,GV8mFIkb,EAAexR,EAAuBuR,GU7mF1CzY,EAAAxC,EAAA,GVinFIyC,EAAWiH,EAAuBlH,GUhnFtC2Y,EAAAnb,EAAA,IVonFIob,EAAkB1R,EAAuByR,GUnnF7CE,EAAArb,EAAA,IVunFIsb,EAAiB5R,EAAuB2R,GUtnF5CE,EAAAvb,EAAA,IV0nFIwb,EAAiB9R,EAAuB6R,GUznF5CE,EAAAzb,EAAA,IV6nFI0b,EAAiBhS,EAAuB+R,GU5nF5CE,EAAA3b,EAAA,IVgoFI4b,EAAkBlS,EAAuBiS,GU/nF7CE,EAAA7b,EAAA,IVmoFI8b,EAAiBpS,EAAuBmS,GUloF5CE,EAAA/b,EAAA,GVsoFIgc,EAActS,EAAuBqS,GUpoFrCE,EAAuB,EACvB5c,EAAwB,mBAATO,MAAuBA,SAA9BoD,GAKNmX,EV2oFY,WUrlFhB,QAAAA,GAAYlX,EAASH,GAASrB,EAAA5B,KAAAsa,GAC5B8B,GAAwB,EAKxBpc,KAAKqc,GAAKD,EAOVpc,KAAKsc,WACHC,MAAO,KACPjV,EAAG,MAQLtH,KAAKoD,SAAU,EAAAR,EAAAE,SAAEM,GACdoZ,SAAS,uBACTC,KAAK,sBAAuBzc,KAAKqc,IAKpCrc,KAAKiD,QAAUM,UAAE2H,QAAO,KAAUN,UAAU3H,EAASjD,KAAKoD,QAAQ4X,QAMlEhb,KAAK0c,UAAW,EAOhB1c,KAAKmJ,cAMLnJ,KAAKuI,WACwB,IAA3BvI,KAAKiD,QAAQsF,YACe,IAA3BvI,KAAKiD,QAAQsF,YAA8C,IAAxBvI,KAAKiD,QAAQqF,OAC/CtI,KAAKoD,QAAUpD,KAAKiD,QAAQsF,UAEhCvI,KAAKuI,WAAgC,IAAnBvI,KAAKuI,YAAuB,EAAA3F,EAAAE,SAAE9C,KAAKuI,WAKrDvI,KAAK2c,aAAe,GAAIC,WAAa5c,MAIrCA,KAAK6c,aAAe,GAAIC,WAAa9c,MAIrCA,KAAK+c,cAAgB,GAAIC,WAAchd,MAIvCA,KAAKid,aAAe,GAAIC,WAAald,KAAMR,GAI3CQ,KAAKmd,cAAgB,GAAIC,WAAcpd,MAIvCA,KAAKqd,aAAe,GAAIC,WAAatd,MAErCA,KAAKud,QAGL,EAAA3a,EAAAE,SAAES,UAAEC,MAAM,WAMRxD,KAAKwd,QAAQ,sBACZxd,OVi+FL,MAteAiC,GAAaqY,IACX9X,IAAK,QAQLrB,IAAK,WU1nFL,MAAOnB,MAAK6c,aAAa3Y,SVqoFzB1B,IAAK,SACLrB,IAAK,WU7nFL,MAAOnB,MAAK6c,aAAahY,UVwoFzBrC,IAAK,SACLrB,IAAK,WUhoFL,MAAOnB,MAAKmd,cAAcM,YV4oF1Bjb,IAAK,QAQLrB,IAAK,WUzrFL,MAAOkD,cVqsFP7B,IAAK,YACLrB,IAAK,WU5rFL,MAAO4B,eVkyFTd,EAAaqY,IACX9X,IAAK,OACLR,MAAO,WU5pFPhC,KAAKqd,aAAaK,OAGlB1d,KAAK2c,aAAae,OAGlB1d,KAAK2d,iBAGL3d,KAAK6c,aAAaa,OAGlB1d,KAAKmd,cAAcO,OAGnB1d,KAAK+c,cAAcW,OACnB1d,KAAKid,aAAaS,OAGlB1d,KAAKmd,cAAcS,SAGnB5d,KAAK6d,SAED7d,KAAK2c,aAAamB,cACpB9d,KAAK+d,aVwqFPvb,IAAK,iBACLR,MAAO,WUjqFQ,GAAAgJ,GAAAhL,IACVkF,OAAMyB,QAAQ3G,KAAKiD,QAAQkG,cAC9BnJ,KAAKiD,QAAQkG,eAGXnJ,KAAKiD,QAAQ2F,OACf5I,KAAKiD,QAAQkG,WAAWjC,MAAMtG,KAAM,aAItCZ,KAAKiD,QAAQkG,WAAWpC,QAAQ,SAACiX,GAC/BhT,EAAKiT,kBAAkB3D,EAAYnR,WAAW6U,EAAIpd,KAAK6G,eAAgBuW,EAAI/a,kBVgrF7ET,IAAK,oBACLR,MAAO,SUtqFSkc,GAA6B,GAAbC,GAAajb,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,MACzC8a,EAAM,GAAIE,GAAele,KAAMme,EAGnC,OADAne,MAAKmJ,WAAWjC,KAAK8W,GACdA,KVkrFPxb,IAAK,UACLR,MAAO,WU1qFP,GAAIkC,GAAQlE,KAAKkE,KAEjBlE,MAAK+c,cAAcqB,SACnBpe,KAAK2c,aAAayB,SAClBpe,KAAKid,aAAamB,SAClBpe,KAAK6c,aAAauB,SAClBpe,KAAKqd,aAAae,SAClBpe,KAAKmd,cAAciB,SAEnBpe,KAAKoD,QACFib,YAAY,uBACZC,WAAW,cAAe,SAC1Bla,IAAI,gBAOPpE,KAAKwd,QAAQ,qBAAsBtZ,MVorFnC1B,IAAK,OACLR,MAAO,SU3qFJsF,GACHtH,KAAKid,aAAasB,KAAKjX,MVsrFvB9E,IAAK,OACLR,MAAO,SU9qFJsF,GACHtH,KAAKid,aAAauB,KAAKlX,MV0rFvB9E,IAAK,SACLR,MAAO,SUjrFFsF,GACLtH,KAAKid,aAAawB,OAAOnX,MV4rFzB9E,IAAK,WACLR,MAAO,WUprFqB,GAArB0J,GAAqBxI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,KAClB0U,EAAM5X,KAAK6c,aAAa3Y,KAI5B,OAFA0T,GAAOA,YAAevT,WAAauT,EAAMlM,EAErCkM,YAAevT,WACVuT,EAAIzR,OAAOnG,KAAK6E,QAGlB+S,KVisFPpV,IAAK,WACLR,MAAO,SUzrFA4V,GACP,IAAI5X,KAAK8d,aAAT,CAGA,GAAIY,GAAK1e,KAAK6c,YAGX6B,GAAGC,YAAgB/G,GAAO8G,EAAGxa,MAAM0a,OAAOhH,KACzC8G,EAAGC,aAAe/G,IAMtB8G,EAAGxa,MAAQ0T,EAAM8G,EAAGG,YAAYjH,EAAK5X,KAAKiD,QAAQ8F,mBAAqB,KAOvE/I,KAAKwd,QAAQ,oBAAqBkB,EAAGxa,MAAO0T,GAG5C5X,KAAK6d,cVgsFLrb,IAAK,SACLR,MAAO,WUxrFHhC,KAAK6c,aAAa8B,WACpB3e,KAAK2c,aAAakB,SAElB7d,KAAK6c,aAAaiC,cAGpB9e,KAAKqd,aAAaQ,SAClB7d,KAAKmd,cAAcU,SAOnB7d,KAAKwd,QAAQ,wBVosFbhb,IAAK,SACLR,MAAO,WUjrFP,MAVAhC,MAAK2c,aAAaoC,SAClB/e,KAAK0c,UAAW,EAChB1c,KAAKyd,OAAOY,YAAY,wBAOxBre,KAAKwd,QAAQ,sBACN,KVusFPhb,IAAK,UACLR,MAAO,WUprFP,MAVAhC,MAAK2c,aAAaoB,UAClB/d,KAAK0c,UAAW,EAChB1c,KAAKyd,OAAOjB,SAAS,wBAOrBxc,KAAKwd,QAAQ,uBACN,KVwsFPhb,IAAK,YACLR,MAAO,WUjsFP,OAAQhC,KAAK8d,gBV2sFbtb,IAAK,aACLR,MAAO,WUpsFP,OAAyB,IAAlBhC,KAAK0c,YVitFZla,IAAK,UACLR,MAAO,SUxsFDgd,GAAuC,GAA5B9a,GAA4BhB,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAApB,KAAMlB,EAAckB,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,IACvClD,MAAKoD,QAAQoa,SACXyB,KAAMD,EACNhc,YAAahD,KACbkE,MAAOA,GAAgBlE,KAAKkE,MAC5BlC,MAAOA,GAAgBhC,KAAKwL,iBVgtFzB8O,IUrsFTA,GAAYnR,WAAa+V,UVktFzBxf,EAAQoD,QUhtFOwX,GVotFT,SAAU3a,EAAQD,EAASS,GAEjC,YAwBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GArBvF9B,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,IAETtC,EAAQoL,QAAUpL,EAAQyf,SAAWzf,EAAQ0f,QAAU1f,EAAQ2f,aAAWlc,EW/pG1E,IAAAmc,GAAAnf,EAAA,IXmqGIof,EAAa1V,EAAuByV,GWlqGxCE,EAAArf,EAAA,IXsqGIsf,EAAY5V,EAAuB2V,GWrqGvCE,EAAAvf,EAAA,IXyqGIwf,EAAa9V,EAAuB6V,GWxqGxCE,EAAAzf,EAAA,GX4qGI0f,EAAYhW,EAAuB+V,EAIvClgB,GW7qGE2f,mBX8qGF3f,EW9qGY0f,kBX+qGZ1f,EW/qGqByf,mBXgrGrBzf,EWhrG+BoL,kBXirG/BpL,EAAQoD,SW7qGNgd,SAAYT,UACZU,QAAWX,UACXY,SAAYb,UACZc,QAAWnV,YXmrGP,SAAUnL,EAAQD,EAASS,GAEjC,YAmBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAEhH,QAAS+H,GAA2B/J,EAAMS,GAAQ,IAAKT,EAAQ,KAAM,IAAIgK,gBAAe,4DAAgE,QAAOvJ,GAAyB,gBAATA,IAAqC,kBAATA,GAA8BT,EAAPS,EAElO,QAASwJ,GAAUC,EAAUC,GAAc,GAA0B,kBAAfA,IAA4C,OAAfA,EAAuB,KAAM,IAAInI,WAAU,iEAAoEmI,GAAeD,GAASzI,UAAYT,OAAOoJ,OAAOD,GAAcA,EAAW1I,WAAa4I,aAAepI,MAAOiI,EAAU/I,YAAY,EAAOqB,UAAU,EAAMtB,cAAc,KAAeiJ,IAAYnJ,OAAOsJ,eAAiBtJ,OAAOsJ,eAAeJ,EAAUC,GAAcD,EAASK,UAAYJ,GAtBjenJ,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MAE5hBoe,EAAO,QAAS/e,GAAIG,EAAQC,EAAU4e,GAA2B,OAAX7e,IAAiBA,EAAS8e,SAAS5e,UAAW,IAAI6e,GAAOtf,OAAOuf,yBAAyBhf,EAAQC,EAAW,QAAa4B,KAATkd,EAAoB,CAAE,GAAIE,GAASxf,OAAOkK,eAAe3J,EAAS,OAAe,QAAXif,MAAmB,GAAkCpf,EAAIof,EAAQhf,EAAU4e,GAAoB,GAAI,SAAWE,GAAQ,MAAOA,GAAKre,KAAgB,IAAInB,GAASwf,EAAKlf,GAAK,QAAegC,KAAXtC,EAA4C,MAAOA,GAAOL,KAAK2f,IYzsG5dzV,EAAAvK,EAAA,GZ6sGIwK,EAAcd,EAAuBa,GY5sGzC/H,EAAAxC,EAAA,GZgtGIyC,EAAWiH,EAAuBlH,GYzsGhC0c,EZwtGS,SAAUtU,GYvtGvB,QAAAsU,GAAYrc,GAA2B,GAAdC,GAAcC,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,KAAAtB,GAAA5B,KAAAqf,EAAA,IAAArU,GAAAlB,EAAA9J,MAAAqf,EAAA/U,WAAAvJ,OAAAkK,eAAAoU,IAAA7e,KAAAR,KAC/BgD,EAAaC,GADkB,OAMrC+H,GAAKwV,aAAe,EAChBxV,EAAKhI,YAAY2Z,aAAa8D,YAChCzV,EAAKhI,YAAY2Z,aAAa9T,MAAMvF,GAAG,yBAA0BC,UAAEC,MAAMwH,EAAK0V,cAAb1V,IAR9BA,EZm2GvC,MA3IAhB,GAAUqV,EAAUtU,GA0BpB9I,EAAaod,IACX7c,IAAK,MACLR,MAAO,SYnuGLgd,GAAoB,OAAA2B,GAAA3b,EAAA9B,UAAAb,OAAN4C,EAAMC,MAAAF,EAAA,EAAAA,EAAA,KAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAANF,EAAME,EAAA,GAAAjC,UAAAiC,EACtBnF,MAAKwgB,cAAgB,CAErB,IAAII,OAAiB5gB,KAAKwgB,aAAtB,iBAAmDxgB,KAAKgD,YAAYqZ,GAApE,KAA2E2C,EAA3E,KAEJ2B,EAAAE,SAAQjY,MAARvD,MAAAsb,GAAcC,GAAdE,OAA6B7b,IAY7BjF,KAAKgD,YAAYI,QAAQoa,SACvByB,KAAM,mBACNjc,YAAahD,KAAKgD,YAClBkB,MAAOlE,KAAKkE,MACZlC,MAAO,KACP4G,OACEkX,SAAU9f,KACVgf,UAAWA,EACX+B,QAAS9b,EACT2b,WAAYA,QZ8uGhBpe,IAAK,eACLR,MAAO,SY1uGIkC,GAAyB,GAAlBkH,KAAkBlI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,KAAAA,UAAA,EAEpC,OADAlD,MAAKghB,IAAI,iBAAkB9c,EAAOkH,IAC3B,KZ+uGP5I,IAAK,WACLR,MAAO,SY7uGAmC,GAEP,MADAnE,MAAKghB,IAAI,qBACTd,EAAAb,EAAA7d,UAAA8I,WAAAvJ,OAAAkK,eAAAoU,EAAA7d,WAAA,WAAAxB,MAAAQ,KAAAR,KAAsBmE,MZgvGtB3B,IAAK,YACLR,MAAO,SY9uGCmC,GAQR,MAPAnE,MAAKghB,IAAI,sBACThhB,KAAKwgB,aAAe,EAEhBxgB,KAAKgD,YAAY2Z,aAAa8D,YAChCzgB,KAAKgD,YAAY2Z,aAAa9T,MAAMzE,IAAI,oBAG1C8b,EAAAb,EAAA7d,UAAA8I,WAAAvJ,OAAAkK,eAAAoU,EAAA7d,WAAA,YAAAxB,MAAAQ,KAAAR,KAAuBmE,MZivGvB3B,IAAK,WACLR,MAAO,SY/uGAmC,GACPnE,KAAKghB,IAAI,wBZwvGTxe,IAAK,gBACLR,MAAO,SYlvGKmC,GACZnE,KAAKghB,IAAI,2BAA4B7c,EAAMnC,MAAOmC,EAAMD,UZqvGxD1B,IAAK,WACLR,MAAO,SYnvGAmC,GACPnE,KAAKghB,IAAI,oBAAqB7c,EAAMnC,MAAOmC,EAAMD,UZsvGjD1B,IAAK,YACLR,MAAO,SYpvGCmC,GACRnE,KAAKghB,IAAI,qBAAsB7c,EAAMnC,MAAOmC,EAAMD,UZuvGlD1B,IAAK,SACLR,MAAO,SYrvGFmC,GACLnE,KAAKghB,IAAI,mBACThhB,KAAKwgB,aAAe,KZwvGpBhe,IAAK,SACLR,MAAO,SYtvGFmC,GACLnE,KAAKghB,IAAI,sBZyvGTxe,IAAK,YACLR,MAAO,SYvvGCmC,GACRnE,KAAKghB,IAAI,yBZ0vGTxe,IAAK,WACLR,MAAO,SYxvGAmC,GACPnE,KAAKghB,IAAI,yBZ4vGJ3B,GYp2Gctc,UZu2GvBrD,GAAQoD,QY3vGOuc,GZ+vGT,SAAU1f,EAAQD,EAASS,GAEjC,YAmBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAEhH,QAAS+H,GAA2B/J,EAAMS,GAAQ,IAAKT,EAAQ,KAAM,IAAIgK,gBAAe,4DAAgE,QAAOvJ,GAAyB,gBAATA,IAAqC,kBAATA,GAA8BT,EAAPS,EAElO,QAASwJ,GAAUC,EAAUC,GAAc,GAA0B,kBAAfA,IAA4C,OAAfA,EAAuB,KAAM,IAAInI,WAAU,iEAAoEmI,GAAeD,GAASzI,UAAYT,OAAOoJ,OAAOD,GAAcA,EAAW1I,WAAa4I,aAAepI,MAAOiI,EAAU/I,YAAY,EAAOqB,UAAU,EAAMtB,cAAc,KAAeiJ,IAAYnJ,OAAOsJ,eAAiBtJ,OAAOsJ,eAAeJ,EAAUC,GAAcD,EAASK,UAAYJ,GAtBjenJ,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MAE5hBoe,EAAO,QAAS/e,GAAIG,EAAQC,EAAU4e,GAA2B,OAAX7e,IAAiBA,EAAS8e,SAAS5e,UAAW,IAAI6e,GAAOtf,OAAOuf,yBAAyBhf,EAAQC,EAAW,QAAa4B,KAATkd,EAAoB,CAAE,GAAIE,GAASxf,OAAOkK,eAAe3J,EAAS,OAAe,QAAXif,MAAmB,GAAkCpf,EAAIof,EAAQhf,EAAU4e,GAAoB,GAAI,SAAWE,GAAQ,MAAOA,GAAKre,KAAgB,IAAInB,GAASwf,EAAKlf,GAAK,QAAegC,KAAXtC,EAA4C,MAAOA,GAAOL,KAAK2f,Ia93G5dzV,EAAAvK,EAAA,Gbk4GIwK,EAAcd,EAAuBa,Gaj4GzC/H,EAAAxC,EAAA,Gbq4GIyC,EAAWiH,EAAuBlH,Ga/3GhCyc,Eb64GQ,SAAUrU,Ga54GtB,QAAAqU,GAAYpc,GAA2B,GAAdC,GAAcC,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,KAAAtB,GAAA5B,KAAAof,EAAA,IAAApU,GAAAlB,EAAA9J,MAAAof,EAAA9U,WAAAvJ,OAAAkK,eAAAmU,IAAA5e,KAAAR,KAC/BgD,EAAaO,UAAE2H,QAAO,MAExBhC,SAAU,iEACVE,UAAU,EACVvE,OAAQ7B,EAAY6B,QAEtB5B,IAPmC,OAUrC+H,GAAK5H,SAAU,EAAAR,EAAAE,SAAEkI,EAAK/H,QAAQiG,UAC9B8B,EAAKiW,aAAejW,EAAK5H,QAAQ8d,KAAK,OAXDlW,Eb67GvC,MAhDAhB,GAAUoV,EAASrU,GAkBnB9I,EAAamd,IACX5c,IAAK,WACLR,MAAO,San5GAmC,GACP+b,EAAAd,EAAA5d,UAAA8I,WAAAvJ,OAAAkK,eAAAmU,EAAA5d,WAAA,WAAAxB,MAAAQ,KAAAR,KAAemE,GACfnE,KAAKgD,YAAYya,OAAO0D,OAAOnhB,KAAKoD,Ybs5GpCZ,IAAK,WACLR,MAAO,Sap5GAmC,GAGP,GAFA+b,EAAAd,EAAA5d,UAAA8I,WAAAvJ,OAAAkK,eAAAmU,EAAA5d,WAAA,WAAAxB,MAAAQ,KAAAR,KAAemE,IAEVA,EAAMD,MAKT,WAJAlE,MAAKihB,aACFG,IAAI,kBAAmB,MACvBA,IAAI,QAAS,MACbC,KAAK,GAIVrhB,MAAKihB,aACFG,IAAI,kBAAmBjd,EAAMD,MAAMod,eAElCthB,KAAKiD,QAAQmG,WACfpJ,KAAKihB,aACFI,KAAKld,EAAMD,MAAMiC,OAAOnG,KAAKiD,QAAQ4B,QAAU7E,KAAKgD,YAAY6B,SAE/DV,EAAMD,MAAMqC,UAAapC,EAAMD,MAAM8B,MAAQ,GAC/ChG,KAAKihB,aAAaG,IAAI,QAAS,SAE/BphB,KAAKihB,aAAaG,IAAI,QAAS,cbq5G9BhC,Ga97Garc,Ubi8GtBrD,GAAQoD,Qal5GOsc,Gbs5GT,SAAUzf,EAAQD,EAASS,GAEjC,YAmBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAEhH,QAAS+H,GAA2B/J,EAAMS,GAAQ,IAAKT,EAAQ,KAAM,IAAIgK,gBAAe,4DAAgE,QAAOvJ,GAAyB,gBAATA,IAAqC,kBAATA,GAA8BT,EAAPS,EAElO,QAASwJ,GAAUC,EAAUC,GAAc,GAA0B,kBAAfA,IAA4C,OAAfA,EAAuB,KAAM,IAAInI,WAAU,iEAAoEmI,GAAeD,GAASzI,UAAYT,OAAOoJ,OAAOD,GAAcA,EAAW1I,WAAa4I,aAAepI,MAAOiI,EAAU/I,YAAY,EAAOqB,UAAU,EAAMtB,cAAc,KAAeiJ,IAAYnJ,OAAOsJ,eAAiBtJ,OAAOsJ,eAAeJ,EAAUC,GAAcD,EAASK,UAAYJ,GAtBjenJ,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MAE5hBoe,EAAO,QAAS/e,GAAIG,EAAQC,EAAU4e,GAA2B,OAAX7e,IAAiBA,EAAS8e,SAAS5e,UAAW,IAAI6e,GAAOtf,OAAOuf,yBAAyBhf,EAAQC,EAAW,QAAa4B,KAATkd,EAAoB,CAAE,GAAIE,GAASxf,OAAOkK,eAAe3J,EAAS,OAAe,QAAXif,MAAmB,GAAkCpf,EAAIof,EAAQhf,EAAU4e,GAAoB,GAAI,SAAWE,GAAQ,MAAOA,GAAKre,KAAgB,IAAInB,GAASwf,EAAKlf,GAAK,QAAegC,KAAXtC,EAA4C,MAAOA,GAAOL,KAAK2f,Icv9G5dN,EAAA1f,EAAA,Gd29GIohB,EAAY1X,EAAuBgW,Gc19GvCld,EAAAxC,EAAA,Gd89GIyC,EAAWiH,EAAuBlH,Gc59GlCiI,GACF4W,8JAGAC,eAAgB,+EAOZtC,Edq+GS,SAAUS,Gcp+GvB,QAAAT,GAAYnc,GAA2B,GAAdC,GAAcC,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,KAAAtB,GAAA5B,KAAAmf,EAAA,IAAAnU,GAAAlB,EAAA9J,MAAAmf,EAAA7U,WAAAvJ,OAAAkK,eAAAkU,IAAA3e,KAAAR,KAC/BgD,EAAaO,UAAE2H,QAAO,KAAUN,EAAU3H,IADX,OAErC+H,GAAK5H,QAAU,KAFsB4H,EdiiHvC,MA5DAhB,GAAUmV,EAAUS,GAapB3d,EAAakd,IACX3c,IAAK,YACLR,MAAO,Wc9+GP,MAAOhC,MAAKqL,YAAc,Kdk/G1B7I,IAAK,WACLR,MAAO,Sch/GAmC,GACP+b,EAAAf,EAAA3d,UAAA8I,WAAAvJ,OAAAkK,eAAAkU,EAAA3d,WAAA,WAAAxB,MAAAQ,KAAAR,KAAemE,GAEVnE,KAAK0hB,cAIV1hB,KAAKoD,SAAU,EAAAR,EAAAE,SAAE9C,KAAKiD,QAAQue,aAC9BxhB,KAAK2hB,OACL3hB,KAAKgD,YAAYya,OAAO0D,OAAOnhB,KAAKoD,adm/GpCZ,IAAK,OACLR,MAAO,Wcj/GF,GAAA4f,GAAA5hB,KACDgD,EAAchD,KAAKgD,YACrB6e,EAAkB7hB,KAAKoD,QAAQ8d,KAAK,gCACpCY,GAA4C,IAA/B9hB,KAAKiD,QAAQ4H,gBAA4B3F,MAAMyB,QAAQ3G,KAAK6G,OAE3Egb,GAAgBE,QAEhBxe,UAAEsX,KAAK7a,KAAK6G,OAAQ,SAACjG,EAAMoB,GACzB,GAAIggB,IAAU,EAAApf,EAAAE,SAAE8e,EAAK3e,QAAQwe,gBAC1BhF,KAAK,YAAa7b,GAClB6b,KAAK,aAAcza,GACnBya,KAAK,QAASqF,EAAelhB,EAAf,KAAwBoB,EAAUA,GAChDsB,GAAG,+CACF,SAAUgE,GACR,GAAI2a,IAAM,EAAArf,EAAAE,SAAE9C,KAIZgD,GAAYkf,SAASJ,EAAYG,EAAIxF,KAAK,aAAewF,EAAIxF,KAAK,gBAIxEuF,GAAQd,KAAK,8BACVE,IAAI,mBAAoBpf,GAE3B6f,EAAgBV,OAAOa,KAGzBH,EAAgBV,QAAO,EAAAve,EAAAE,SAAE,0Cdg/GpBqc,GcliHcrU,UdqiHvBpL,GAAQoD,Qc/+GOqc,Gdm/GT,SAAUxf,EAAQD,EAASS,GAEjC,YAeA,SAASyB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAZhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MehkHhiBa,EAAAxC,EAAA,GfokHIyC,EAEJ,SAAgCC,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,IAFjDF,Ge9jHhCqa,EfwkHc,WepkHlB,QAAAA,GAAYha,GAAapB,EAAA5B,KAAAgd,GAIvBhd,KAAKgD,YAAcA,EAKnBhD,KAAKmiB,cAAgB,KAKrBniB,KAAKoiB,cACHC,KAAM,EACNC,IAAK,GAMPtiB,KAAKuiB,OAAShf,UAAEC,MAAMxD,KAAKwiB,cAAexiB,MfmyH5C,MA7MAiC,GAAa+a,IACXxa,IAAK,gBACLR,MAAO,Se9kHKsgB,EAAKD,GACjB,GAAKriB,KAAKmiB,cAAV,CAIA,GAAIM,GAASziB,KAAKmiB,cAAeO,EAAK1iB,KAAKgD,YAAa0b,EAAKgE,EAAG7F,aAG5D3Y,EAASwa,EAAGC,WAAqCD,EAAGxa,MAAMye,WAAjCjE,EAAGkE,kBAGhCH,GAAOI,WAAWR,KAAOA,EAAO,KAChCI,EAAOI,WAAWP,IAAMA,EAAM,KAG1BG,EAAOhZ,UACTvF,EAAMue,EAAOhZ,UAAU4Y,EAAOI,EAAOlZ,SAEnCkZ,EAAO/Y,SACTxF,EAAMue,EAAO/Y,SAAS4Y,EAAMG,EAAOjZ,QAIrCkZ,EAAGR,SAAShe,GACZwe,EAAGzF,aAAa6F,YfwlHhBtgB,IAAK,OACLR,MAAO,WellHP,GAAIqH,GAAUrJ,KAAKgD,YAAYC,QAAQoF,WAAarI,KAAKgD,YACtDC,QAAQ2G,YAAc5J,KAAKgD,YAAYC,QAAQoG,QAC9C0Z,IAEJ,KAAK,GAAIC,KAAc3Z,GAChBA,EAAQ5H,eAAeuhB,IAI5BD,EAAc7b,KAAKmC,EAAQ2Z,GAAY1Z,SAGzCtJ,MAAKgD,YAAYya,OAAOyD,KAAK6B,EAAclJ,KAAK,OAC7CvW,GAAG,+CAAgDC,UAAEC,MAAMxD,KAAKijB,QAASjjB,UfylH5EwC,IAAK,SACLR,MAAO,YenlHP,EAAAY,EAAAE,SAAE9C,KAAKgD,YAAYya,QAAQrZ,KACzB8e,wBAAyB3f,UAAEC,MAAMxD,KAAKmjB,MAAOnjB,MAC7CojB,wBAAyB7f,UAAEC,MAAMxD,KAAKmjB,MAAOnjB,MAC7CqjB,sBAAuB9f,UAAEC,MAAMxD,KAAKsjB,SAAUtjB,MAC9CujB,uBAAwBhgB,UAAEC,MAAMxD,KAAKsjB,SAAUtjB,WfimHjDwC,IAAK,UACLR,MAAO,SevlHDsF,GACN,IAAItH,KAAKgD,YAAY8a,aAArB,CAGA9d,KAAKgD,YAAYsZ,UAAUC,MAAQ,UACnCvc,KAAKgD,YAAYsZ,UAAUhV,EAAIA,GAE1BA,EAAEkc,QAAUlc,EAAEmc,OAASnc,EAAEoc,eAAiBpc,EAAEoc,cAAcC,UAC7Drc,EAAEkc,MAAQlc,EAAEoc,cAAcC,QAAQ,GAAGH,MACrClc,EAAEmc,MAAQnc,EAAEoc,cAAcC,QAAQ,GAAGF,MAKvC,IAAIthB,IAAS,EAAAS,EAAAE,SAAEwE,EAAEnF,QAGbyhB,EAAOzhB,EAAO0hB,QAAQ,OACtBxa,EAAUrJ,KAAKgD,YAAYC,QAAQoF,WAAarI,KAAKgD,YACtDC,QAAQ2G,YAAc5J,KAAKgD,YAAYC,QAAQoG,OAElD,KAAIua,EAAKE,GAAG,gBAAZ,CAIA9jB,KAAKmiB,cAAgB,IAErB,KAAK,GAAIa,KAAc3Z,GACrB,GAAKA,EAAQ5H,eAAeuhB,GAA5B,CAIA,GAAIP,GAASpZ,EAAQ2Z,EAErB,IAAIY,EAAKE,GAAGrB,EAAOnZ,UAAW,CAC5BtJ,KAAKmiB,cAAgB5e,UAAE2H,UAAWuX,GAAS7hB,KAAMoiB,GACjD,OACK,OAA6B7f,KAAzBsf,EAAO9Y,eAA+Bia,EAAKE,GAAGrB,EAAO9Y,eAAgB,CAC9E3J,KAAKmiB,cAAgB5e,UAAE2H,UAAWuX,GAAS7hB,KAAMoiB,IACjDY,EAAOA,EAAKrD,QACZ,QAIJ,GAAIwD,GAAQH,EAAK1C,KAAK,sBAAsB/f,IAAI,EAEhD,IAA2B,OAAvBnB,KAAKmiB,eAAoC,OAAV4B,EAAnC,CAIA,GAAIC,GAASJ,EAAKI,QAGlBhkB,MAAKmiB,cAAcU,WAAakB,EAAME,MACtCjkB,KAAKmiB,cAAcE,KAAO/a,EAAEkc,MAAQQ,EAAO3B,KAC3CriB,KAAKmiB,cAAcG,IAAMhb,EAAEmc,MAAQO,EAAO1B,IAC1CtiB,KAAKoiB,cACHC,KAAM/a,EAAEkc,MACRlB,IAAKhb,EAAEmc,QAUT,EAAA7gB,EAAAE,SAAE9C,KAAKgD,YAAYya,QAAQna,IACzB4f,wBAAyB3f,UAAEC,MAAMxD,KAAKmjB,MAAOnjB,MAC7CojB,wBAAyB7f,UAAEC,MAAMxD,KAAKmjB,MAAOnjB,MAC7CqjB,sBAAuB9f,UAAEC,MAAMxD,KAAKsjB,SAAUtjB,MAC9CujB,uBAAwBhgB,UAAEC,MAAMxD,KAAKsjB,SAAUtjB,QAC9Cwd,QAAQ,mBfimHXhb,IAAK,QACLR,MAAO,SezlHHsF,GACJtH,KAAKgD,YAAYsZ,UAAUC,MAAQ,QACnCvc,KAAKgD,YAAYsZ,UAAUhV,EAAIA,GAE1BA,EAAEkc,QAAUlc,EAAEmc,OAASnc,EAAEoc,eAAiBpc,EAAEoc,cAAcC,UAC7Drc,EAAEkc,MAAQlc,EAAEoc,cAAcC,QAAQ,GAAGH,MACrClc,EAAEmc,MAAQnc,EAAEoc,cAAcC,QAAQ,GAAGF,OAIvCnc,EAAE4c,gBAEF,IAAI7B,GAAOjb,KAAKsP,IACd,EACAtP,KAAKqP,IACHzW,KAAKmiB,cAAc5Y,QACnBvJ,KAAKmiB,cAAcE,OAAS/a,EAAEkc,OAASxjB,KAAKoiB,aAAaC,MAAQriB,KAAKoiB,aAAaC,QAInFC,EAAMlb,KAAKsP,IACb,EACAtP,KAAKqP,IACHzW,KAAKmiB,cAAc3Y,OACnBxJ,KAAKmiB,cAAcG,MAAQhb,EAAEmc,OAASzjB,KAAKoiB,aAAaE,KAAOtiB,KAAKoiB,aAAaE,MAIrFtiB,MAAKuiB,OAAOD,EAAKD,MfwlHjB7f,IAAK,WACLR,MAAO,SehlHAsF,GACPtH,KAAKgD,YAAYsZ,UAAUC,MAAQ,WACnCvc,KAAKgD,YAAYsZ,UAAUhV,EAAIA,GAK/B,EAAA1E,EAAAE,SAAE9C,KAAKgD,YAAYya,QAAQrZ,KACzB8e,wBAAyBljB,KAAKmjB,MAC9BC,wBAAyBpjB,KAAKmjB,MAC9BE,sBAAuBrjB,KAAKsjB,SAC5BC,uBAAwBvjB,KAAKsjB,efqlH1BtG,IAGTtd,GAAQoD,QenlHOka,GfulHT,SAAUrd,EAAQD,EAASS,GAEjC,YAiBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAhBhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MgBn1HhiBa,EAAAxC,EAAA,GhBu1HIyC,EAAWiH,EAAuBlH,GgBt1HtCuY,EAAA/a,EAAA,GhB01HIgb,EAAYtR,EAAuBqR,GgBp1HjCgC,EhB81Ha,WgBz1HjB,QAAAA,GAAYla,EAAaxD,GAAMoC,EAAA5B,KAAAkd,GAI7Bld,KAAKR,KAAOA,EAIZQ,KAAKgD,YAAcA,EAInBhD,KAAKmkB,cAAgB,KAIrBnkB,KAAKokB,WAAa,KAMlBpkB,KAAKqkB,UAAW,EAIhBrkB,KAAKskB,SAAU,EAIftkB,KAAKukB,SAAU,EhB+uIjB,MAtYAtiB,GAAaib,IACX1a,IAAK,OAOLR,MAAO,WgBj0HP,GAAI0gB,GAAK1iB,KAAKgD,WAEd,IAAI0f,EAAGzf,QAAQqF,OAEb,WADAoa,GAAGjF,OAAOjB,SAAS,yCAIrBkG,GAAGjF,OAAOjB,SAAS,yCAGdxc,KAAKygB,UAAazgB,KAAKwkB,YAKxB9B,EAAGzf,QAAQuF,SACbxI,KAAKykB,gBAIHzkB,KAAKwkB,WAEFxkB,KAAK8I,MAAM2T,KAAK,aACnBzc,KAAK8I,MAAM2T,KAAK,WAAY,GAG9Bzc,KAAK8I,MAAMxF,IACTohB,+CAAgDnhB,UAAEC,MAAMxD,KAAKye,OAAQze,QAGvEA,KAAK8I,MAAMxF,IACTqhB,oBAAqBphB,UAAEC,MAAMxD,KAAKue,KAAMve,QAG1CA,KAAK8I,MAAMxF,IACTshB,uBAAwBrhB,UAAEC,MAAMxD,KAAKwe,KAAMxe,SAK3CA,KAAKygB,WAAazgB,KAAKwkB,WACzBxkB,KAAK6I,MAAMvF,IACTohB,+CAAgDnhB,UAAEC,MAAMxD,KAAKue,KAAMve,MACnE2kB,oBAAqBphB,UAAEC,MAAMxD,KAAKue,KAAMve,QAG1CA,KAAK6I,MAAMvF,IACTshB,uBAAwBrhB,UAAEC,MAAMxD,KAAKwe,KAAMxe,UAK/C,EAAA4C,EAAAE,SAAE9C,KAAKR,MAAM8D,GAAG,qBAAsBC,UAAEC,MAAMxD,KAAK6kB,WAAY7kB,WhB00H/DwC,IAAK,SACLR,MAAO,WgBp0HHhC,KAAKygB,WACPzgB,KAAK6I,MAAMzE,KACTsgB,+CAAgDnhB,UAAEC,MAAMxD,KAAKue,KAAMve,MACnE2kB,oBAAqBphB,UAAEC,MAAMxD,KAAKue,KAAMve,QAE1CA,KAAK6I,MAAMzE,KACTwgB,uBAAwBrhB,UAAEC,MAAMxD,KAAKwe,KAAMxe,SAI3CA,KAAKwkB,WACPxkB,KAAK8I,MAAM1E,KACTsgB,+CAAgDnhB,UAAEC,MAAMxD,KAAKye,OAAQze,QAEvEA,KAAK8I,MAAM1E,KACTugB,oBAAqBphB,UAAEC,MAAMxD,KAAKue,KAAMve,QAE1CA,KAAK8I,MAAM1E,KACTwgB,uBAAwBrhB,UAAEC,MAAMxD,KAAKwe,KAAMxe,SAI3CA,KAAKmkB,eACPnkB,KAAKmkB,cAAc3b,QAAQ,YAG7B,EAAA5F,EAAAE,SAAE9C,KAAKR,MAAM4E,IAAI,qBAAsBb,UAAEC,MAAMxD,KAAK6kB,WAAY7kB,QAChE,EAAA4C,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAU1gB,IAAI,+CAAgDb,UAAEC,MAAMxD,KAAKwe,KAAMxe,QAC7F,EAAA4C,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAU1gB,IAAI,+CAAgDb,UAAEC,MAAMxD,KAAK+kB,iBAAkB/kB,UhBw0HzGwC,IAAK,mBACLR,MAAO,SgBt0HQsF,GACf,QAAKA,IAKHtH,KAAKglB,aAAahlB,KAAKokB,WAAY9c,EAAE2d,gBACrCjlB,KAAKglB,aAAahlB,KAAKokB,WAAY9c,EAAEnF,SACrCnC,KAAKglB,aAAahlB,KAAKgD,YAAYya,OAAQnW,EAAE2d,gBAC7CjlB,KAAKglB,aAAahlB,KAAKgD,YAAYya,OAAQnW,EAAEnF,YhBq0H/CK,IAAK,eACLR,MAAO,SgBl0HIuG,EAAWnF,GACtB,SAAKmF,IAAcnF,KAInBA,GAAU,EAAAR,EAAAE,SAAEM,GAGVA,EAAQ0gB,GAAGvb,IACXA,EAAU2Y,KAAK9d,GAASf,OAAS,MhBm0HnCG,IAAK,mBACLR,MAAO,SgBh0HQsF,GACftH,KAAKqkB,SAAWrkB,KAAKklB,iBAAiB5d,MhBm0HtC9E,IAAK,gBACLR,MAAO,WgBh0HP,GAAI0gB,GAAK1iB,KAAKgD,WAEdhD,MAAKmkB,cAAgBnkB,KAAKwkB,SAAWxkB,KAAK8I,MAAQ9I,KAAK6I,MAEvD6Z,EAAGjF,OAAOjB,SAAS,kCAEnBxc,KAAKmkB,cAAc3b,QACjBjF,UAAE2H,QACA,KAEAia,UAAU3c,QACVka,EAAGzf,QAAQuF,SACVgV,QAAS,SAAU4H,QAAS1C,EAAGjF,OAAQ4D,MAAM,KAIlDrhB,KAAKokB,YAAa,EAAAxhB,EAAAE,SAAE9C,KAAKmkB,cAAc3b,QAAQ,iBAAiBwS,KAAK,cAAcqK,KACnFrlB,KAAKokB,WAAW5H,SAAS,0BAEzBxc,KAAKmkB,cAAc7gB,GAAG,mBAAoBC,UAAEC,MAAMxD,KAAKslB,SAAUtlB,OACjEA,KAAKmkB,cAAc7gB,GAAG,oBAAqBC,UAAEC,MAAMxD,KAAKulB,SAAUvlB,UhBo0HlEwC,IAAK,aACLR,MAAO,SgB5zHEsF,GACLtH,KAAKmkB,eAAiBnkB,KAAKwlB,aAC7BxlB,KAAKmkB,cAAc3b,QAAQ,ahBy0H7BhG,IAAK,SACLR,MAAO,SgB/zHFsF,GACDtH,KAAKwlB,YACPxlB,KAAKwe,KAAKlX,GAEVtH,KAAKue,KAAKjX,MhB20HZ9E,IAAK,OACLR,MAAO,SgBl0HJsF,GACH,KAAItH,KAAKwlB,aAAexlB,KAAKukB,SAAWvkB,KAAKskB,SAA7C,CAIAtkB,KAAKukB,SAAU,EACfvkB,KAAKskB,SAAU,EACftkB,KAAKqkB,UAAW,CAEhB,IAAI3B,GAAK1iB,KAAKgD,WAEd0f,GAAGpG,UAAUC,MAAQ,OACrBmG,EAAGpG,UAAUhV,EAAIA,EAIdA,KAAOtH,KAAKygB,UAAwC,UAA5BzgB,KAAK6I,MAAM4T,KAAK,UACxCnV,GAAKA,EAAE4c,iBAER5c,EAAEme,kBACFne,EAAE4c,kBAIAlkB,KAAK0lB,YACP,EAAA9iB,EAAAE,SAAE9C,KAAKR,MAAM8D,GAAG,qBAAsBC,UAAEC,MAAMxD,KAAK6kB,WAAY7kB,OAIjE0iB,EAAGjF,OAAOjB,SAAS,uBAAuB6B,YAAY,sBAElDre,KAAKmkB,cACPnkB,KAAKmkB,cAAc3b,QAAQ,QAE3BxI,KAAKslB,ehBm0HP9iB,IAAK,WACLR,MAAO,WgB/zHPhC,KAAKskB,SAAU,EACftkB,KAAKukB,SAAU,EAEXvkB,KAAK0lB,aAEP,EAAA9iB,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAUxhB,GAAG,+CAAgDC,UAAEC,MAAMxD,KAAKwe,KAAMxe,QAC5F,EAAA4C,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAUxhB,GAAG,+CAAgDC,UAAEC,MAAMxD,KAAK+kB,iBAAkB/kB,QAQ1GA,KAAKgD,YAAYwa,QAAQ,sBhB40HzBhb,IAAK,OACLR,MAAO,SgBn0HJsF,GACH,KAAItH,KAAK2lB,YAAc3lB,KAAKukB,SAAWvkB,KAAKskB,SAA5C,CAIA,GAAI5B,GAAK1iB,KAAKgD,YAAaqhB,EAAYrkB,KAAKqkB,UAAYrkB,KAAKklB,iBAAiB5d,EAY9E,IAVAtH,KAAKskB,SAAU,EACftkB,KAAKukB,SAAU,EACfvkB,KAAKqkB,UAAW,EAEhB3B,EAAGpG,UAAUC,MAAQ,OACrBmG,EAAGpG,UAAUhV,EAAIA,EAKb+c,EAEF,YADArkB,KAAKskB,SAAU,EAIbtkB,MAAKmkB,cACPnkB,KAAKmkB,cAAc3b,QAAQ,QAE3BxI,KAAKulB,ehBw0HP/iB,IAAK,WACLR,MAAO,WgBp0HPhC,KAAKskB,SAAU,EACftkB,KAAKukB,SAAU,CAEf,IAAI7B,GAAK1iB,KAAKgD,WAGd0f,GAAGjF,OAAOjB,SAAS,sBAAsB6B,YAAY,wBAGrD,EAAAzb,EAAAE,SAAE9C,KAAKR,MAAM4E,IAAI,qBAAsBb,UAAEC,MAAMxD,KAAK6kB,WAAY7kB,QAChE,EAAA4C,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAU1gB,IAAI,+CAAgDb,UAAEC,MAAMxD,KAAKwe,KAAMxe,QAC7F,EAAA4C,EAAAE,SAAE9C,KAAKR,KAAKslB,UAAU1gB,IAAI,+CAAgDb,UAAEC,MAAMxD,KAAK+kB,iBAAkB/kB,OAOzG0iB,EAAGlF,QAAQ,sBhBw0HXhb,IAAK,QACLR,MAAO,WgBr0HP,MAAIhC,MAAKwkB,SACAxkB,KAAK8I,MAAMga,UAEhB9iB,KAAKygB,UACAzgB,KAAK6I,MAAMia,WhBm1HpBtgB,IAAK,YACLR,MAAO,WgBx0HP,MAAOhC,MAAKgD,YAAYya,OAAOmI,SAAS,yBACrC5lB,KAAKgD,YAAYya,OAAOmI,SAAS,yBhBm1HpCpjB,IAAK,WACLR,MAAO,WgB10HP,MAAOhC,MAAKgD,YAAYya,OAAOmI,SAAS,wBACrC5lB,KAAKgD,YAAYya,OAAOmI,SAAS,0BhB60HpCpjB,IAAK,QACLrB,IAAK,WgBtrIL,MAAOnB,MAAKgD,YAAY2Z,aAAa9T,ShBgsIrCrG,IAAK,WACLrB,IAAK,WgBzrIL,MAAOnB,MAAKgD,YAAY2Z,aAAa8D,chBmsIrCje,IAAK,QACLrB,IAAK,WgB5rIL,MAAOnB,MAAKgD,YAAYqa,aAAavU,ShBssIrCtG,IAAK,WACLrB,IAAK,WgB/rIL,MAAOnB,MAAKgD,YAAYqa,aAAamH,chBysIrChiB,IAAK,YACLrB,IAAK,WgBlsIL,OAAQnB,KAAKgD,YAAYC,QAAQqF,UAAYtI,KAAKokB,ehBusI7ClH,IAGTxd,GAAQoD,QgB93HOoa,GhBk4HT,SAAUvd,EAAQD,EAASS,GAEjC,YAiBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAhBhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MiBzyIhiBa,EAAAxC,EAAA,GjB6yIIyC,EAAWiH,EAAuBlH,GiB5yItCuZ,EAAA/b,EAAA,GjBgzIIgc,EAActS,EAAuBqS,GiB1yInCU,EjBozIa,WiBhzIjB,QAAAA,GAAY5Z,GAAapB,EAAA5B,KAAA4c,GAIvB5c,KAAKgD,YAAcA,EAInBhD,KAAK6I,MAAQ7I,KAAKgD,YAAYI,QAAQ0gB,GAAG,SAAW9jB,KAAKgD,YAAYI,UAAWpD,KAAKgD,YAAYC,QAAQ4F,OACvG7I,KAAKgD,YAAYI,QAAQ8d,KAAKlhB,KAAKgD,YAAYC,QAAQ4F,OAErD7I,KAAK6I,OAAgC,IAAtB7I,KAAK6I,MAAMxG,SAC5BrC,KAAK6I,OAAQ,GAGf7I,KAAK6lB,ajB4jJP,MApQA5jB,GAAa2a,IACXpa,IAAK,OACLR,MAAO,WiBtzIFhC,KAAKygB,aAGVzgB,KAAK6I,MAAMvF,IACTwiB,oBAAqBviB,UAAEC,MAAMxD,KAAK+lB,QAAS/lB,QAE7CA,KAAK6I,MAAMvF,IACT0iB,qBAAsBziB,UAAEC,MAAMxD,KAAKimB,SAAUjmB,YjB2zI/CwC,IAAK,SACLR,MAAO,WiBvzIFhC,KAAKygB,YAGVzgB,KAAK6I,MAAMzE,IAAI,mBjB2zIf5B,IAAK,aACLR,MAAO,WiBxzIP,GAAKhC,KAAKygB,WAAV,CAIA,GAAI7I,GAAM,IAIR5X,KAAK6I,MAAM+O,MACX5X,KAAK6I,MAAMmS,KAAK,SAChBhb,KAAK6I,MAAM4T,KAAK,eAChB9C,IAAI,SAACuM,GACDA,GAAiB,KAARtO,IACXA,EAAMsO,KAINtO,YAAevT,WACjBuT,EAAM5X,KAAKmmB,kBAAkBvO,EAAIzR,OAAOnG,KAAKgD,YAAY6B,SAC/B,gBAAR+S,IAAoBA,YAAerQ,UACrDqQ,EAAM,IAGR5X,KAAK6I,MAAMud,KAAK,QAASxO,OjBi0IzBpV,IAAK,WACLR,MAAO,WiBxzIP,QAAKhC,KAAKygB,YAIHzgB,KAAK6I,MAAM+O,SjBs0IlBpV,IAAK,WACLR,MAAO,SiB5zIA4V,GACP,GAAK5X,KAAKygB,WAAV,CAIA,GAAI4F,GAAWrmB,KAAK6I,MAAMud,KAAK,QAE/BxO,GAAMA,GAAY,GAEdA,KAASyO,GAAsB,MAKnCrmB,KAAK6I,MAAMud,KAAK,QAASxO,GAOzB5X,KAAK6I,MAAM2U,SACTyB,KAAM,SACNjc,YAAahD,KAAKgD,YAClBkB,MAAOlE,KAAKgD,YAAYkB,MACxBlC,MAAO4V,SjB00ITpV,IAAK,oBACLR,MAAO,WiB/zIqB,GAAZ4V,GAAY1U,UAAAb,OAAA,OAAAc,KAAAD,UAAA,GAAAA,UAAA,GAAN,IAGtB,QAFA0U,EAAMA,GAAY5X,KAAKgD,YAAY6Z,aAAayJ,mBAMhD1O,EAAM5X,KAAKgD,YAAY6Z,aAAa0J,qBAAqB3O,GAAK,IAEf,IAA3C5X,KAAKgD,YAAYC,QAAQ+F,gBAC3B4O,EAAMA,EAAI9S,QAAQ,MAAO,KAGpB8S,GATE,MjBm1ITpV,IAAK,WACLR,MAAO,WiBn0IP,OAAuB,IAAfhC,KAAK6I,SjB60IbrG,IAAK,YACLR,MAAO,WiBt0IP,MAAOhC,MAAKygB,aAAezgB,KAAK8d,gBjBg1IhCtb,IAAK,aACLR,MAAO,WiBz0IP,MAAOhC,MAAKygB,aAA+C,IAAhCzgB,KAAK6I,MAAMud,KAAK,ejBq1I3C5jB,IAAK,UACLR,MAAO,WiB50IHhC,KAAKygB,YACPzgB,KAAK6I,MAAMud,KAAK,YAAY,MjBy1I9B5jB,IAAK,SACLR,MAAO,WiB/0IHhC,KAAKygB,YACPzgB,KAAK6I,MAAMud,KAAK,YAAY,MjB21I9B5jB,IAAK,SACLR,MAAO,WiBl1IFhC,KAAKygB,cAKwC,IAA/CzgB,KAAKgD,YAAYC,QAAQ8F,mBAC1B/I,KAAKgD,YAAY6Z,aAAa2J,kBAMhCxmB,KAAKkiB,SAASliB,KAAKmmB,yBjB41InB3jB,IAAK,WACLR,MAAO,SiBn1IAsF,GACPtH,KAAKgD,YAAYsZ,UAAUC,MAAQ,eACnCvc,KAAKgD,YAAYsZ,UAAUhV,EAAIA,CAE/B,IAAIsQ,GAAM5X,KAAKwL,UAEXoM,KAAQtQ,EAAEtF,OACZhC,KAAKgD,YAAYkf,SAAStK,MjBg2I5BpV,IAAK,UACLR,MAAO,SiBt1IDsF,GACNtH,KAAKgD,YAAYsZ,UAAUC,MAAQ,cACnCvc,KAAKgD,YAAYsZ,UAAUhV,EAAIA,CAE/B,IAAIsQ,GAAM5X,KAAKwL,UAEXoM,KAAQtQ,EAAEtF,OACZhC,KAAKgD,YAAYkf,SAAStK,OjB21IvBgF,IAGTld,GAAQoD,QiBz1IO8Z,GjB61IT,SAAUjd,EAAQD,EAASS,GAEjC,YkBxkJA,SAAAsmB,GAAA5jB,EAAAgD,GACA,KAAA7F,eAAAymB,IACA,UAAAA,GAAA5jB,EAAAgD,EAOA,IAJAA,OAAA6gB,KACA7gB,EAAA,MAGAA,SAAAwP,IACA,SAAAhS,OAAA,kBAAAwC,EAGA,IAAAvF,GACAiV,CAEA,aAAA1S,EACA7C,KAAA6F,MAAA,MACA7F,KAAAkE,OAAA,OACAlE,KAAA2mB,OAAA,MACE,IAAA9jB,YAAA4jB,GACFzmB,KAAA6F,MAAAhD,EAAAgD,MACA7F,KAAAkE,MAAArB,EAAAqB,MAAAuW,QACAza,KAAA2mB,OAAA9jB,EAAA8jB,WACE,oBAAA9jB,GAAA,CACF,GAAAuC,GAAAqU,EAAAtY,IAAA0B,EACA,WAAAuC,EACA,SAAA/B,OAAA,sCAAAR,EAGA7C,MAAA6F,MAAAT,EAAAS,MACA0P,EAAAF,EAAArV,KAAA6F,OAAA0P,SACAvV,KAAAkE,MAAAkB,EAAApD,MAAAyY,MAAA,EAAAlF,GACAvV,KAAA2mB,OAAA,gBAAAvhB,GAAApD,MAAAuT,GAAAnQ,EAAApD,MAAAuT,GAAA,MACE,IAAA1S,EAAAR,OAAA,CACFrC,KAAA6F,SAAA,MACA0P,EAAAF,EAAArV,KAAA6F,OAAA0P,QACA,IAAAqR,GAAAC,EAAArmB,KAAAqC,EAAA,EAAA0S,EACAvV,MAAAkE,MAAA4iB,EAAAF,EAAArR,GACAvV,KAAA2mB,OAAA,gBAAA9jB,GAAA0S,GAAA1S,EAAA0S,GAAA,MACE,oBAAA1S,GAEFA,GAAA,SACA7C,KAAA6F,MAAA,MACA7F,KAAAkE,OACArB,GAAA,OACAA,GAAA,MACA,IAAAA,GAEA7C,KAAA2mB,OAAA,MACE,CACF3mB,KAAA2mB,OAAA,CAEA,IAAAxb,GAAApK,OAAAoK,KAAAtI,EACA,UAAAA,KACAsI,EAAA4b,OAAA5b,EAAAG,QAAA,YACAtL,KAAA2mB,OAAA,gBAAA9jB,GAAAmD,MAAAnD,EAAAmD,MAAA,EAGA,IAAAghB,GAAA7b,EAAA8b,OAAApN,KAAA,GACA,MAAAmN,IAAAE,IACA,SAAA7jB,OAAA,sCAAA8jB,KAAAC,UAAAvkB,GAGA7C,MAAA6F,MAAAqhB,EAAAF,EAEA,IAAAxR,GAAAH,EAAArV,KAAA6F,OAAA2P,OACAtR,IACA,KAAA5D,EAAA,EAAaA,EAAAkV,EAAAnT,OAAmB/B,IAChC4D,EAAAgD,KAAArE,EAAA2S,EAAAlV,IAGAN,MAAAkE,MAAA4iB,EAAA5iB,GAIA,GAAAmjB,EAAArnB,KAAA6F,OAEA,IADA0P,EAAAF,EAAArV,KAAA6F,OAAA0P,SACAjV,EAAA,EAAaA,EAAAiV,EAAcjV,IAAA,CAC3B,GAAAgnB,GAAAD,EAAArnB,KAAA6F,OAAAvF,EACAgnB,KACAtnB,KAAAkE,MAAA5D,GAAAgnB,EAAAtnB,KAAAkE,MAAA5D,KAKAN,KAAA2mB,OAAAvf,KAAAsP,IAAA,EAAAtP,KAAAqP,IAAA,EAAAzW,KAAA2mB,SAEA5lB,OAAAwmB,QACAxmB,OAAAwmB,OAAAvnB,MAkTA,QAAAwnB,GAAAC,EAAAC,GACA,MAAAC,QAAAF,EAAAG,QAAAF,IAGA,QAAAG,GAAAH,GACA,gBAAAD,GACA,MAAAD,GAAAC,EAAAC,IAIA,QAAAI,GAAAjiB,EAAAkiB,EAAAC,GASA,MARAniB,GAAAX,MAAAyB,QAAAd,SAEAA,EAAAkB,QAAA,SAAAtG,IACA4mB,EAAA5mB,KAAA4mB,EAAA5mB,QAAAsnB,GAAAC,IAGAniB,IAAA,GAEA,SAAA+R,GACA,GAAAxS,EAEA,OAAAlC,WAAAb,QACA2lB,IACApQ,EAAAoQ,EAAApQ,IAGAxS,EAAApF,KAAA6F,KACAT,EAAAlB,MAAA6jB,GAAAnQ,EACAxS,IAGAA,EAAApF,KAAA6F,KAAA3B,MAAA6jB,GACAC,IACA5iB,EAAA4iB,EAAA5iB,IAGAA,IAIA,QAAA6iB,GAAAvR,GACA,gBAAAhS,GACA,MAAA0C,MAAAsP,IAAA,EAAAtP,KAAAqP,IAAAC,EAAAhS,KAIA,QAAAwjB,GAAAtQ,GACA,MAAA1S,OAAAyB,QAAAiR,SAGA,QAAAkP,GAAAqB,EAAA9lB,GACA,OAAA/B,GAAA,EAAgBA,EAAA+B,EAAY/B,IAC5B,gBAAA6nB,GAAA7nB,KACA6nB,EAAA7nB,GAAA,EAIA,OAAA6nB,GA5dA,GAAA1O,GAAkBtZ,EAAQ,IAC1BkV,EAAclV,EAAQ,IAEtB0mB,KAAApM,MAEAiM,GAEA,UAGA,OAGA,OAGAQ,IACAnmB,QAAAoK,KAAAkK,GAAAtO,QAAA,SAAAlB,GACAqhB,EAAAL,EAAArmB,KAAA6U,EAAAxP,GAAA2P,QAAAyR,OAAApN,KAAA,KAAAhU,GAGA,IAAAwhB,KA+FAZ,GAAAjlB,WACA+X,SAAA,WACA,MAAAvZ,MAAAmG,UAGAiiB,OAAA,WACA,MAAApoB,WAAA6F,UAGAM,OAAA,SAAAuhB,GACA,GAAA3nB,GAAAC,KAAA6F,QAAA4T,GAAA4O,GAAAroB,UAAAsV,KACAvV,KAAAqG,MAAA,gBAAAshB,KAAA,EACA,IAAAziB,GAAA,IAAAlF,EAAA4mB,OAAA5mB,EAAAmE,MAAAnE,EAAAmE,MAAA4c,OAAA9gB,KAAA2mB,OACA,OAAAlN,GAAA4O,GAAAtoB,EAAA8F,OAAAZ,IAGAqjB,cAAA,SAAAZ,GACA,GAAA3nB,GAAAC,KAAAsV,MAAAlP,MAAA,gBAAAshB,KAAA,GACAziB,EAAA,IAAAlF,EAAA4mB,OAAA5mB,EAAAmE,MAAAnE,EAAAmE,MAAA4c,OAAA9gB,KAAA2mB,OACA,OAAAlN,GAAA4O,GAAA/S,IAAAiT,QAAAtjB,IAGAujB,MAAA,WACA,WAAAxoB,KAAA2mB,OAAA3mB,KAAAkE,MAAAuW,QAAAza,KAAAkE,MAAA4c,OAAA9gB,KAAA2mB,SAGArlB,OAAA,WAKA,OAJA8D,MACAmQ,EAAAF,EAAArV,KAAA6F,OAAA0P,SACAC,EAAAH,EAAArV,KAAA6F,OAAA2P,OAEAlV,EAAA,EAAiBA,EAAAiV,EAAcjV,IAC/B8E,EAAAoQ,EAAAlV,IAAAN,KAAAkE,MAAA5D,EAOA,OAJA,KAAAN,KAAA2mB,SACAvhB,EAAAY,MAAAhG,KAAA2mB,QAGAvhB,GAGAqjB,UAAA,WACA,GAAAnT,GAAAtV,KAAAsV,MAAApR,KASA,OARAoR,GAAA,QACAA,EAAA,QACAA,EAAA,QAEA,IAAAtV,KAAA2mB,QACArR,EAAApO,KAAAlH,KAAA2mB,QAGArR,GAGAoT,WAAA,WACA,GAAApT,GAAAtV,KAAAsV,MAAAhU,QASA,OARAgU,GAAAgB,GAAA,IACAhB,EAAAiB,GAAA,IACAjB,EAAAkB,GAAA,IAEA,IAAAxW,KAAA2mB,SACArR,EAAAtP,MAAAhG,KAAA2mB,QAGArR,GAGAlP,MAAA,SAAAshB,GAEA,MADAA,GAAAtgB,KAAAsP,IAAAgR,GAAA,KACA,GAAAjB,GAAAzmB,KAAAkE,MAAAyV,IAAAkO,EAAAH,IAAA5G,OAAA9gB,KAAA2mB,QAAA3mB,KAAA6F,QAGAG,MAAA,SAAA4R,GACA,MAAA1U,WAAAb,OACA,GAAAokB,GAAAzmB,KAAAkE,MAAA4c,OAAA1Z,KAAAsP,IAAA,EAAAtP,KAAAqP,IAAA,EAAAmB,KAAA5X,KAAA6F,OAGA7F,KAAA2mB,QAIAxT,IAAA2U,EAAA,QAAAG,EAAA,MACAhZ,MAAA6Y,EAAA,QAAAG,EAAA,MACA7b,KAAA0b,EAAA,QAAAG,EAAA,MAEAniB,IAAAgiB,GAAA,0CAAAlQ,GAAqE,OAAAA,EAAA,eAErE+Q,YAAAb,EAAA,QAAAG,EAAA,MACAW,UAAAd,EAAA,QAAAG,EAAA,MAEAhhB,YAAA6gB,EAAA,QAAAG,EAAA,MACAjmB,MAAA8lB,EAAA,QAAAG,EAAA,MAEAjO,OAAA8N,EAAA,QAAAG,EAAA,MACAjZ,KAAA8Y,EAAA,QAAAG,EAAA,MAEAtT,MAAAmT,EAAA,QAAAG,EAAA,MACAY,OAAAf,EAAA,QAAAG,EAAA,MAEAlb,KAAA+a,EAAA,SAAAG,EAAA,MACAjX,QAAA8W,EAAA,SAAAG,EAAA,MACApT,OAAAiT,EAAA,SAAAG,EAAA,MACA/b,MAAA4b,EAAA,SAAAG,EAAA,MAEAjT,EAAA8S,EAAA,QAAAG,EAAA,MACAhT,EAAA6S,EAAA,QAAAG,EAAA,MACAzQ,EAAAsQ,EAAA,QAAAG,EAAA,MAEA1nB,EAAAunB,EAAA,QAAAG,EAAA,MACAtjB,EAAAmjB,EAAA,SACAtR,EAAAsR,EAAA,SAEA7R,QAAA,SAAA2B,GACA,MAAA1U,WAAAb,OACA,GAAAokB,GAAA7O,GAGAvC,EAAArV,KAAA6F,OAAAoQ,QAAAjW,KAAAkE,QAGA8R,IAAA,SAAA4B,GACA,MAAA1U,WAAAb,OACA,GAAAokB,GAAA7O,GAGA6B,EAAA4O,GAAArS,IAAAhW,KAAAsV,MAAAlP,QAAAlC,QAGA4kB,UAAA,WACA,GAAAxT,GAAAtV,KAAAsV,MAAApR,KACA,YAAAoR,EAAA,aAAAA,EAAA,WAAAA,EAAA,IAGAyT,WAAA,WAKA,OAHAzT,GAAAtV,KAAAsV,MAAApR,MAEA8kB,KACA1oB,EAAA,EAAiBA,EAAAgV,EAAAjT,OAAgB/B,IAAA,CACjC,GAAA2oB,GAAA3T,EAAAhV,GAAA,GACA0oB,GAAA1oB,GAAA2oB,GAAA,OAAAA,EAAA,MAAA7hB,KAAA8N,KAAA+T,EAAA,iBAGA,YAAAD,EAAA,SAAAA,EAAA,SAAAA,EAAA,IAGAE,SAAA,SAAAC,GAEA,GAAAC,GAAAppB,KAAA+oB,aACAM,EAAAF,EAAAJ,YAEA,OAAAK,GAAAC,GACAD,EAAA,MAAAC,EAAA,MAGAA,EAAA,MAAAD,EAAA,MAGAE,MAAA,SAAAH,GACA,GAAAI,GAAAvpB,KAAAkpB,SAAAC,EACA,OAAAI,IAAA,IACA,MAGAA,GAAA,aAGAhjB,OAAA,WAEA,GAAA+O,GAAAtV,KAAAsV,MAAApR,KAEA,QADA,IAAAoR,EAAA,OAAAA,EAAA,OAAAA,EAAA,QACA,KAGA9O,QAAA,WACA,OAAAxG,KAAAuG,UAGAijB,OAAA,WAEA,OADAlU,GAAAtV,KAAAsV,MACAhV,EAAA,EAAiBA,EAAA,EAAOA,IACxBgV,EAAApR,MAAA5D,GAAA,IAAAgV,EAAApR,MAAA5D,EAEA,OAAAgV,IAGAmU,QAAA,SAAAhR,GACA,GAAAhD,GAAAzV,KAAAyV,KAEA,OADAA,GAAAvR,MAAA,IAAAuR,EAAAvR,MAAA,GAAAuU,EACAhD,GAGAiU,OAAA,SAAAjR,GACA,GAAAhD,GAAAzV,KAAAyV,KAEA,OADAA,GAAAvR,MAAA,IAAAuR,EAAAvR,MAAA,GAAAuU,EACAhD,GAGAkU,SAAA,SAAAlR,GACA,GAAAhD,GAAAzV,KAAAyV,KAEA,OADAA,GAAAvR,MAAA,IAAAuR,EAAAvR,MAAA,GAAAuU,EACAhD,GAGAmU,WAAA,SAAAnR,GACA,GAAAhD,GAAAzV,KAAAyV,KAEA,OADAA,GAAAvR,MAAA,IAAAuR,EAAAvR,MAAA,GAAAuU,EACAhD,GAGAoU,OAAA,SAAApR,GACA,GAAA9C,GAAA3V,KAAA2V,KAEA,OADAA,GAAAzR,MAAA,IAAAyR,EAAAzR,MAAA,GAAAuU,EACA9C,GAGAmU,QAAA,SAAArR,GACA,GAAA9C,GAAA3V,KAAA2V,KAEA,OADAA,GAAAzR,MAAA,IAAAyR,EAAAzR,MAAA,GAAAuU,EACA9C,GAGAoE,UAAA,WAEA,GAAAzE,GAAAtV,KAAAsV,MAAApR,MACA0T,EAAA,GAAAtC,EAAA,OAAAA,EAAA,OAAAA,EAAA,EACA,OAAAmR,GAAAnR,IAAAsC,QAGAmS,KAAA,SAAAtR,GACA,MAAAzY,MAAAgG,MAAAhG,KAAA2mB,OAAA3mB,KAAA2mB,OAAAlO,IAGAuR,QAAA,SAAAvR,GACA,MAAAzY,MAAAgG,MAAAhG,KAAA2mB,OAAA3mB,KAAA2mB,OAAAlO,IAGAwR,OAAA,SAAAC,GACA,GAAAzU,GAAAzV,KAAAyV,MACA3P,EAAA2P,EAAAvR,MAAA,EAIA,OAHA4B,MAAAokB,GAAA,IACApkB,IAAA,MAAAA,IACA2P,EAAAvR,MAAA,GAAA4B,EACA2P,GAGA0U,IAAA,SAAAC,EAAAC,GAGA,IAAAD,MAAA9U,IACA,SAAAjS,OAAA,+EAAA+mB,GAEA,IAAAE,GAAAF,EAAA9U,MACA6T,EAAAnpB,KAAAsV,MACA5T,MAAAyB,KAAAknB,EAAA,GAAAA,EAEApT,EAAA,EAAAvV,EAAA,EACAiD,EAAA2lB,EAAAtkB,QAAAmjB,EAAAnjB,QAEAukB,IAAAtT,EAAAtS,IAAA,EAAAsS,KAAAtS,IAAA,EAAAsS,EAAAtS,IAAA,KACA6lB,EAAA,EAAAD,CAEA,OAAA9D,GAAAnR,IACAiV,EAAAD,EAAAnX,MAAAqX,EAAArB,EAAAhW,MACAoX,EAAAD,EAAArb,QAAAub,EAAArB,EAAAla,QACAsb,EAAAD,EAAAle,OAAAoe,EAAArB,EAAA/c,OACAke,EAAAtkB,QAAAtE,EAAAynB,EAAAnjB,SAAA,EAAAtE,MAKAX,OAAAoK,KAAAkK,GAAAtO,QAAA,SAAAlB,GACA,QAAA6gB,EAAApb,QAAAzF,GAAA,CAIA,GAAA0P,GAAAF,EAAAxP,GAAA0P,QAGAkR,GAAAjlB,UAAAqE,GAAA,WACA,GAAA7F,KAAA6F,UACA,UAAA4gB,GAAAzmB,KAGA,IAAAkD,UAAAb,OACA,UAAAokB,GAAAvjB,UAAA2C,EAGA,IAAA4kB,GAAA,gBAAAvnB,WAAAqS,KAAAvV,KAAA2mB,MACA,WAAAF,GAAAyB,EAAA7S,EAAArV,KAAA6F,UAAA6kB,IAAA1qB,KAAAkE,QAAA4c,OAAA2J,GAAA5kB,IAIA4gB,EAAA5gB,GAAA,SAAA3B,GAIA,MAHA,gBAAAA,KACAA,EAAA4iB,EAAAD,EAAArmB,KAAA0C,WAAAqS,IAEA,GAAAkR,GAAAviB,EAAA2B,OAiEAlG,EAAAD,QAAA+mB,GlBumJM,SAAU9mB,EAAQD,EAASS,GmBt2JjC,QAAAwqB,GAAAlD,EAAAhR,EAAAC,GACA,MAAAtP,MAAAqP,IAAArP,KAAAsP,IAAAD,EAAAgR,GAAA/Q,GAGA,QAAAkU,GAAAnD,GACA,GAAAphB,GAAAohB,EAAAlO,SAAA,IAAAhO,aACA,OAAAlF,GAAAhE,OAAA,MAAAgE,IAvOA,GAAAwkB,GAAiB1qB,EAAQ,GACzB2qB,EAAc3qB,EAAQ,IAEtB4qB,IAGA,QAAAnqB,KAAAiqB,GACAA,EAAAppB,eAAAb,KACAmqB,EAAAF,EAAAjqB,MAIA,IAAAoqB,GAAArrB,EAAAD,SACA2oB,MACAlnB,OAGA6pB,GAAA7pB,IAAA,SAAAgF,GACA,GACAyR,GACA/R,EAFAolB,EAAA9kB,EAAAqT,UAAA,KAAA/R,aAGA,QAAAwjB,GACA,UACArT,EAAAoT,EAAA7pB,IAAAsU,IAAAtP,GACAN,EAAA,KACA,MACA,WACA+R,EAAAoT,EAAA7pB,IAAAwU,IAAAxP,GACAN,EAAA,KACA,MACA,SACA+R,EAAAoT,EAAA7pB,IAAAmU,IAAAnP,GACAN,EAAA,MAIA,MAAA+R,IAIS/R,QAAA7D,MAAA4V,GAHT,MAMAoT,EAAA7pB,IAAAmU,IAAA,SAAAnP,GACA,IAAAA,EACA,WAGA,IAOAqB,GACAlH,EACA4qB,EATAC,EAAA,sBACAnV,EAAA,kCACAoV,EAAA,0FACAC,EAAA,4GACApV,EAAA,QAEAX,GAAA,QAKA,IAAA9N,EAAArB,EAAAqB,MAAAwO,GAAA,CAIA,IAHAkV,EAAA1jB,EAAA,GACAA,IAAA,GAEAlH,EAAA,EAAaA,EAAA,EAAOA,IAAA,CAEpB,GAAAgrB,GAAA,EAAAhrB,CACAgV,GAAAhV,GAAAwZ,SAAAtS,EAAAiT,MAAA6Q,IAAA,OAGAJ,IACA5V,EAAA,GAAAlO,KAAAhB,MAAA0T,SAAAoR,EAAA,sBAEE,IAAA1jB,EAAArB,EAAAqB,MAAA2jB,GAAA,CAIF,IAHA3jB,IAAA,GACA0jB,EAAA1jB,EAAA,GAEAlH,EAAA,EAAaA,EAAA,EAAOA,IACpBgV,EAAAhV,GAAAwZ,SAAAtS,EAAAlH,GAAAkH,EAAAlH,GAAA,GAGA4qB,KACA5V,EAAA,GAAAlO,KAAAhB,MAAA0T,SAAAoR,IAAA,sBAEE,IAAA1jB,EAAArB,EAAAqB,MAAA4jB,GAAA,CACF,IAAA9qB,EAAA,EAAaA,EAAA,EAAOA,IACpBgV,EAAAhV,GAAAwZ,SAAAtS,EAAAlH,EAAA,KAGAkH,GAAA,KACA8N,EAAA,GAAAiW,WAAA/jB,EAAA,SAEE,MAAAA,EAAArB,EAAAqB,MAAA6jB,IAQA,OAAA7jB,EAAArB,EAAAqB,MAAAyO,IACF,gBAAAzO,EAAA,IACA,UAGA8N,EAAAuV,EAAArjB,EAAA,MAMA8N,EAAA,KAEAA,GALA,KAOA,IAtBA,KAAAhV,EAAA,EAAaA,EAAA,EAAOA,IACpBgV,EAAAhV,GAAA8G,KAAAhB,MAAA,KAAAmlB,WAAA/jB,EAAAlH,EAAA,IAGAkH,GAAA,KACA8N,EAAA,GAAAiW,WAAA/jB,EAAA,KAoBA,IAAAlH,EAAA,EAAYA,EAAA,EAAOA,IACnBgV,EAAAhV,GAAAqqB,EAAArV,EAAAhV,GAAA,MAIA,OAFAgV,GAAA,GAAAqV,EAAArV,EAAA,QAEAA,GAGA0V,EAAA7pB,IAAAsU,IAAA,SAAAtP,GACA,IAAAA,EACA,WAGA,IAAAsP,GAAA,sHACAjO,EAAArB,EAAAqB,MAAAiO,EAEA,IAAAjO,EAAA,CACA,GAAAxB,GAAAulB,WAAA/jB,EAAA,GAMA,SALA+jB,WAAA/jB,EAAA,aACAmjB,EAAAY,WAAA/jB,EAAA,WACAmjB,EAAAY,WAAA/jB,EAAA,WACAmjB,EAAA/lB,MAAAoB,GAAA,EAAAA,EAAA,MAKA,aAGAglB,EAAA7pB,IAAAwU,IAAA,SAAAxP,GACA,IAAAA,EACA,WAGA,IAAAwP,GAAA,kHACAnO,EAAArB,EAAAqB,MAAAmO,EAEA,IAAAnO,EAAA,CACA,GAAAxB,GAAAulB,WAAA/jB,EAAA,GAKA,SAJA+jB,WAAA/jB,EAAA,iBACAmjB,EAAAY,WAAA/jB,EAAA,WACAmjB,EAAAY,WAAA/jB,EAAA,WACAmjB,EAAA/lB,MAAAoB,GAAA,EAAAA,EAAA,MAIA,aAGAglB,EAAA3C,GAAArS,IAAA,WACA,GAAAoV,GAAAN,EAAA5nB,UAEA,OACA,IACA0nB,EAAAQ,EAAA,IACAR,EAAAQ,EAAA,IACAR,EAAAQ,EAAA,KACAA,EAAA,KACAR,EAAAxjB,KAAAhB,MAAA,IAAAglB,EAAA,KACA,KAIAJ,EAAA3C,GAAA/S,IAAA,WACA,GAAA8V,GAAAN,EAAA5nB,UAEA,OAAAkoB,GAAA/oB,OAAA,OAAA+oB,EAAA,GACA,OAAAhkB,KAAAhB,MAAAglB,EAAA,SAAAhkB,KAAAhB,MAAAglB,EAAA,SAAAhkB,KAAAhB,MAAAglB,EAAA,QACA,QAAAhkB,KAAAhB,MAAAglB,EAAA,SAAAhkB,KAAAhB,MAAAglB,EAAA,SAAAhkB,KAAAhB,MAAAglB,EAAA,SAAAA,EAAA,QAGAJ,EAAA3C,GAAA/S,IAAAiT,QAAA,WACA,GAAA6C,GAAAN,EAAA5nB,WAEAoT,EAAAlP,KAAAhB,MAAAglB,EAAA,YACA7U,EAAAnP,KAAAhB,MAAAglB,EAAA,YACA5U,EAAApP,KAAAhB,MAAAglB,EAAA,WAEA,OAAAA,GAAA/oB,OAAA,OAAA+oB,EAAA,GACA,OAAA9U,EAAA,MAAAC,EAAA,MAAAC,EAAA,KACA,QAAAF,EAAA,MAAAC,EAAA,MAAAC,EAAA,MAAA4U,EAAA,QAGAJ,EAAA3C,GAAA5S,IAAA,WACA,GAAA+V,GAAAV,EAAA5nB,UACA,OAAAsoB,GAAAnpB,OAAA,OAAAmpB,EAAA,GACA,OAAAA,EAAA,QAAAA,EAAA,SAAAA,EAAA,QACA,QAAAA,EAAA,QAAAA,EAAA,SAAAA,EAAA,SAAAA,EAAA,QAKAR,EAAA3C,GAAA1S,IAAA,WACA,GAAA8V,GAAAX,EAAA5nB,WAEAyB,EAAA,EAKA,OAJA8mB,GAAAppB,QAAA,OAAAopB,EAAA,KACA9mB,EAAA,KAAA8mB,EAAA,IAGA,OAAAA,EAAA,QAAAA,EAAA,SAAAA,EAAA,OAAA9mB,EAAA,KAGAqmB,EAAA3C,GAAApS,QAAA,SAAAX,GACA,MAAAyV,GAAAzV,EAAAmF,MAAA,QnB0lKM,SAAU9a,EAAQD,EAASS,GAEjC,YoBxzKA,IAAAurB,GAAiBvrB,EAAQ,IAEzB2gB,EAAA5b,MAAA1D,UAAAsf,OACArG,EAAAvV,MAAA1D,UAAAiZ,MAEAqQ,EAAAnrB,EAAAD,QAAA,SAAAuF,GAGA,OAFA0mB,MAEArrB,EAAA,EAAAsrB,EAAA3mB,EAAA5C,OAAmC/B,EAAAsrB,EAAStrB,IAAA,CAC5C,GAAAurB,GAAA5mB,EAAA3E,EAEAorB,GAAAG,GAEAF,EAAA7K,EAAAtgB,KAAAmrB,EAAAlR,EAAAja,KAAAqrB,IAEAF,EAAAzkB,KAAA2kB,GAIA,MAAAF,GAGAb,GAAAgB,KAAA,SAAA/mB,GACA,kBACA,MAAAA,GAAA+lB,EAAA5nB,epBk0KM,SAAUvD,EAAQD,EAASS,GAEjC,YqB51KAR,GAAAD,QAAA,SAAAmD,GACA,QAAAA,IAIAA,YAAAqC,cAAAyB,QAAA9D,IACAA,EAAAR,QAAA,GAAAQ,EAAAkkB,iBAAA3G,arBq2KM,SAAUzgB,EAAQD,EAASS,GsBt2KjC,QAAA4rB,GAAAhnB,GACA,GAAAinB,GAAA,SAAA/mB,GACA,WAAA9B,KAAA8B,GAAA,OAAAA,EACAA,GAGA/B,UAAAb,OAAA,IACA4C,EAAAC,MAAA1D,UAAAiZ,MAAAja,KAAA0C,YAGA6B,EAAAE,IAQA,OAJA,cAAAF,KACAinB,EAAAC,WAAAlnB,EAAAknB,YAGAD,EAGA,QAAAE,GAAAnnB,GACA,GAAAinB,GAAA,SAAA/mB,GACA,OAAA9B,KAAA8B,GAAA,OAAAA,EACA,MAAAA,EAGA/B,WAAAb,OAAA,IACA4C,EAAAC,MAAA1D,UAAAiZ,MAAAja,KAAA0C,WAGA,IAAAkC,GAAAL,EAAAE,EAKA,oBAAAG,GACA,OAAAwmB,GAAAxmB,EAAA/C,OAAA/B,EAAA,EAAuCA,EAAAsrB,EAAStrB,IAChD8E,EAAA9E,GAAA8G,KAAAhB,MAAAhB,EAAA9E,GAIA,OAAA8E,GAQA,OAJA,cAAAL,KACAinB,EAAAC,WAAAlnB,EAAAknB,YAGAD,EAzDA,GAAAG,GAAkBhsB,EAAQ,GAC1BisB,EAAYjsB,EAAQ,IAEpBkV,IAEAtU,QAAAoK,KAAAghB,GAuDAplB,QAAA,SAAAslB,GACAhX,EAAAgX,MAEAtrB,OAAAC,eAAAqU,EAAAgX,GAAA,YAAwDrqB,MAAAmqB,EAAAE,GAAA9W,WACxDxU,OAAAC,eAAAqU,EAAAgX,GAAA,UAAsDrqB,MAAAmqB,EAAAE,GAAA7W,QAEtD,IAAA8W,GAAAF,EAAAC,EACAtrB,QAAAoK,KAAAmhB,GAEAvlB,QAAA,SAAAwlB,GACA,GAAAxnB,GAAAunB,EAAAC,EAEAlX,GAAAgX,GAAAE,GAAAL,EAAAnnB,GACAsQ,EAAAgX,GAAAE,GAAA7B,IAAAqB,EAAAhnB,OAIApF,EAAAD,QAAA2V,GtBo3KM,SAAU1V,EAAQD,EAASS,GuBp7KjC,QAAAqsB,KAKA,OAJAC,MAEAC,EAAA3rB,OAAAoK,KAAAghB,GAEAP,EAAAc,EAAArqB,OAAA/B,EAAA,EAAqCA,EAAAsrB,EAAStrB,IAC9CmsB,EAAAC,EAAApsB,KAGAiX,UAAA,EACAgJ,OAAA,KAIA,OAAAkM,GAIA,QAAAE,GAAAN,GACA,GAAAI,GAAAD,IACAI,GAAAP,EAIA,KAFAI,EAAAJ,GAAA9U,SAAA,EAEAqV,EAAAvqB,QAIA,OAHAwqB,GAAAD,EAAAE,MACAC,EAAAhsB,OAAAoK,KAAAghB,EAAAU,IAEAjB,EAAAmB,EAAA1qB,OAAA/B,EAAA,EAAyCA,EAAAsrB,EAAStrB,IAAA,CAClD,GAAA0sB,GAAAD,EAAAzsB,GACA2sB,EAAAR,EAAAO,IAEA,IAAAC,EAAA1V,WACA0V,EAAA1V,SAAAkV,EAAAI,GAAAtV,SAAA,EACA0V,EAAA1M,OAAAsM,EACAD,EAAAM,QAAAF,IAKA,MAAAP,GAGA,QAAAU,GAAAC,EAAA/E,GACA,gBAAApjB,GACA,MAAAojB,GAAA+E,EAAAnoB,KAIA,QAAAooB,GAAAd,EAAAE,GAKA,IAJA,GAAAa,IAAAb,EAAAF,GAAAhM,OAAAgM,GACAxnB,EAAAonB,EAAAM,EAAAF,GAAAhM,QAAAgM,GAEAgB,EAAAd,EAAAF,GAAAhM,OACAkM,EAAAc,GAAAhN,QACA+M,EAAAJ,QAAAT,EAAAc,GAAAhN,QACAxb,EAAAooB,EAAAhB,EAAAM,EAAAc,GAAAhN,QAAAgN,GAAAxoB,GACAwoB,EAAAd,EAAAc,GAAAhN,MAIA,OADAxb,GAAAknB,WAAAqB,EACAvoB,EA1EA,GAAAonB,GAAkBhsB,EAAQ,EA6E1BR,GAAAD,QAAA,SAAA2sB,GAKA,OAJAI,GAAAE,EAAAN,GACAJ,KAEAS,EAAA3rB,OAAAoK,KAAAshB,GACAb,EAAAc,EAAArqB,OAAA/B,EAAA,EAAqCA,EAAAsrB,EAAStrB,IAAA,CAC9C,GAAAisB,GAAAG,EAAApsB,EAGA,QAFAmsB,EAAAF,GAEAhM,SAKA0L,EAAAM,GAAAc,EAAAd,EAAAE,IAGA,MAAAR,KvB08KM,SAAUtsB,EAAQD,EAASS,GAEjC,YAiBA,SAAS0J,GAAuBhH,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,GAEvF,QAASjB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAhBhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MwB/iLhiBa,EAAAxC,EAAA,GxBmjLIyC,EAAWiH,EAAuBlH,GwBljLtCuZ,EAAA/b,EAAA,GxBsjLIgc,EAActS,EAAuBqS,GwBhjLnCY,ExB0jLa,WwBtjLjB,QAAAA,GAAY9Z,GAAapB,EAAA5B,KAAA8c,GAIvB9c,KAAKgD,YAAcA,ExB8yLrB,MA1OAf,GAAa6a,IACXta,IAAK,OACLR,MAAO,WwB9gLP,GAAIhC,KAAKgD,YAAYC,QAAQiB,MAE3B,YADAlE,KAAKkE,MAAQlE,KAAK6e,YAAY7e,KAAKgD,YAAYC,QAAQiB,SAKpDlE,KAAKkE,OAAWlE,KAAKgD,YAAY2Z,aAAanR,aACjDxL,KAAKkE,MAAQlE,KAAK6e,YAChB7e,KAAKgD,YAAY2Z,aAAanR,WAAYxL,KAAKgD,YAAYC,QAAQ8F,uBxBmhLvEvG,IAAK,SACLR,MAAO,WwB9gLPhC,KAAKgD,YAAYI,QAAQkb,WAAW,YxB0hLpC9b,IAAK,iBACLR,MAAO,WwBjhLP,MAAKhC,MAAK2e,WAIH3e,KAAKkE,MAAMiC,OAAOnG,KAAK6E,QAHrB,MxB+hLTrC,IAAK,iBACLR,MAAO,SwBrhLM4V,GACb,GAAI1T,GAAQ0T,EAAM5X,KAAK6e,YAAYjH,GAAO,IAE1C5X,MAAKkE,MAAQA,GAAgB,QxBkiL7B1B,IAAK,cACLR,MAAO,SwBxhLG4V,GAA+B,GAA1B4V,KAA0BtqB,UAAAb,OAAA,OAAAc,KAAAD,UAAA,KAAAA,UAAA,GACrCgB,EAAQ,GAAIG,WAAUrE,KAAKumB,qBAAqB3O,GAAM5X,KAAK6E,OAoB/D,OAlBKX,GAAMoC,YACLknB,IACFtpB,EAAQlE,KAAK4iB,oBAQf5iB,KAAKgD,YAAYwa,QAAQ,qBAAsBtZ,EAAO0T,IAGnD5X,KAAKytB,mBAERvpB,EAAM8B,MAAQ,GAGT9B,KxB6hLP1B,IAAK,mBACLR,MAAO,WwB1hLP,GAAIhC,KAAK0tB,UAAa1tB,KAAK0tB,WAAa1tB,KAAKkE,MAC3C,MAAOlE,MAAKkE,KAGd,IAAIwpB,GAAW1tB,KAAKumB,qBAAqBvmB,KAAK0tB,UAC1CxpB,EAAQ,GAAIG,WAAUqpB,EAAU1tB,KAAK6E,OAEzC,OAAKX,GAAMoC,UAKJpC,GAJL2c,QAAQ8M,KAAK,sFACN3tB,KAAKkE,MAAQlE,KAAKkE,MAAQ,GAAIG,WAAU,UAAWrE,KAAK6E,YxBsiLjErC,IAAK,cACLR,MAAO,WwBzhLP,MAJKhC,MAAK2e,aACR3e,KAAKkE,MAAQlE,KAAK4iB,oBAGb5iB,KAAKkE,SxB0iLZ1B,IAAK,uBACLR,MAAO,SwBjiLYkC,GAAyB,GAAlBkH,KAAkBlI,UAAAb,OAAA,OAAAc,KAAAD,UAAA,KAAAA,UAAA,GACxC0qB,GAAmB,CAUvB,OARArqB,WAAEsX,KAAK7a,KAAKgD,YAAYmG,WAAY,SAAUvI,EAAMod,IACzB,IAArB4P,IAIJA,EAAmB5P,EAAI6P,aAAa3pB,EAAOkH,MAGtCwiB,GAAsC1pB,KxB4iL7C1B,IAAK,iBACLR,MAAO,WwBriLP,OAAQhC,KAAK2e,aAAe3e,KAAKkE,MAAMoC,axB+iLvC9D,IAAK,iBACLR,MAAO,WwBxiLP,OAA8C,IAAtChC,KAAKgD,YAAYC,QAAQgG,YxBkjLjCzG,IAAK,WACLR,MAAO,WwB3iLP,MAAOhC,MAAKkE,gBAAiBG,cxB+iL7B7B,IAAK,WACLrB,IAAK,WwBhvLL,MAAOnB,MAAKgD,YAAYC,QAAQmF,cAC9BpI,KAAKgD,YAAYC,QAAQmF,cAAiBpI,KAAK2e,WAAa3e,KAAKkE,MAAQ,QxBwvL3E1B,IAAK,SACLrB,IAAK,WwBlvLL,MAAInB,MAAKgD,YAAYC,QAAQ4B,OACpB7E,KAAKgD,YAAYC,QAAQ4B,OAG9B7E,KAAK2e,YAAc3e,KAAKkE,MAAM4pB,mBAAqB9tB,KAAKkE,MAAMW,OAAO2C,MAAM,QACtExH,KAAKytB,iBAAmB,OAAS,MAGtCztB,KAAK2e,WACA3e,KAAKkE,MAAMW,OAGb,SxB6vLPrC,IAAK,QACLrB,IAAK,WwBrvLL,MAAOnB,MAAKgD,YAAYI,QAAQ4X,KAAK,UxBgwLrC7T,IAAK,SwBvvLGnF,GACRhC,KAAKgD,YAAYI,QAAQ4X,KAAK,QAAShZ,GAElCA,YAAiBqC,YAAmD,SAApCrE,KAAKgD,YAAYC,QAAQ4B,SAE5D7E,KAAKgD,YAAYC,QAAQ4B,OAAS7E,KAAKkE,MAAMW,YxB4vL1CiY,IAGTpd,GAAQoD,QwBtmLOga,GxB0mLT,SAAUnd,EAAQD,EAASS,GAEjC,YAeA,SAASyB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAZhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,MyB70LhiBa,EAAAxC,EAAA,GzBi1LIyC,EAEJ,SAAgCC,GAAO,MAAOA,IAAOA,EAAIxB,WAAawB,GAAQC,QAASD,IAFjDF,GyB30LhCya,EzBq1Lc,WyBj1LlB,QAAAA,GAAYpa,GAAapB,EAAA5B,KAAAod,GAIvBpd,KAAKgD,YAAcA,EAInBhD,KAAKyd,OAAS,KzBm8LhB,MAzGAxb,GAAamb,IACX5a,IAAK,OACLR,MAAO,WyB70LP,GAAIyb,GAASzd,KAAKyd,QAAS,EAAA7a,EAAAE,SAAE9C,KAAKiD,QAAQiG,SAEtClJ,MAAKiD,QAAQkF,aACfsV,EAAOjB,SAASxc,KAAKiD,QAAQkF,aAG3BnI,KAAKiD,QAAQoF,YACfoV,EAAOjB,SAAS,0BAGdxc,KAAK+tB,qBACP/tB,KAAKiD,QAAQgG,UAAW,EACxBwU,EAAOjB,SAAS,2BAEhBxc,KAAKiD,QAAQgG,UAAW,KzBq1L1BzG,IAAK,SACLR,MAAO,WyBh1LP,GAAIgsB,GAAehuB,KAAKgD,YAAYuF,UAAYvI,KAAKgD,YAAYuF,UAAY,IAEzEylB,IACFhuB,KAAKyd,OAAOwQ,SAASD,MzBs1LvBxrB,IAAK,SACLR,MAAO,WyBl1LPhC,KAAKyd,OAAOyQ,YzBs1LZ1rB,IAAK,oBACLR,MAAO,WyBn1LP,OACGhC,KAAKiD,QAAQgG,UAAajJ,KAAKgD,YAAY6Z,aAAa8B,YAAc3e,KAAKkE,MAAM4pB,qBACvD,IAA1B9tB,KAAKiD,QAAQgG,YACZjJ,KAAKiD,QAAQ4B,QAAW7E,KAAKiD,QAAQ4B,SAAW7E,KAAKiD,QAAQ4B,OAAO2C,MAAM,qBzBy1L9EhF,IAAK,SACLR,MAAO,WyBl1LP,GAAKhC,KAAKgD,YAAY6Z,aAAa8B,WAAnC,CAIA,GAAIwP,IAAwC,IAA5BnuB,KAAKiD,QAAQoF,WAC3Boa,EAAS0L,EAAWnuB,KAAKiD,QAAQoG,QAAUrJ,KAAKiD,QAAQ2G,YAEtDwkB,EAAkBpuB,KAAKyd,OAAOyD,KAAK,8CACrCmN,EAAWruB,KAAKyd,OAAOyD,KAAK,uCAC5BoN,EAAatuB,KAAKyd,OAAOyD,KAAK,yCAE5BqN,EAAOvuB,KAAKkE,MAAMsqB,aAGlBH,GAAShsB,QACXgsB,EAASjN,IAAI+M,EAAW,MAAQ,QAASA,EAAW1L,EAAO3c,IAAI0D,OAASiZ,EAAO3c,IAAIyD,UAAY,EAAIglB,EAAK9pB,IAEtG6pB,EAAWjsB,QACbisB,EAAWlN,IAAI+M,EAAW,MAAQ,QAASA,EAAW1L,EAAOzc,MAAMwD,OAASiZ,EAAOzc,MAAMuD,UAAY,EAAIglB,EAAK5pB,IAE5GypB,EAAgB/rB,QAClB+rB,EAAgBhN,KACdkB,IAAOG,EAAO1c,WAAWyD,OAAS+kB,EAAK7pB,EAAI+d,EAAO1c,WAAWyD,OAC7D6Y,KAAQkM,EAAK5sB,EAAI8gB,EAAO1c,WAAWwD,UAKvCvJ,KAAKyd,OAAOyD,KAAK,2BACdE,IAAI,kBAAmBphB,KAAKkE,MAAMuqB,kBAAkBC,cAGvD,IAAIC,GAAW3uB,KAAKkE,MAAMwqB,cACtBE,EAAU,EAGZA,GADE5uB,KAAKiD,QAAQoF,WACfumB,6BAAuCD,EAAvC,yBAEAC,8BAAwCD,EAAxC,yBAGF3uB,KAAKyd,OAAOyD,KAAK,4BAA4BE,IAAI,aAAcwN,OzBq1L/DpsB,IAAK,UACLrB,IAAK,WyBr7LL,MAAOnB,MAAKgD,YAAYC,WzBy7LxBT,IAAK,QACLrB,IAAK,WyBt7LL,MAAOnB,MAAKgD,YAAY6Z,aAAa3Y,UzB27LhCkZ,IAGT1d,GAAQoD,QyB/1LOsa,GzBm2LT,SAAUzd,EAAQD,EAASS,GAEjC,YAcA,SAASyB,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhHhB,OAAOC,eAAetB,EAAS,cAC7BsC,OAAO,GAGT,IAAIC,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9B,GAAI,EAAGA,EAAI8B,EAAMC,OAAQ/B,IAAK,CAAE,GAAIgC,GAAaF,EAAM9B,EAAIgC,GAAWpB,WAAaoB,EAAWpB,aAAc,EAAOoB,EAAWrB,cAAe,EAAU,SAAWqB,KAAYA,EAAWC,UAAW,GAAMxB,OAAOC,eAAemB,EAAQG,EAAWE,IAAKF,IAAiB,MAAO,UAAUR,EAAaW,EAAYC,GAAiJ,MAA9HD,IAAYP,EAAiBJ,EAAYN,UAAWiB,GAAiBC,GAAaR,EAAiBJ,EAAaY,GAAqBZ,M0Bt+L1hBwb,E1B0+La,W0Bt+LjB,QAAAA,GAAYta,GAAapB,EAAA5B,KAAAsd,GAIvBtd,KAAKgD,YAAcA,EAInBhD,KAAK8I,MAAQ,K1BiiMf,MAlDA7G,GAAaqb,IACX9a,IAAK,WACLR,MAAO,W0B7+LP,QAAShC,KAAK8I,S1Bi/LdtG,IAAK,OACLR,MAAO,W0B3+LPhC,KAAK8I,MAAQ9I,KAAKgD,YAAYC,QAAQ6F,MACpC9I,KAAKgD,YAAYI,QAAQ8d,KAAKlhB,KAAKgD,YAAYC,QAAQ6F,OAAS,KAE9D9I,KAAK8I,OAAgC,IAAtB9I,KAAK8I,MAAMzG,SAE5BrC,KAAK8I,MAAQ,S1Bk/LftG,IAAK,SACLR,MAAO,W0B9+LHhC,KAAKwkB,YACPxkB,KAAK8I,MAAM1E,IAAI,mB1Bw/LjB5B,IAAK,SACLR,MAAO,W0Bj/LP,GAAKhC,KAAKgD,YAAY6Z,aAAa8B,YAAe3e,KAAKwkB,WAAvD,CAIA,GAAIqK,GAAW7uB,KAAKgD,YAAY6Z,aAAayJ,iBACzCwI,GAAUC,WAAcF,GAExBG,EAAMhvB,KAAK8I,MAAMoY,KAAK,KAAK+N,GAAG,EAE9BD,GAAI3sB,OAAS,EACf2sB,EAAI5N,IAAI0N,GAER9uB,KAAK8I,MAAMsY,IAAI0N,Q1Bu/LZxR,IAGT5d,GAAQoD,Q0Br/LOwa", "file": "bootstrap-colorpicker.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"bootstrap-colorpicker\", [\"jquery\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"bootstrap-colorpicker\"] = factory(require(\"jquery\"));\n\telse\n\t\troot[\"bootstrap-colorpicker\"] = factory(root[\"jQuery\"]);\n})(typeof self !== 'undefined' ? self : this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"bootstrap-colorpicker\", [\"jquery\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"bootstrap-colorpicker\"] = factory(require(\"jquery\"));\n\telse\n\t\troot[\"bootstrap-colorpicker\"] = factory(root[\"jQuery\"]);\n})(typeof self !== 'undefined' ? self : this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 7);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Colorpicker extension class.\n */\nvar Extension = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Object} options\n   */\n  function Extension(colorpicker) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Extension);\n\n    /**\n     * The colorpicker instance\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * Extension options\n     *\n     * @type {Object}\n     */\n    this.options = options;\n\n    if (!(this.colorpicker.element && this.colorpicker.element.length)) {\n      throw new Error('Extension: this.colorpicker.element is not valid');\n    }\n\n    this.colorpicker.element.on('colorpickerCreate.colorpicker-ext', _jquery2.default.proxy(this.onCreate, this));\n    this.colorpicker.element.on('colorpickerDestroy.colorpicker-ext', _jquery2.default.proxy(this.onDestroy, this));\n    this.colorpicker.element.on('colorpickerUpdate.colorpicker-ext', _jquery2.default.proxy(this.onUpdate, this));\n    this.colorpicker.element.on('colorpickerChange.colorpicker-ext', _jquery2.default.proxy(this.onChange, this));\n    this.colorpicker.element.on('colorpickerInvalid.colorpicker-ext', _jquery2.default.proxy(this.onInvalid, this));\n    this.colorpicker.element.on('colorpickerShow.colorpicker-ext', _jquery2.default.proxy(this.onShow, this));\n    this.colorpicker.element.on('colorpickerHide.colorpicker-ext', _jquery2.default.proxy(this.onHide, this));\n    this.colorpicker.element.on('colorpickerEnable.colorpicker-ext', _jquery2.default.proxy(this.onEnable, this));\n    this.colorpicker.element.on('colorpickerDisable.colorpicker-ext', _jquery2.default.proxy(this.onDisable, this));\n  }\n\n  /**\n   * Function called every time a new color needs to be created.\n   * Return false to skip this resolver and continue with other extensions' ones\n   * or return anything else to consider the color resolved.\n   *\n   * @param {ColorItem|String|*} color\n   * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n   * @return {ColorItem|String|*}\n   */\n\n\n  _createClass(Extension, [{\n    key: 'resolveColor',\n    value: function resolveColor(color) {\n      var realColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n      return false;\n    }\n\n    /**\n     * Method called after the colorpicker is created\n     *\n     * @listens Colorpicker#colorpickerCreate\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onCreate',\n    value: function onCreate(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker is destroyed\n     *\n     * @listens Colorpicker#colorpickerDestroy\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onDestroy',\n    value: function onDestroy(event) {\n      this.colorpicker.element.off('.colorpicker-ext');\n    }\n\n    /**\n     * Method called after the colorpicker is updated\n     *\n     * @listens Colorpicker#colorpickerUpdate\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onUpdate',\n    value: function onUpdate(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker color is changed\n     *\n     * @listens Colorpicker#colorpickerChange\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onChange',\n    value: function onChange(event) {}\n    // to be extended\n\n\n    /**\n     * Method called when the colorpicker color is invalid\n     *\n     * @listens Colorpicker#colorpickerInvalid\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onInvalid',\n    value: function onInvalid(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker is hidden\n     *\n     * @listens Colorpicker#colorpickerHide\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onHide',\n    value: function onHide(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker is shown\n     *\n     * @listens Colorpicker#colorpickerShow\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onShow',\n    value: function onShow(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker is disabled\n     *\n     * @listens Colorpicker#colorpickerDisable\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onDisable',\n    value: function onDisable(event) {}\n    // to be extended\n\n\n    /**\n     * Method called after the colorpicker is enabled\n     *\n     * @listens Colorpicker#colorpickerEnable\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onEnable',\n    value: function onEnable(event) {\n      // to be extended\n    }\n  }]);\n\n  return Extension;\n}();\n\nexports.default = Extension;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ColorItem = exports.HSVAColor = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }(); /**\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * Color manipulation class, specific for Bootstrap Colorpicker\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      */\n\n\nvar _color = __webpack_require__(16);\n\nvar _color2 = _interopRequireDefault(_color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * HSVA color data class, containing the hue, saturation, value and alpha\n * information.\n */\nvar HSVAColor = function () {\n  /**\n   * @param {number|int} h\n   * @param {number|int} s\n   * @param {number|int} v\n   * @param {number|int} a\n   */\n  function HSVAColor(h, s, v, a) {\n    _classCallCheck(this, HSVAColor);\n\n    this.h = isNaN(h) ? 0 : h;\n    this.s = isNaN(s) ? 0 : s;\n    this.v = isNaN(v) ? 0 : v;\n    this.a = isNaN(h) ? 1 : a;\n  }\n\n  _createClass(HSVAColor, [{\n    key: 'toString',\n    value: function toString() {\n      return this.h + ', ' + this.s + '%, ' + this.v + '%, ' + this.a;\n    }\n  }]);\n\n  return HSVAColor;\n}();\n\n/**\n * HSVA color manipulation\n */\n\n\nvar ColorItem = function () {\n  _createClass(ColorItem, [{\n    key: 'api',\n\n\n    /**\n     * Applies a method of the QixColor API and returns a new Color object or\n     * the return value of the method call.\n     *\n     * If no argument is provided, the internal QixColor object is returned.\n     *\n     * @param {String} fn QixColor function name\n     * @param args QixColor function arguments\n     * @example let darkerColor = color.api('darken', 0.25);\n     * @example let luminosity = color.api('luminosity');\n     * @example color = color.api('negate');\n     * @example let qColor = color.api().negate();\n     * @returns {ColorItem|QixColor|*}\n     */\n    value: function api(fn) {\n      for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      if (arguments.length === 0) {\n        return this._color;\n      }\n\n      var result = this._color[fn].apply(this._color, args);\n\n      if (!(result instanceof _color2.default)) {\n        // return result of the method call\n        return result;\n      }\n\n      return new ColorItem(result, this.format);\n    }\n\n    /**\n     * Returns the original ColorItem constructor data,\n     * plus a 'valid' flag to know if it's valid or not.\n     *\n     * @returns {{color: *, format: String, valid: boolean}}\n     */\n\n  }, {\n    key: 'original',\n    get: function get() {\n      return this._original;\n    }\n\n    /**\n     * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n     * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n     */\n\n  }], [{\n    key: 'HSVAColor',\n\n\n    /**\n     * Returns the HSVAColor class\n     *\n     * @static\n     * @example let colorData = new ColorItem.HSVAColor(360, 100, 100, 1);\n     * @returns {HSVAColor}\n     */\n    get: function get() {\n      return HSVAColor;\n    }\n  }]);\n\n  function ColorItem() {\n    var color = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n    _classCallCheck(this, ColorItem);\n\n    this.replace(color, format);\n  }\n\n  /**\n   * Replaces the internal QixColor object with a new one.\n   * This also replaces the internal original color data.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data to be parsed (if needed)\n   * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n   * @example color.replace('rgb(255,0,0)', 'hsl');\n   * @example color.replace(hsvaColorData);\n   */\n\n\n  _createClass(ColorItem, [{\n    key: 'replace',\n    value: function replace(color) {\n      var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n      format = ColorItem.sanitizeFormat(format);\n\n      /**\n       * @type {{color: *, format: String}}\n       * @private\n       */\n      this._original = {\n        color: color,\n        format: format,\n        valid: true\n      };\n      /**\n       * @type {QixColor}\n       * @private\n       */\n      this._color = ColorItem.parse(color);\n\n      if (this._color === null) {\n        this._color = (0, _color2.default)();\n        this._original.valid = false;\n        return;\n      }\n\n      /**\n       * @type {*|string}\n       * @private\n       */\n      this._format = format ? format : ColorItem.isHex(color) ? 'hex' : this._color.model;\n    }\n\n    /**\n     * Parses the color returning a Qix Color object or null if cannot be\n     * parsed.\n     *\n     * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n     * @example let qColor = ColorItem.parse('rgb(255,0,0)');\n     * @static\n     * @returns {QixColor|null}\n     */\n\n  }, {\n    key: 'isValid',\n\n\n    /**\n     * Returns true if the color is valid, false if not.\n     *\n     * @returns {boolean}\n     */\n    value: function isValid() {\n      return this._original.valid === true;\n    }\n\n    /**\n     * Hue value from 0 to 360\n     *\n     * @returns {int}\n     */\n\n  }, {\n    key: 'setHueRatio',\n\n\n    /**\n     * Sets the hue ratio, where 1.0 is 0, 0.5 is 180 and 0.0 is 360.\n     *\n     * @ignore\n     * @param {number} h Ratio from 1.0 to 0.0\n     */\n    value: function setHueRatio(h) {\n      this.hue = (1 - h) * 360;\n    }\n\n    /**\n     * Sets the saturation value\n     *\n     * @param {int} value Integer from 0 to 100\n     */\n\n  }, {\n    key: 'setSaturationRatio',\n\n\n    /**\n     * Sets the saturation ratio, where 1.0 is 100 and 0.0 is 0.\n     *\n     * @ignore\n     * @param {number} s Ratio from 0.0 to 1.0\n     */\n    value: function setSaturationRatio(s) {\n      this.saturation = s * 100;\n    }\n\n    /**\n     * Sets the 'value' channel value\n     *\n     * @param {int} value Integer from 0 to 100\n     */\n\n  }, {\n    key: 'setValueRatio',\n\n\n    /**\n     * Sets the value ratio, where 1.0 is 0 and 0.0 is 100.\n     *\n     * @ignore\n     * @param {number} v Ratio from 1.0 to 0.0\n     */\n    value: function setValueRatio(v) {\n      this.value = (1 - v) * 100;\n    }\n\n    /**\n     * Sets the alpha value. It will be rounded to 2 decimals.\n     *\n     * @param {int} value Float from 0.0 to 1.0\n     */\n\n  }, {\n    key: 'setAlphaRatio',\n\n\n    /**\n     * Sets the alpha ratio, where 1.0 is 0.0 and 0.0 is 1.0.\n     *\n     * @ignore\n     * @param {number} a Ratio from 1.0 to 0.0\n     */\n    value: function setAlphaRatio(a) {\n      this.alpha = 1 - a;\n    }\n\n    /**\n     * Sets the default color format\n     *\n     * @param {String} value Supported: 'rgb', 'hsl', 'hex'\n     */\n\n  }, {\n    key: 'isDesaturated',\n\n\n    /**\n     * Returns true if the saturation value is zero, false otherwise\n     *\n     * @returns {boolean}\n     */\n    value: function isDesaturated() {\n      return this.saturation === 0;\n    }\n\n    /**\n     * Returns true if the alpha value is zero, false otherwise\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isTransparent',\n    value: function isTransparent() {\n      return this.alpha === 0;\n    }\n\n    /**\n     * Returns true if the alpha value is numeric and less than 1, false otherwise\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasTransparency',\n    value: function hasTransparency() {\n      return this.hasAlpha() && this.alpha < 1;\n    }\n\n    /**\n     * Returns true if the alpha value is numeric, false otherwise\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasAlpha',\n    value: function hasAlpha() {\n      return !isNaN(this.alpha);\n    }\n\n    /**\n     * Returns a new HSVAColor object, based on the current color\n     *\n     * @returns {HSVAColor}\n     */\n\n  }, {\n    key: 'toObject',\n    value: function toObject() {\n      return new HSVAColor(this.hue, this.saturation, this.value, this.alpha);\n    }\n\n    /**\n     * Alias of toObject()\n     *\n     * @returns {HSVAColor}\n     */\n\n  }, {\n    key: 'toHsva',\n    value: function toHsva() {\n      return this.toObject();\n    }\n\n    /**\n     * Returns a new HSVAColor object with the ratio values (from 0.0 to 1.0),\n     * based on the current color.\n     *\n     * @ignore\n     * @returns {HSVAColor}\n     */\n\n  }, {\n    key: 'toHsvaRatio',\n    value: function toHsvaRatio() {\n      return new HSVAColor(this.hue / 360, this.saturation / 100, this.value / 100, this.alpha);\n    }\n\n    /**\n     * Converts the current color to its string representation,\n     * using the internal format of this instance.\n     *\n     * @returns {String}\n     */\n\n  }, {\n    key: 'toString',\n    value: function toString() {\n      return this.string();\n    }\n\n    /**\n     * Converts the current color to its string representation,\n     * using the given format.\n     *\n     * @param {String|null} format Format to convert to. If empty or null, the internal format will be used.\n     * @returns {String}\n     */\n\n  }, {\n    key: 'string',\n    value: function string() {\n      var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n\n      format = ColorItem.sanitizeFormat(format ? format : this.format);\n\n      if (!format) {\n        return this._color.round().string();\n      }\n\n      if (this._color[format] === undefined) {\n        throw new Error('Unsupported color format: \\'' + format + '\\'');\n      }\n\n      var str = this._color[format]();\n\n      return str.round ? str.round().string() : str;\n    }\n\n    /**\n     * Returns true if the given color values equals this one, false otherwise.\n     * The format is not compared.\n     * If any of the colors is invalid, the result will be false.\n     *\n     * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'equals',\n    value: function equals(color) {\n      color = color instanceof ColorItem ? color : new ColorItem(color);\n\n      if (!color.isValid() || !this.isValid()) {\n        return false;\n      }\n\n      return this.hue === color.hue && this.saturation === color.saturation && this.value === color.value && this.alpha === color.alpha;\n    }\n\n    /**\n     * Creates a copy of this instance\n     *\n     * @returns {ColorItem}\n     */\n\n  }, {\n    key: 'getClone',\n    value: function getClone() {\n      return new ColorItem(this._color, this.format);\n    }\n\n    /**\n     * Creates a copy of this instance, only copying the hue value,\n     * and setting the others to its max value.\n     *\n     * @returns {ColorItem}\n     */\n\n  }, {\n    key: 'getCloneHueOnly',\n    value: function getCloneHueOnly() {\n      return new ColorItem([this.hue, 100, 100, 1], this.format);\n    }\n\n    /**\n     * Creates a copy of this instance setting the alpha to the max.\n     *\n     * @returns {ColorItem}\n     */\n\n  }, {\n    key: 'getCloneOpaque',\n    value: function getCloneOpaque() {\n      return new ColorItem(this._color.alpha(1), this.format);\n    }\n\n    /**\n     * Converts the color to a RGB string\n     *\n     * @returns {String}\n     */\n\n  }, {\n    key: 'toRgbString',\n    value: function toRgbString() {\n      return this.string('rgb');\n    }\n\n    /**\n     * Converts the color to a Hexadecimal string\n     *\n     * @returns {String}\n     */\n\n  }, {\n    key: 'toHexString',\n    value: function toHexString() {\n      return this.string('hex');\n    }\n\n    /**\n     * Converts the color to a HSL string\n     *\n     * @returns {String}\n     */\n\n  }, {\n    key: 'toHslString',\n    value: function toHslString() {\n      return this.string('hsl');\n    }\n\n    /**\n     * Returns true if the color is dark, false otherwhise.\n     * This is useful to decide a text color.\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isDark',\n    value: function isDark() {\n      return this._color.isDark();\n    }\n\n    /**\n     * Returns true if the color is light, false otherwhise.\n     * This is useful to decide a text color.\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isLight',\n    value: function isLight() {\n      return this._color.isLight();\n    }\n\n    /**\n     * Generates a list of colors using the given hue-based formula or the given array of hue values.\n     * Hue formulas can be extended using ColorItem.colorFormulas static property.\n     *\n     * @param {String|Number[]} formula Examples: 'complementary', 'triad', 'tetrad', 'splitcomplement', [180, 270]\n     * @example let colors = color.generate('triad');\n     * @example let colors = color.generate([45, 80, 112, 200]);\n     * @returns {ColorItem[]}\n     */\n\n  }, {\n    key: 'generate',\n    value: function generate(formula) {\n      var hues = [];\n\n      if (Array.isArray(formula)) {\n        hues = formula;\n      } else if (!ColorItem.colorFormulas.hasOwnProperty(formula)) {\n        throw new Error('No color formula found with the name \\'' + formula + '\\'.');\n      } else {\n        hues = ColorItem.colorFormulas[formula];\n      }\n\n      var colors = [],\n          mainColor = this._color,\n          format = this.format;\n\n      hues.forEach(function (hue) {\n        var levels = [hue ? (mainColor.hue() + hue) % 360 : mainColor.hue(), mainColor.saturationv(), mainColor.value(), mainColor.alpha()];\n\n        colors.push(new ColorItem(levels, format));\n      });\n\n      return colors;\n    }\n  }, {\n    key: 'hue',\n    get: function get() {\n      return this._color.hue();\n    }\n\n    /**\n     * Saturation value from 0 to 100\n     *\n     * @returns {int}\n     */\n    ,\n\n\n    /**\n     * Sets the hue value\n     *\n     * @param {int} value Integer from 0 to 360\n     */\n    set: function set(value) {\n      this._color = this._color.hue(value);\n    }\n  }, {\n    key: 'saturation',\n    get: function get() {\n      return this._color.saturationv();\n    }\n\n    /**\n     * Value channel value from 0 to 100\n     *\n     * @returns {int}\n     */\n    ,\n    set: function set(value) {\n      this._color = this._color.saturationv(value);\n    }\n  }, {\n    key: 'value',\n    get: function get() {\n      return this._color.value();\n    }\n\n    /**\n     * Alpha value from 0.0 to 1.0\n     *\n     * @returns {number}\n     */\n    ,\n    set: function set(value) {\n      this._color = this._color.value(value);\n    }\n  }, {\n    key: 'alpha',\n    get: function get() {\n      var a = this._color.alpha();\n\n      return isNaN(a) ? 1 : a;\n    }\n\n    /**\n     * Default color format to convert to when calling toString() or string()\n     *\n     * @returns {String} 'rgb', 'hsl', 'hex' or ''\n     */\n    ,\n    set: function set(value) {\n      // 2 decimals max\n      this._color = this._color.alpha(Math.round(value * 100) / 100);\n    }\n  }, {\n    key: 'format',\n    get: function get() {\n      return this._format ? this._format : this._color.model;\n    },\n    set: function set(value) {\n      this._format = ColorItem.sanitizeFormat(value);\n    }\n  }], [{\n    key: 'parse',\n    value: function parse(color) {\n      if (color instanceof _color2.default) {\n        return color;\n      }\n\n      if (color instanceof ColorItem) {\n        return color._color;\n      }\n\n      var format = null;\n\n      if (color instanceof HSVAColor) {\n        color = [color.h, color.s, color.v, isNaN(color.a) ? 1 : color.a];\n      } else {\n        color = ColorItem.sanitizeString(color);\n      }\n\n      if (color === null) {\n        return null;\n      }\n\n      if (Array.isArray(color)) {\n        format = 'hsv';\n      }\n\n      try {\n        return (0, _color2.default)(color, format);\n      } catch (e) {\n        return null;\n      }\n    }\n\n    /**\n     * Sanitizes a color string, adding missing hash to hexadecimal colors\n     * and converting 'transparent' to a color code.\n     *\n     * @param {String|*} str Color string\n     * @example let colorStr = ColorItem.sanitizeString('ffaa00');\n     * @static\n     * @returns {String|*}\n     */\n\n  }, {\n    key: 'sanitizeString',\n    value: function sanitizeString(str) {\n      if (!(typeof str === 'string' || str instanceof String)) {\n        return str;\n      }\n\n      if (str.match(/^[0-9a-f]{2,}$/i)) {\n        return '#' + str;\n      }\n\n      if (str.toLowerCase() === 'transparent') {\n        return '#FFFFFF00';\n      }\n\n      return str;\n    }\n\n    /**\n     * Detects if a value is a string and a color in hexadecimal format (in any variant).\n     *\n     * @param {String} str\n     * @example ColorItem.isHex('rgba(0,0,0)'); // false\n     * @example ColorItem.isHex('ffaa00'); // true\n     * @example ColorItem.isHex('#ffaa00'); // true\n     * @static\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isHex',\n    value: function isHex(str) {\n      if (!(typeof str === 'string' || str instanceof String)) {\n        return false;\n      }\n\n      return !!str.match(/^#?[0-9a-f]{2,}$/i);\n    }\n\n    /**\n     * Sanitizes a color format to one supported by web browsers.\n     * Returns an empty string of the format can't be recognised.\n     *\n     * @param {String|*} format\n     * @example ColorItem.sanitizeFormat('rgba'); // 'rgb'\n     * @example ColorItem.isHex('hex8'); // 'hex'\n     * @example ColorItem.isHex('invalid'); // ''\n     * @static\n     * @returns {String} 'rgb', 'hsl', 'hex' or ''.\n     */\n\n  }, {\n    key: 'sanitizeFormat',\n    value: function sanitizeFormat(format) {\n      switch (format) {\n        case 'hex':\n        case 'hex3':\n        case 'hex4':\n        case 'hex6':\n        case 'hex8':\n          return 'hex';\n        case 'rgb':\n        case 'rgba':\n        case 'keyword':\n        case 'name':\n          return 'rgb';\n        case 'hsl':\n        case 'hsla':\n        case 'hsv':\n        case 'hsva':\n        case 'hwb': // HWB this is supported by Qix Color, but not by browsers\n        case 'hwba':\n          return 'hsl';\n        default:\n          return '';\n      }\n    }\n  }]);\n\n  return ColorItem;\n}();\n\n/**\n * List of hue-based color formulas used by ColorItem.prototype.generate()\n *\n * @static\n * @type {{complementary: number[], triad: number[], tetrad: number[], splitcomplement: number[]}}\n */\n\n\nColorItem.colorFormulas = {\n  complementary: [180],\n  triad: [0, 120, 240],\n  tetrad: [0, 90, 180, 270],\n  splitcomplement: [0, 72, 216]\n};\n\nexports.default = ColorItem;\nexports.HSVAColor = HSVAColor;\nexports.ColorItem = ColorItem;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n/**\n * @module\n */\n\n// adjust these values accordingly to the sass vars\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar sassVars = {\n  'bar_size_short': 16,\n  'base_margin': 6,\n  'columns': 6\n};\n\nvar sliderSize = sassVars.bar_size_short * sassVars.columns + sassVars.base_margin * (sassVars.columns - 1);\n\n/**\n * Colorpicker default options\n */\nexports.default = {\n  /**\n   * Custom class to be added to the `.colorpicker-element` element\n   *\n   * @type {String|null}\n   * @default null\n   */\n  customClass: null,\n  /**\n   * Sets a initial color, ignoring the one from the element/input value or the data-color attribute.\n   *\n   * @type {(String|ColorItem|boolean)}\n   * @default false\n   */\n  color: false,\n  /**\n   * Fallback color to use when the given color is invalid.\n   * If false, the latest valid color will be used as a fallback.\n   *\n   * @type {String|ColorItem|boolean}\n   * @default false\n   */\n  fallbackColor: false,\n  /**\n   * Forces an specific color format. If 'auto', it will be automatically detected the first time only,\n   * but if null it will be always recalculated.\n   *\n   * Note that the ending 'a' of the format meaning \"alpha\" has currently no effect, meaning that rgb is the same as\n   * rgba excepting if the alpha channel is disabled (see useAlpha).\n   *\n   * @type {('rgb'|'hex'|'hsl'|'auto'|null)}\n   * @default 'auto'\n   */\n  format: 'auto',\n  /**\n   * Horizontal mode layout.\n   *\n   * If true, the hue and alpha channel bars will be rendered horizontally, above the saturation selector.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  horizontal: false,\n  /**\n   * Forces to show the colorpicker as an inline element.\n   *\n   * Note that if there is no container specified, the inline element\n   * will be added to the body, so you may want to set the container option.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  inline: false,\n  /**\n   * Container where the colorpicker is appended to in the DOM.\n   *\n   * If is a string (CSS selector), the colorpicker will be placed inside this container.\n   * If true, the `.colorpicker-element` element itself will be used as the container.\n   * If false, the document body is used as the container, unless it is a popover (in this case it is appended to the\n   * popover body instead).\n   *\n   * @type {String|boolean}\n   * @default false\n   */\n  container: false,\n  /**\n   * Bootstrap Popover options.\n   * The trigger, content and html options are always ignored.\n   *\n   * @type {boolean}\n   * @default Object\n   */\n  popover: {\n    animation: true,\n    placement: 'bottom',\n    fallbackPlacement: 'flip'\n  },\n  /**\n   * If true, loads the 'debugger' extension automatically, which logs the events in the console\n   * @type {boolean}\n   * @default false\n   */\n  debug: false,\n  /**\n   * Child CSS selector for the colorpicker input.\n   *\n   * @type {String}\n   * @default 'input'\n   */\n  input: 'input',\n  /**\n   * Child CSS selector for the colorpicker addon.\n   * If it exists, the child <i> element background will be changed on color change.\n   *\n   * @type {String}\n   * @default '.colorpicker-trigger, .colorpicker-input-addon'\n   */\n  addon: '.colorpicker-input-addon',\n  /**\n   * If true, the input content will be replaced always with a valid color,\n   * if false, the invalid color will be left in the input,\n   *   while the internal color object will still resolve into a valid one.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  autoInputFallback: true,\n  /**\n   * If true a hash will be prepended to hexadecimal colors.\n   * If false, the hash will be removed.\n   * This only affects the input values in hexadecimal format.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useHashPrefix: true,\n  /**\n   * If true, the alpha channel bar will be displayed no matter what.\n   *\n   * If false, it will be always hidden and alpha channel will be disabled also programmatically, meaning that\n   * the selected or typed color will be always opaque.\n   *\n   * If null, the alpha channel will be automatically disabled/enabled depending if the initial color format supports\n   * alpha or not.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useAlpha: true,\n  /**\n   * Colorpicker widget template\n   * @type {String}\n   * @example\n   * <!-- This is the default template: -->\n   * <div class=\"colorpicker\">\n   *   <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-alpha\">\n   *     <div class=\"colorpicker-alpha-color\"></div>\n   *     <i class=\"colorpicker-guide\"></i>\n   *   </div>\n   * </div>\n   */\n  template: '<div class=\"colorpicker\">\\n      <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\\n      <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\\n      <div class=\"colorpicker-alpha\">\\n        <div class=\"colorpicker-alpha-color\"></div>\\n        <i class=\"colorpicker-guide\"></i>\\n      </div>\\n    </div>',\n  /**\n   *\n   * Associative object with the extension class name and its config.\n   * Colorpicker comes with many bundled extensions: debugger, palette, preview and swatches (a superset of palette).\n   *\n   * @type {Object[]}\n   * @example\n   *   extensions: [\n   *     {\n   *       name: 'swatches'\n   *       options: {\n   *         colors: {\n   *           'primary': '#337ab7',\n   *           'success': '#5cb85c',\n   *           'info': '#5bc0de',\n   *           'warning': '#f0ad4e',\n   *           'danger': '#d9534f'\n   *         },\n   *         namesAsValues: true\n   *       }\n   *     }\n   *   ]\n   */\n  extensions: [{\n    name: 'preview',\n    options: {\n      showText: true\n    }\n  }],\n  /**\n   * Vertical sliders configuration\n   * @type {Object}\n   */\n  sliders: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setHueRatio'\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setAlphaRatio'\n    }\n  },\n  /**\n   * Horizontal sliders configuration\n   * @type {Object}\n   */\n  slidersHorz: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setHueRatio',\n      callTop: false\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setAlphaRatio',\n      callTop: false\n    }\n  }\n};\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Extension2 = __webpack_require__(1);\n\nvar _Extension3 = _interopRequireDefault(_Extension2);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar defaults = {\n  /**\n   * Key-value pairs defining a color alias and its CSS color representation.\n   *\n   * They can also be just an array of values. In that case, no special names are used, only the real colors.\n   *\n   * @type {Object|Array}\n   * @default null\n   * @example\n   *  {\n   *   'black': '#000000',\n   *   'white': '#ffffff',\n   *   'red': '#FF0000',\n   *   'default': '#777777',\n   *   'primary': '#337ab7',\n   *   'success': '#5cb85c',\n   *   'info': '#5bc0de',\n   *   'warning': '#f0ad4e',\n   *   'danger': '#d9534f'\n   *  }\n   *\n   * @example ['#f0ad4e', '#337ab7', '#5cb85c']\n   */\n  colors: null,\n  /**\n   * If true, when a color swatch is selected the name (alias) will be used as input value,\n   * otherwise the swatch real color value will be used.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  namesAsValues: true\n};\n\n/**\n * Palette extension\n * @ignore\n */\n\nvar Palette = function (_Extension) {\n  _inherits(Palette, _Extension);\n\n  _createClass(Palette, [{\n    key: 'colors',\n\n\n    /**\n     * @returns {Object|Array}\n     */\n    get: function get() {\n      return this.options.colors;\n    }\n  }]);\n\n  function Palette(colorpicker) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Palette);\n\n    var _this = _possibleConstructorReturn(this, (Palette.__proto__ || Object.getPrototypeOf(Palette)).call(this, colorpicker, _jquery2.default.extend(true, {}, defaults, options)));\n\n    if (!Array.isArray(_this.options.colors) && _typeof(_this.options.colors) !== 'object') {\n      _this.options.colors = null;\n    }\n    return _this;\n  }\n\n  /**\n   * @returns {int}\n   */\n\n\n  _createClass(Palette, [{\n    key: 'getLength',\n    value: function getLength() {\n      if (!this.options.colors) {\n        return 0;\n      }\n\n      if (Array.isArray(this.options.colors)) {\n        return this.options.colors.length;\n      }\n\n      if (_typeof(this.options.colors) === 'object') {\n        return Object.keys(this.options.colors).length;\n      }\n\n      return 0;\n    }\n  }, {\n    key: 'resolveColor',\n    value: function resolveColor(color) {\n      var realColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n      if (this.getLength() <= 0) {\n        return false;\n      }\n\n      // Array of colors\n      if (Array.isArray(this.options.colors)) {\n        if (this.options.colors.indexOf(color) >= 0) {\n          return color;\n        }\n        if (this.options.colors.indexOf(color.toUpperCase()) >= 0) {\n          return color.toUpperCase();\n        }\n        if (this.options.colors.indexOf(color.toLowerCase()) >= 0) {\n          return color.toLowerCase();\n        }\n        return false;\n      }\n\n      if (_typeof(this.options.colors) !== 'object') {\n        return false;\n      }\n\n      // Map of objects\n      if (!this.options.namesAsValues || realColor) {\n        return this.getValue(color, false);\n      }\n      return this.getName(color, this.getName('#' + color));\n    }\n\n    /**\n     * Given a color value, returns the corresponding color name or defaultValue.\n     *\n     * @param {String} value\n     * @param {*} defaultValue\n     * @returns {*}\n     */\n\n  }, {\n    key: 'getName',\n    value: function getName(value) {\n      var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (!(typeof value === 'string') || !this.options.colors) {\n        return defaultValue;\n      }\n      for (var name in this.options.colors) {\n        if (!this.options.colors.hasOwnProperty(name)) {\n          continue;\n        }\n        if (this.options.colors[name].toLowerCase() === value.toLowerCase()) {\n          return name;\n        }\n      }\n      return defaultValue;\n    }\n\n    /**\n     * Given a color name, returns the corresponding color value or defaultValue.\n     *\n     * @param {String} name\n     * @param {*} defaultValue\n     * @returns {*}\n     */\n\n  }, {\n    key: 'getValue',\n    value: function getValue(name) {\n      var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (!(typeof name === 'string') || !this.options.colors) {\n        return defaultValue;\n      }\n      if (this.options.colors.hasOwnProperty(name)) {\n        return this.options.colors[name];\n      }\n      return defaultValue;\n    }\n  }]);\n\n  return Palette;\n}(_Extension3.default);\n\nexports.default = Palette;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* MIT license */\nvar cssKeywords = __webpack_require__(5);\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _Colorpicker = __webpack_require__(8);\n\nvar _Colorpicker2 = _interopRequireDefault(_Colorpicker);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar plugin = 'colorpicker';\n\n_jquery2.default[plugin] = _Colorpicker2.default;\n\n// Colorpicker jQuery Plugin API\n_jquery2.default.fn[plugin] = function (option) {\n  var fnArgs = Array.prototype.slice.call(arguments, 1),\n      isSingleElement = this.length === 1,\n      returnValue = null;\n\n  var $elements = this.each(function () {\n    var $this = (0, _jquery2.default)(this),\n        inst = $this.data(plugin),\n        options = (typeof option === 'undefined' ? 'undefined' : _typeof(option)) === 'object' ? option : {};\n\n    // Create instance if does not exist\n    if (!inst) {\n      inst = new _Colorpicker2.default(this, options);\n      $this.data(plugin, inst);\n    }\n\n    if (!isSingleElement) {\n      return;\n    }\n\n    returnValue = $this;\n\n    if (typeof option === 'string') {\n      if (option === 'colorpicker') {\n        // Return colorpicker instance: e.g. .colorpicker('colorpicker')\n        returnValue = inst;\n      } else if (_jquery2.default.isFunction(inst[option])) {\n        // Return method call return value: e.g. .colorpicker('isEnabled')\n        returnValue = inst[option].apply(inst, fnArgs);\n      } else {\n        // Return property value: e.g. .colorpicker('element')\n        returnValue = inst[option];\n      }\n    }\n  });\n\n  return isSingleElement ? returnValue : $elements;\n};\n\n_jquery2.default.fn[plugin].constructor = _Colorpicker2.default;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Extension = __webpack_require__(1);\n\nvar _Extension2 = _interopRequireDefault(_Extension);\n\nvar _options = __webpack_require__(3);\n\nvar _options2 = _interopRequireDefault(_options);\n\nvar _extensions = __webpack_require__(9);\n\nvar _extensions2 = _interopRequireDefault(_extensions);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nvar _SliderHandler = __webpack_require__(13);\n\nvar _SliderHandler2 = _interopRequireDefault(_SliderHandler);\n\nvar _PopupHandler = __webpack_require__(14);\n\nvar _PopupHandler2 = _interopRequireDefault(_PopupHandler);\n\nvar _InputHandler = __webpack_require__(15);\n\nvar _InputHandler2 = _interopRequireDefault(_InputHandler);\n\nvar _ColorHandler = __webpack_require__(22);\n\nvar _ColorHandler2 = _interopRequireDefault(_ColorHandler);\n\nvar _PickerHandler = __webpack_require__(23);\n\nvar _PickerHandler2 = _interopRequireDefault(_PickerHandler);\n\nvar _AddonHandler = __webpack_require__(24);\n\nvar _AddonHandler2 = _interopRequireDefault(_AddonHandler);\n\nvar _ColorItem = __webpack_require__(2);\n\nvar _ColorItem2 = _interopRequireDefault(_ColorItem);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar colorPickerIdCounter = 0;\nvar root = typeof self !== 'undefined' ? self : undefined; // window\n\n/**\n * Colorpicker widget class\n */\n\nvar Colorpicker = function () {\n  _createClass(Colorpicker, [{\n    key: 'color',\n\n\n    /**\n     * Internal color object\n     *\n     * @type {Color|null}\n     */\n    get: function get() {\n      return this.colorHandler.color;\n    }\n\n    /**\n     * Internal color format\n     *\n     * @type {String|null}\n     */\n\n  }, {\n    key: 'format',\n    get: function get() {\n      return this.colorHandler.format;\n    }\n\n    /**\n     * Getter of the picker element\n     *\n     * @returns {jQuery|HTMLElement}\n     */\n\n  }, {\n    key: 'picker',\n    get: function get() {\n      return this.pickerHandler.picker;\n    }\n\n    /**\n     * @fires Colorpicker#colorpickerCreate\n     * @param {Object|String} element\n     * @param {Object} options\n     * @constructor\n     */\n\n  }], [{\n    key: 'Color',\n\n    /**\n     * Color class\n     *\n     * @static\n     * @type {Color}\n     */\n    get: function get() {\n      return _ColorItem2.default;\n    }\n\n    /**\n     * Extension class\n     *\n     * @static\n     * @type {Extension}\n     */\n\n  }, {\n    key: 'Extension',\n    get: function get() {\n      return _Extension2.default;\n    }\n  }]);\n\n  function Colorpicker(element, options) {\n    _classCallCheck(this, Colorpicker);\n\n    colorPickerIdCounter += 1;\n    /**\n     * The colorpicker instance number\n     * @type {number}\n     */\n    this.id = colorPickerIdCounter;\n\n    /**\n     * Latest colorpicker event\n     *\n     * @type {{name: String, e: *}}\n     */\n    this.lastEvent = {\n      alias: null,\n      e: null\n    };\n\n    /**\n     * The element that the colorpicker is bound to\n     *\n     * @type {*|jQuery}\n     */\n    this.element = (0, _jquery2.default)(element).addClass('colorpicker-element').attr('data-colorpicker-id', this.id);\n\n    /**\n     * @type {defaults}\n     */\n    this.options = _jquery2.default.extend(true, {}, _options2.default, options, this.element.data());\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.disabled = false;\n\n    /**\n     * Extensions added to this instance\n     *\n     * @type {Extension[]}\n     */\n    this.extensions = [];\n\n    /**\n     * The element where the\n     * @type {*|jQuery}\n     */\n    this.container = this.options.container === true || this.options.container !== true && this.options.inline === true ? this.element : this.options.container;\n\n    this.container = this.container !== false ? (0, _jquery2.default)(this.container) : false;\n\n    /**\n     * @type {InputHandler}\n     */\n    this.inputHandler = new _InputHandler2.default(this);\n    /**\n     * @type {ColorHandler}\n     */\n    this.colorHandler = new _ColorHandler2.default(this);\n    /**\n     * @type {SliderHandler}\n     */\n    this.sliderHandler = new _SliderHandler2.default(this);\n    /**\n     * @type {PopupHandler}\n     */\n    this.popupHandler = new _PopupHandler2.default(this, root);\n    /**\n     * @type {PickerHandler}\n     */\n    this.pickerHandler = new _PickerHandler2.default(this);\n    /**\n     * @type {AddonHandler}\n     */\n    this.addonHandler = new _AddonHandler2.default(this);\n\n    this.init();\n\n    // Emit a create event\n    (0, _jquery2.default)(_jquery2.default.proxy(function () {\n      /**\n       * (Colorpicker) When the Colorpicker instance has been created and the DOM is ready.\n       *\n       * @event Colorpicker#colorpickerCreate\n       */\n      this.trigger('colorpickerCreate');\n    }, this));\n  }\n\n  /**\n   * Initializes the plugin\n   * @private\n   */\n\n\n  _createClass(Colorpicker, [{\n    key: 'init',\n    value: function init() {\n      // Init addon\n      this.addonHandler.bind();\n\n      // Init input\n      this.inputHandler.bind();\n\n      // Init extensions (before initializing the color)\n      this.initExtensions();\n\n      // Init color\n      this.colorHandler.bind();\n\n      // Init picker\n      this.pickerHandler.bind();\n\n      // Init sliders and popup\n      this.sliderHandler.bind();\n      this.popupHandler.bind();\n\n      // Inject into the DOM (this may make it visible)\n      this.pickerHandler.attach();\n\n      // Update all components\n      this.update();\n\n      if (this.inputHandler.isDisabled()) {\n        this.disable();\n      }\n    }\n\n    /**\n     * Initializes the plugin extensions\n     * @private\n     */\n\n  }, {\n    key: 'initExtensions',\n    value: function initExtensions() {\n      var _this = this;\n\n      if (!Array.isArray(this.options.extensions)) {\n        this.options.extensions = [];\n      }\n\n      if (this.options.debug) {\n        this.options.extensions.push({ name: 'debugger' });\n      }\n\n      // Register and instantiate extensions\n      this.options.extensions.forEach(function (ext) {\n        _this.registerExtension(Colorpicker.extensions[ext.name.toLowerCase()], ext.options || {});\n      });\n    }\n\n    /**\n     * Creates and registers the given extension\n     *\n     * @param {Extension} ExtensionClass The extension class to instantiate\n     * @param {Object} [config] Extension configuration\n     * @returns {Extension}\n     */\n\n  }, {\n    key: 'registerExtension',\n    value: function registerExtension(ExtensionClass) {\n      var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n      var ext = new ExtensionClass(this, config);\n\n      this.extensions.push(ext);\n      return ext;\n    }\n\n    /**\n     * Destroys the current instance\n     *\n     * @fires Colorpicker#colorpickerDestroy\n     */\n\n  }, {\n    key: 'destroy',\n    value: function destroy() {\n      var color = this.color;\n\n      this.sliderHandler.unbind();\n      this.inputHandler.unbind();\n      this.popupHandler.unbind();\n      this.colorHandler.unbind();\n      this.addonHandler.unbind();\n      this.pickerHandler.unbind();\n\n      this.element.removeClass('colorpicker-element').removeData('colorpicker', 'color').off('.colorpicker');\n\n      /**\n       * (Colorpicker) When the instance is destroyed with all events unbound.\n       *\n       * @event Colorpicker#colorpickerDestroy\n       */\n      this.trigger('colorpickerDestroy', color);\n    }\n\n    /**\n     * Shows the colorpicker widget if hidden.\n     * If the colorpicker is disabled this call will be ignored.\n     *\n     * @fires Colorpicker#colorpickerShow\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'show',\n    value: function show(e) {\n      this.popupHandler.show(e);\n    }\n\n    /**\n     * Hides the colorpicker widget.\n     *\n     * @fires Colorpicker#colorpickerHide\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'hide',\n    value: function hide(e) {\n      this.popupHandler.hide(e);\n    }\n\n    /**\n     * Toggles the colorpicker between visible and hidden.\n     *\n     * @fires Colorpicker#colorpickerShow\n     * @fires Colorpicker#colorpickerHide\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'toggle',\n    value: function toggle(e) {\n      this.popupHandler.toggle(e);\n    }\n\n    /**\n     * Returns the current color value as string\n     *\n     * @param {String|*} [defaultValue]\n     * @returns {String|*}\n     */\n\n  }, {\n    key: 'getValue',\n    value: function getValue() {\n      var defaultValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n\n      var val = this.colorHandler.color;\n\n      val = val instanceof _ColorItem2.default ? val : defaultValue;\n\n      if (val instanceof _ColorItem2.default) {\n        return val.string(this.format);\n      }\n\n      return val;\n    }\n\n    /**\n     * Sets the color manually\n     *\n     * @fires Colorpicker#colorpickerChange\n     * @param {String|Color} val\n     */\n\n  }, {\n    key: 'setValue',\n    value: function setValue(val) {\n      if (this.isDisabled()) {\n        return;\n      }\n      var ch = this.colorHandler;\n\n      if (ch.hasColor() && !!val && ch.color.equals(val) || !ch.hasColor() && !val) {\n        // same color or still empty\n        return;\n      }\n\n      ch.color = val ? ch.createColor(val, this.options.autoInputFallback) : null;\n\n      /**\n       * (Colorpicker) When the color is set programmatically with setValue().\n       *\n       * @event Colorpicker#colorpickerChange\n       */\n      this.trigger('colorpickerChange', ch.color, val);\n\n      // force update if color has changed to empty\n      this.update();\n    }\n\n    /**\n     * Updates the UI and the input color according to the internal color.\n     *\n     * @fires Colorpicker#colorpickerUpdate\n     */\n\n  }, {\n    key: 'update',\n    value: function update() {\n      if (this.colorHandler.hasColor()) {\n        this.inputHandler.update();\n      } else {\n        this.colorHandler.assureColor();\n      }\n\n      this.addonHandler.update();\n      this.pickerHandler.update();\n\n      /**\n       * (Colorpicker) Fired when the widget is updated.\n       *\n       * @event Colorpicker#colorpickerUpdate\n       */\n      this.trigger('colorpickerUpdate');\n    }\n\n    /**\n     * Enables the widget and the input if any\n     *\n     * @fires Colorpicker#colorpickerEnable\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'enable',\n    value: function enable() {\n      this.inputHandler.enable();\n      this.disabled = false;\n      this.picker.removeClass('colorpicker-disabled');\n\n      /**\n       * (Colorpicker) When the widget has been enabled.\n       *\n       * @event Colorpicker#colorpickerEnable\n       */\n      this.trigger('colorpickerEnable');\n      return true;\n    }\n\n    /**\n     * Disables the widget and the input if any\n     *\n     * @fires Colorpicker#colorpickerDisable\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'disable',\n    value: function disable() {\n      this.inputHandler.disable();\n      this.disabled = true;\n      this.picker.addClass('colorpicker-disabled');\n\n      /**\n       * (Colorpicker) When the widget has been disabled.\n       *\n       * @event Colorpicker#colorpickerDisable\n       */\n      this.trigger('colorpickerDisable');\n      return true;\n    }\n\n    /**\n     * Returns true if this instance is enabled\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isEnabled',\n    value: function isEnabled() {\n      return !this.isDisabled();\n    }\n\n    /**\n     * Returns true if this instance is disabled\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isDisabled',\n    value: function isDisabled() {\n      return this.disabled === true;\n    }\n\n    /**\n     * Triggers a Colorpicker event.\n     *\n     * @param eventName\n     * @param color\n     * @param value\n     */\n\n  }, {\n    key: 'trigger',\n    value: function trigger(eventName) {\n      var color = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var value = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n      this.element.trigger({\n        type: eventName,\n        colorpicker: this,\n        color: color ? color : this.color,\n        value: value ? value : this.getValue()\n      });\n    }\n  }]);\n\n  return Colorpicker;\n}();\n\n/**\n * Colorpicker extension classes, indexed by extension name\n *\n * @static\n * @type {Object} a map between the extension name and its class\n */\n\n\nColorpicker.extensions = _extensions2.default;\n\nexports.default = Colorpicker;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Palette = exports.Swatches = exports.Preview = exports.Debugger = undefined;\n\nvar _Debugger = __webpack_require__(10);\n\nvar _Debugger2 = _interopRequireDefault(_Debugger);\n\nvar _Preview = __webpack_require__(11);\n\nvar _Preview2 = _interopRequireDefault(_Preview);\n\nvar _Swatches = __webpack_require__(12);\n\nvar _Swatches2 = _interopRequireDefault(_Swatches);\n\nvar _Palette = __webpack_require__(4);\n\nvar _Palette2 = _interopRequireDefault(_Palette);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.Debugger = _Debugger2.default;\nexports.Preview = _Preview2.default;\nexports.Swatches = _Swatches2.default;\nexports.Palette = _Palette2.default;\nexports.default = {\n  'debugger': _Debugger2.default,\n  'preview': _Preview2.default,\n  'swatches': _Swatches2.default,\n  'palette': _Palette2.default\n};\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _Extension2 = __webpack_require__(1);\n\nvar _Extension3 = _interopRequireDefault(_Extension2);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n/**\n * Debugger extension class\n * @alias DebuggerExtension\n * @ignore\n */\nvar Debugger = function (_Extension) {\n  _inherits(Debugger, _Extension);\n\n  function Debugger(colorpicker) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Debugger);\n\n    /**\n     * @type {number}\n     */\n    var _this = _possibleConstructorReturn(this, (Debugger.__proto__ || Object.getPrototypeOf(Debugger)).call(this, colorpicker, options));\n\n    _this.eventCounter = 0;\n    if (_this.colorpicker.inputHandler.hasInput()) {\n      _this.colorpicker.inputHandler.input.on('change.colorpicker-ext', _jquery2.default.proxy(_this.onChangeInput, _this));\n    }\n    return _this;\n  }\n\n  /**\n   * @fires DebuggerExtension#colorpickerDebug\n   * @param {string} eventName\n   * @param {*} args\n   */\n\n\n  _createClass(Debugger, [{\n    key: 'log',\n    value: function log(eventName) {\n      var _console;\n\n      for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      this.eventCounter += 1;\n\n      var logMessage = '#' + this.eventCounter + ': Colorpicker#' + this.colorpicker.id + ' [' + eventName + ']';\n\n      (_console = console).debug.apply(_console, [logMessage].concat(args));\n\n      /**\n       * Whenever the debugger logs an event, this other event is emitted.\n       *\n       * @event DebuggerExtension#colorpickerDebug\n       * @type {object} The event object\n       * @property {Colorpicker} colorpicker The Colorpicker instance\n       * @property {ColorItem} color The color instance\n       * @property {{debugger: DebuggerExtension, eventName: String, logArgs: Array, logMessage: String}} debug\n       *  The debug info\n       */\n      this.colorpicker.element.trigger({\n        type: 'colorpickerDebug',\n        colorpicker: this.colorpicker,\n        color: this.color,\n        value: null,\n        debug: {\n          debugger: this,\n          eventName: eventName,\n          logArgs: args,\n          logMessage: logMessage\n        }\n      });\n    }\n  }, {\n    key: 'resolveColor',\n    value: function resolveColor(color) {\n      var realColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n      this.log('resolveColor()', color, realColor);\n      return false;\n    }\n  }, {\n    key: 'onCreate',\n    value: function onCreate(event) {\n      this.log('colorpickerCreate');\n      return _get(Debugger.prototype.__proto__ || Object.getPrototypeOf(Debugger.prototype), 'onCreate', this).call(this, event);\n    }\n  }, {\n    key: 'onDestroy',\n    value: function onDestroy(event) {\n      this.log('colorpickerDestroy');\n      this.eventCounter = 0;\n\n      if (this.colorpicker.inputHandler.hasInput()) {\n        this.colorpicker.inputHandler.input.off('.colorpicker-ext');\n      }\n\n      return _get(Debugger.prototype.__proto__ || Object.getPrototypeOf(Debugger.prototype), 'onDestroy', this).call(this, event);\n    }\n  }, {\n    key: 'onUpdate',\n    value: function onUpdate(event) {\n      this.log('colorpickerUpdate');\n    }\n\n    /**\n     * @listens Colorpicker#change\n     * @param {Event} event\n     */\n\n  }, {\n    key: 'onChangeInput',\n    value: function onChangeInput(event) {\n      this.log('input:change.colorpicker', event.value, event.color);\n    }\n  }, {\n    key: 'onChange',\n    value: function onChange(event) {\n      this.log('colorpickerChange', event.value, event.color);\n    }\n  }, {\n    key: 'onInvalid',\n    value: function onInvalid(event) {\n      this.log('colorpickerInvalid', event.value, event.color);\n    }\n  }, {\n    key: 'onHide',\n    value: function onHide(event) {\n      this.log('colorpickerHide');\n      this.eventCounter = 0;\n    }\n  }, {\n    key: 'onShow',\n    value: function onShow(event) {\n      this.log('colorpickerShow');\n    }\n  }, {\n    key: 'onDisable',\n    value: function onDisable(event) {\n      this.log('colorpickerDisable');\n    }\n  }, {\n    key: 'onEnable',\n    value: function onEnable(event) {\n      this.log('colorpickerEnable');\n    }\n  }]);\n\n  return Debugger;\n}(_Extension3.default);\n\nexports.default = Debugger;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _Extension2 = __webpack_require__(1);\n\nvar _Extension3 = _interopRequireDefault(_Extension2);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n/**\n * Color preview extension\n * @ignore\n */\nvar Preview = function (_Extension) {\n  _inherits(Preview, _Extension);\n\n  function Preview(colorpicker) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Preview);\n\n    var _this = _possibleConstructorReturn(this, (Preview.__proto__ || Object.getPrototypeOf(Preview)).call(this, colorpicker, _jquery2.default.extend(true, {}, {\n      template: '<div class=\"colorpicker-bar colorpicker-preview\"><div /></div>',\n      showText: true,\n      format: colorpicker.format\n    }, options)));\n\n    _this.element = (0, _jquery2.default)(_this.options.template);\n    _this.elementInner = _this.element.find('div');\n    return _this;\n  }\n\n  _createClass(Preview, [{\n    key: 'onCreate',\n    value: function onCreate(event) {\n      _get(Preview.prototype.__proto__ || Object.getPrototypeOf(Preview.prototype), 'onCreate', this).call(this, event);\n      this.colorpicker.picker.append(this.element);\n    }\n  }, {\n    key: 'onUpdate',\n    value: function onUpdate(event) {\n      _get(Preview.prototype.__proto__ || Object.getPrototypeOf(Preview.prototype), 'onUpdate', this).call(this, event);\n\n      if (!event.color) {\n        this.elementInner.css('backgroundColor', null).css('color', null).html('');\n        return;\n      }\n\n      this.elementInner.css('backgroundColor', event.color.toRgbString());\n\n      if (this.options.showText) {\n        this.elementInner.html(event.color.string(this.options.format || this.colorpicker.format));\n\n        if (event.color.isDark() && event.color.alpha > 0.5) {\n          this.elementInner.css('color', 'white');\n        } else {\n          this.elementInner.css('color', 'black');\n        }\n      }\n    }\n  }]);\n\n  return Preview;\n}(_Extension3.default);\n\nexports.default = Preview;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _Palette2 = __webpack_require__(4);\n\nvar _Palette3 = _interopRequireDefault(_Palette2);\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar defaults = {\n  barTemplate: '<div class=\"colorpicker-bar colorpicker-swatches\">\\n                    <div class=\"colorpicker-swatches--inner\"></div>\\n                </div>',\n  swatchTemplate: '<i class=\"colorpicker-swatch\"><i class=\"colorpicker-swatch--inner\"></i></i>'\n};\n\n/**\n * Color swatches extension\n * @ignore\n */\n\nvar Swatches = function (_Palette) {\n  _inherits(Swatches, _Palette);\n\n  function Swatches(colorpicker) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Swatches);\n\n    var _this = _possibleConstructorReturn(this, (Swatches.__proto__ || Object.getPrototypeOf(Swatches)).call(this, colorpicker, _jquery2.default.extend(true, {}, defaults, options)));\n\n    _this.element = null;\n    return _this;\n  }\n\n  _createClass(Swatches, [{\n    key: 'isEnabled',\n    value: function isEnabled() {\n      return this.getLength() > 0;\n    }\n  }, {\n    key: 'onCreate',\n    value: function onCreate(event) {\n      _get(Swatches.prototype.__proto__ || Object.getPrototypeOf(Swatches.prototype), 'onCreate', this).call(this, event);\n\n      if (!this.isEnabled()) {\n        return;\n      }\n\n      this.element = (0, _jquery2.default)(this.options.barTemplate);\n      this.load();\n      this.colorpicker.picker.append(this.element);\n    }\n  }, {\n    key: 'load',\n    value: function load() {\n      var _this2 = this;\n\n      var colorpicker = this.colorpicker,\n          swatchContainer = this.element.find('.colorpicker-swatches--inner'),\n          isAliased = this.options.namesAsValues === true && !Array.isArray(this.colors);\n\n      swatchContainer.empty();\n\n      _jquery2.default.each(this.colors, function (name, value) {\n        var $swatch = (0, _jquery2.default)(_this2.options.swatchTemplate).attr('data-name', name).attr('data-value', value).attr('title', isAliased ? name + ': ' + value : value).on('mousedown.colorpicker touchstart.colorpicker', function (e) {\n          var $sw = (0, _jquery2.default)(this);\n\n          // e.preventDefault();\n\n          colorpicker.setValue(isAliased ? $sw.attr('data-name') : $sw.attr('data-value'));\n        });\n\n        $swatch.find('.colorpicker-swatch--inner').css('background-color', value);\n\n        swatchContainer.append($swatch);\n      });\n\n      swatchContainer.append((0, _jquery2.default)('<i class=\"colorpicker-clear\"></i>'));\n    }\n  }]);\n\n  return Swatches;\n}(_Palette3.default);\n\nexports.default = Swatches;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Class that handles all configured sliders on mouse or touch events.\n * @ignore\n */\nvar SliderHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  function SliderHandler(colorpicker) {\n    _classCallCheck(this, SliderHandler);\n\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {*|String}\n     * @private\n     */\n    this.currentSlider = null;\n    /**\n     * @type {{left: number, top: number}}\n     * @private\n     */\n    this.mousePointer = {\n      left: 0,\n      top: 0\n    };\n\n    /**\n     * @type {Function}\n     */\n    this.onMove = _jquery2.default.proxy(this.defaultOnMove, this);\n  }\n\n  /**\n   * This function is called every time a slider guide is moved\n   * The scope of \"this\" is the SliderHandler object.\n   *\n   * @param {int} top\n   * @param {int} left\n   */\n\n\n  _createClass(SliderHandler, [{\n    key: 'defaultOnMove',\n    value: function defaultOnMove(top, left) {\n      if (!this.currentSlider) {\n        return;\n      }\n\n      var slider = this.currentSlider,\n          cp = this.colorpicker,\n          ch = cp.colorHandler;\n\n      // Create a color object\n      var color = !ch.hasColor() ? ch.getFallbackColor() : ch.color.getClone();\n\n      // Adjust the guide position\n      slider.guideStyle.left = left + 'px';\n      slider.guideStyle.top = top + 'px';\n\n      // Adjust the color\n      if (slider.callLeft) {\n        color[slider.callLeft](left / slider.maxLeft);\n      }\n      if (slider.callTop) {\n        color[slider.callTop](top / slider.maxTop);\n      }\n\n      // Set the new color\n      cp.setValue(color);\n      cp.popupHandler.focus();\n    }\n\n    /**\n     * Binds the colorpicker sliders to the mouse/touch events\n     */\n\n  }, {\n    key: 'bind',\n    value: function bind() {\n      var sliders = this.colorpicker.options.horizontal ? this.colorpicker.options.slidersHorz : this.colorpicker.options.sliders;\n      var sliderClasses = [];\n\n      for (var sliderName in sliders) {\n        if (!sliders.hasOwnProperty(sliderName)) {\n          continue;\n        }\n\n        sliderClasses.push(sliders[sliderName].selector);\n      }\n\n      this.colorpicker.picker.find(sliderClasses.join(', ')).on('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.pressed, this));\n    }\n\n    /**\n     * Unbinds any event bound by this handler\n     */\n\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      (0, _jquery2.default)(this.colorpicker.picker).off({\n        'mousemove.colorpicker': _jquery2.default.proxy(this.moved, this),\n        'touchmove.colorpicker': _jquery2.default.proxy(this.moved, this),\n        'mouseup.colorpicker': _jquery2.default.proxy(this.released, this),\n        'touchend.colorpicker': _jquery2.default.proxy(this.released, this)\n      });\n    }\n\n    /**\n     * Function triggered when clicking in one of the color adjustment bars\n     *\n     * @private\n     * @fires Colorpicker#mousemove\n     * @param {Event} e\n     */\n\n  }, {\n    key: 'pressed',\n    value: function pressed(e) {\n      if (this.colorpicker.isDisabled()) {\n        return;\n      }\n      this.colorpicker.lastEvent.alias = 'pressed';\n      this.colorpicker.lastEvent.e = e;\n\n      if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n        e.pageX = e.originalEvent.touches[0].pageX;\n        e.pageY = e.originalEvent.touches[0].pageY;\n      }\n      // e.stopPropagation();\n      // e.preventDefault();\n\n      var target = (0, _jquery2.default)(e.target);\n\n      // detect the slider and set the limits and callbacks\n      var zone = target.closest('div');\n      var sliders = this.colorpicker.options.horizontal ? this.colorpicker.options.slidersHorz : this.colorpicker.options.sliders;\n\n      if (zone.is('.colorpicker')) {\n        return;\n      }\n\n      this.currentSlider = null;\n\n      for (var sliderName in sliders) {\n        if (!sliders.hasOwnProperty(sliderName)) {\n          continue;\n        }\n\n        var slider = sliders[sliderName];\n\n        if (zone.is(slider.selector)) {\n          this.currentSlider = _jquery2.default.extend({}, slider, { name: sliderName });\n          break;\n        } else if (slider.childSelector !== undefined && zone.is(slider.childSelector)) {\n          this.currentSlider = _jquery2.default.extend({}, slider, { name: sliderName });\n          zone = zone.parent(); // zone.parents(slider.selector).first() ?\n          break;\n        }\n      }\n\n      var guide = zone.find('.colorpicker-guide').get(0);\n\n      if (this.currentSlider === null || guide === null) {\n        return;\n      }\n\n      var offset = zone.offset();\n\n      // reference to guide's style\n      this.currentSlider.guideStyle = guide.style;\n      this.currentSlider.left = e.pageX - offset.left;\n      this.currentSlider.top = e.pageY - offset.top;\n      this.mousePointer = {\n        left: e.pageX,\n        top: e.pageY\n      };\n\n      // TODO: fix moving outside the picker makes the guides to keep moving. The event needs to be bound to the window.\n      /**\n       * (window.document) Triggered on mousedown for the document object,\n       * so the color adjustment guide is moved to the clicked position.\n       *\n       * @event Colorpicker#mousemove\n       */\n      (0, _jquery2.default)(this.colorpicker.picker).on({\n        'mousemove.colorpicker': _jquery2.default.proxy(this.moved, this),\n        'touchmove.colorpicker': _jquery2.default.proxy(this.moved, this),\n        'mouseup.colorpicker': _jquery2.default.proxy(this.released, this),\n        'touchend.colorpicker': _jquery2.default.proxy(this.released, this)\n      }).trigger('mousemove');\n    }\n\n    /**\n     * Function triggered when dragging a guide inside one of the color adjustment bars.\n     *\n     * @private\n     * @param {Event} e\n     */\n\n  }, {\n    key: 'moved',\n    value: function moved(e) {\n      this.colorpicker.lastEvent.alias = 'moved';\n      this.colorpicker.lastEvent.e = e;\n\n      if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n        e.pageX = e.originalEvent.touches[0].pageX;\n        e.pageY = e.originalEvent.touches[0].pageY;\n      }\n\n      // e.stopPropagation();\n      e.preventDefault(); // prevents scrolling on mobile\n\n      var left = Math.max(0, Math.min(this.currentSlider.maxLeft, this.currentSlider.left + ((e.pageX || this.mousePointer.left) - this.mousePointer.left)));\n\n      var top = Math.max(0, Math.min(this.currentSlider.maxTop, this.currentSlider.top + ((e.pageY || this.mousePointer.top) - this.mousePointer.top)));\n\n      this.onMove(top, left);\n    }\n\n    /**\n     * Function triggered when releasing the click in one of the color adjustment bars.\n     *\n     * @private\n     * @param {Event} e\n     */\n\n  }, {\n    key: 'released',\n    value: function released(e) {\n      this.colorpicker.lastEvent.alias = 'released';\n      this.colorpicker.lastEvent.e = e;\n\n      // e.stopPropagation();\n      // e.preventDefault();\n\n      (0, _jquery2.default)(this.colorpicker.picker).off({\n        'mousemove.colorpicker': this.moved,\n        'touchmove.colorpicker': this.moved,\n        'mouseup.colorpicker': this.released,\n        'touchend.colorpicker': this.released\n      });\n    }\n  }]);\n\n  return SliderHandler;\n}();\n\nexports.default = SliderHandler;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nvar _options = __webpack_require__(3);\n\nvar _options2 = _interopRequireDefault(_options);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Handles everything related to the UI of the colorpicker popup: show, hide, position,...\n * @ignore\n */\nvar PopupHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Window} root\n   */\n  function PopupHandler(colorpicker, root) {\n    _classCallCheck(this, PopupHandler);\n\n    /**\n     * @type {Window}\n     */\n    this.root = root;\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTarget = null;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTip = null;\n\n    /**\n     * If true, the latest click was inside the popover\n     * @type {boolean}\n     */\n    this.clicking = false;\n    /**\n     * @type {boolean}\n     */\n    this.hidding = false;\n    /**\n     * @type {boolean}\n     */\n    this.showing = false;\n  }\n\n  /**\n   * @private\n   * @returns {jQuery|false}\n   */\n\n\n  _createClass(PopupHandler, [{\n    key: 'bind',\n\n\n    /**\n     * Binds the different colorpicker elements to the focus/mouse/touch events so it reacts in order to show or\n     * hide the colorpicker popup accordingly. It also adds the proper classes.\n     */\n    value: function bind() {\n      var cp = this.colorpicker;\n\n      if (cp.options.inline) {\n        cp.picker.addClass('colorpicker-inline colorpicker-visible');\n        return; // no need to bind show/hide events for inline elements\n      }\n\n      cp.picker.addClass('colorpicker-popup colorpicker-hidden');\n\n      // there is no input or addon\n      if (!this.hasInput && !this.hasAddon) {\n        return;\n      }\n\n      // create Bootstrap 4 popover\n      if (cp.options.popover) {\n        this.createPopover();\n      }\n\n      // bind addon show/hide events\n      if (this.hasAddon) {\n        // enable focus on addons\n        if (!this.addon.attr('tabindex')) {\n          this.addon.attr('tabindex', 0);\n        }\n\n        this.addon.on({\n          'mousedown.colorpicker touchstart.colorpicker': _jquery2.default.proxy(this.toggle, this)\n        });\n\n        this.addon.on({\n          'focus.colorpicker': _jquery2.default.proxy(this.show, this)\n        });\n\n        this.addon.on({\n          'focusout.colorpicker': _jquery2.default.proxy(this.hide, this)\n        });\n      }\n\n      // bind input show/hide events\n      if (this.hasInput && !this.hasAddon) {\n        this.input.on({\n          'mousedown.colorpicker touchstart.colorpicker': _jquery2.default.proxy(this.show, this),\n          'focus.colorpicker': _jquery2.default.proxy(this.show, this)\n        });\n\n        this.input.on({\n          'focusout.colorpicker': _jquery2.default.proxy(this.hide, this)\n        });\n      }\n\n      // reposition popup on window resize\n      (0, _jquery2.default)(this.root).on('resize.colorpicker', _jquery2.default.proxy(this.reposition, this));\n    }\n\n    /**\n     * Unbinds any event bound by this handler\n     */\n\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      if (this.hasInput) {\n        this.input.off({\n          'mousedown.colorpicker touchstart.colorpicker': _jquery2.default.proxy(this.show, this),\n          'focus.colorpicker': _jquery2.default.proxy(this.show, this)\n        });\n        this.input.off({\n          'focusout.colorpicker': _jquery2.default.proxy(this.hide, this)\n        });\n      }\n\n      if (this.hasAddon) {\n        this.addon.off({\n          'mousedown.colorpicker touchstart.colorpicker': _jquery2.default.proxy(this.toggle, this)\n        });\n        this.addon.off({\n          'focus.colorpicker': _jquery2.default.proxy(this.show, this)\n        });\n        this.addon.off({\n          'focusout.colorpicker': _jquery2.default.proxy(this.hide, this)\n        });\n      }\n\n      if (this.popoverTarget) {\n        this.popoverTarget.popover('dispose');\n      }\n\n      (0, _jquery2.default)(this.root).off('resize.colorpicker', _jquery2.default.proxy(this.reposition, this));\n      (0, _jquery2.default)(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.hide, this));\n      (0, _jquery2.default)(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.onClickingInside, this));\n    }\n  }, {\n    key: 'isClickingInside',\n    value: function isClickingInside(e) {\n      if (!e) {\n        return false;\n      }\n\n      return this.isOrIsInside(this.popoverTip, e.currentTarget) || this.isOrIsInside(this.popoverTip, e.target) || this.isOrIsInside(this.colorpicker.picker, e.currentTarget) || this.isOrIsInside(this.colorpicker.picker, e.target);\n    }\n  }, {\n    key: 'isOrIsInside',\n    value: function isOrIsInside(container, element) {\n      if (!container || !element) {\n        return false;\n      }\n\n      element = (0, _jquery2.default)(element);\n\n      return element.is(container) || container.find(element).length > 0;\n    }\n  }, {\n    key: 'onClickingInside',\n    value: function onClickingInside(e) {\n      this.clicking = this.isClickingInside(e);\n    }\n  }, {\n    key: 'createPopover',\n    value: function createPopover() {\n      var cp = this.colorpicker;\n\n      this.popoverTarget = this.hasAddon ? this.addon : this.input;\n\n      cp.picker.addClass('colorpicker-bs-popover-content');\n\n      this.popoverTarget.popover(_jquery2.default.extend(true, {}, _options2.default.popover, cp.options.popover, { trigger: 'manual', content: cp.picker, html: true }));\n\n      this.popoverTip = (0, _jquery2.default)(this.popoverTarget.popover('getTipElement').data('bs.popover').tip);\n      this.popoverTip.addClass('colorpicker-bs-popover');\n\n      this.popoverTarget.on('shown.bs.popover', _jquery2.default.proxy(this.fireShow, this));\n      this.popoverTarget.on('hidden.bs.popover', _jquery2.default.proxy(this.fireHide, this));\n    }\n\n    /**\n     * If the widget is not inside a container or inline, rearranges its position relative to its element offset.\n     *\n     * @param {Event} [e]\n     * @private\n     */\n\n  }, {\n    key: 'reposition',\n    value: function reposition(e) {\n      if (this.popoverTarget && this.isVisible()) {\n        this.popoverTarget.popover('update');\n      }\n    }\n\n    /**\n     * Toggles the colorpicker between visible or hidden\n     *\n     * @fires Colorpicker#colorpickerShow\n     * @fires Colorpicker#colorpickerHide\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'toggle',\n    value: function toggle(e) {\n      if (this.isVisible()) {\n        this.hide(e);\n      } else {\n        this.show(e);\n      }\n    }\n\n    /**\n     * Shows the colorpicker widget if hidden.\n     *\n     * @fires Colorpicker#colorpickerShow\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'show',\n    value: function show(e) {\n      if (this.isVisible() || this.showing || this.hidding) {\n        return;\n      }\n\n      this.showing = true;\n      this.hidding = false;\n      this.clicking = false;\n\n      var cp = this.colorpicker;\n\n      cp.lastEvent.alias = 'show';\n      cp.lastEvent.e = e;\n\n      // Prevent showing browser native HTML5 colorpicker\n      if (e && (!this.hasInput || this.input.attr('type') === 'color') && e && e.preventDefault) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n\n      // If it's a popover, add event to the document to hide the picker when clicking outside of it\n      if (this.isPopover) {\n        (0, _jquery2.default)(this.root).on('resize.colorpicker', _jquery2.default.proxy(this.reposition, this));\n      }\n\n      // add visible class before popover is shown\n      cp.picker.addClass('colorpicker-visible').removeClass('colorpicker-hidden');\n\n      if (this.popoverTarget) {\n        this.popoverTarget.popover('show');\n      } else {\n        this.fireShow();\n      }\n    }\n  }, {\n    key: 'fireShow',\n    value: function fireShow() {\n      this.hidding = false;\n      this.showing = false;\n\n      if (this.isPopover) {\n        // Add event to hide on outside click\n        (0, _jquery2.default)(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.hide, this));\n        (0, _jquery2.default)(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.onClickingInside, this));\n      }\n\n      /**\n       * (Colorpicker) When show() is called and the widget can be shown.\n       *\n       * @event Colorpicker#colorpickerShow\n       */\n      this.colorpicker.trigger('colorpickerShow');\n    }\n\n    /**\n     * Hides the colorpicker widget.\n     * Hide is prevented when it is triggered by an event whose target element has been clicked/touched.\n     *\n     * @fires Colorpicker#colorpickerHide\n     * @param {Event} [e]\n     */\n\n  }, {\n    key: 'hide',\n    value: function hide(e) {\n      if (this.isHidden() || this.showing || this.hidding) {\n        return;\n      }\n\n      var cp = this.colorpicker,\n          clicking = this.clicking || this.isClickingInside(e);\n\n      this.hidding = true;\n      this.showing = false;\n      this.clicking = false;\n\n      cp.lastEvent.alias = 'hide';\n      cp.lastEvent.e = e;\n\n      // TODO: fix having to click twice outside when losing focus and last 2 clicks where inside the colorpicker\n\n      // Prevent hide if triggered by an event and an element inside the colorpicker has been clicked/touched\n      if (clicking) {\n        this.hidding = false;\n        return;\n      }\n\n      if (this.popoverTarget) {\n        this.popoverTarget.popover('hide');\n      } else {\n        this.fireHide();\n      }\n    }\n  }, {\n    key: 'fireHide',\n    value: function fireHide() {\n      this.hidding = false;\n      this.showing = false;\n\n      var cp = this.colorpicker;\n\n      // add hidden class after popover is hidden\n      cp.picker.addClass('colorpicker-hidden').removeClass('colorpicker-visible');\n\n      // Unbind window and document events, since there is no need to keep them while the popup is hidden\n      (0, _jquery2.default)(this.root).off('resize.colorpicker', _jquery2.default.proxy(this.reposition, this));\n      (0, _jquery2.default)(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.hide, this));\n      (0, _jquery2.default)(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', _jquery2.default.proxy(this.onClickingInside, this));\n\n      /**\n       * (Colorpicker) When hide() is called and the widget can be hidden.\n       *\n       * @event Colorpicker#colorpickerHide\n       */\n      cp.trigger('colorpickerHide');\n    }\n  }, {\n    key: 'focus',\n    value: function focus() {\n      if (this.hasAddon) {\n        return this.addon.focus();\n      }\n      if (this.hasInput) {\n        return this.input.focus();\n      }\n      return false;\n    }\n\n    /**\n     * Returns true if the colorpicker element has the colorpicker-visible class and not the colorpicker-hidden one.\n     * False otherwise.\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isVisible',\n    value: function isVisible() {\n      return this.colorpicker.picker.hasClass('colorpicker-visible') && !this.colorpicker.picker.hasClass('colorpicker-hidden');\n    }\n\n    /**\n     * Returns true if the colorpicker element has the colorpicker-hidden class and not the colorpicker-visible one.\n     * False otherwise.\n     *\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isHidden',\n    value: function isHidden() {\n      return this.colorpicker.picker.hasClass('colorpicker-hidden') && !this.colorpicker.picker.hasClass('colorpicker-visible');\n    }\n  }, {\n    key: 'input',\n    get: function get() {\n      return this.colorpicker.inputHandler.input;\n    }\n\n    /**\n     * @private\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasInput',\n    get: function get() {\n      return this.colorpicker.inputHandler.hasInput();\n    }\n\n    /**\n     * @private\n     * @returns {jQuery|false}\n     */\n\n  }, {\n    key: 'addon',\n    get: function get() {\n      return this.colorpicker.addonHandler.addon;\n    }\n\n    /**\n     * @private\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasAddon',\n    get: function get() {\n      return this.colorpicker.addonHandler.hasAddon();\n    }\n\n    /**\n     * @private\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isPopover',\n    get: function get() {\n      return !this.colorpicker.options.inline && !!this.popoverTip;\n    }\n  }]);\n\n  return PopupHandler;\n}();\n\nexports.default = PopupHandler;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nvar _ColorItem = __webpack_require__(2);\n\nvar _ColorItem2 = _interopRequireDefault(_ColorItem);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Handles everything related to the colorpicker input\n * @ignore\n */\nvar InputHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  function InputHandler(colorpicker) {\n    _classCallCheck(this, InputHandler);\n\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery|false}\n     */\n    this.input = this.colorpicker.element.is('input') ? this.colorpicker.element : this.colorpicker.options.input ? this.colorpicker.element.find(this.colorpicker.options.input) : false;\n\n    if (this.input && this.input.length === 0) {\n      this.input = false;\n    }\n\n    this._initValue();\n  }\n\n  _createClass(InputHandler, [{\n    key: 'bind',\n    value: function bind() {\n      if (!this.hasInput()) {\n        return;\n      }\n      this.input.on({\n        'keyup.colorpicker': _jquery2.default.proxy(this.onkeyup, this)\n      });\n      this.input.on({\n        'change.colorpicker': _jquery2.default.proxy(this.onchange, this)\n      });\n    }\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      if (!this.hasInput()) {\n        return;\n      }\n      this.input.off('.colorpicker');\n    }\n  }, {\n    key: '_initValue',\n    value: function _initValue() {\n      if (!this.hasInput()) {\n        return;\n      }\n\n      var val = '';\n\n      [\n      // candidates:\n      this.input.val(), this.input.data('color'), this.input.attr('data-color')].map(function (item) {\n        if (item && val === '') {\n          val = item;\n        }\n      });\n\n      if (val instanceof _ColorItem2.default) {\n        val = this.getFormattedColor(val.string(this.colorpicker.format));\n      } else if (!(typeof val === 'string' || val instanceof String)) {\n        val = '';\n      }\n\n      this.input.prop('value', val);\n    }\n\n    /**\n     * Returns the color string from the input value.\n     * If there is no input the return value is false.\n     *\n     * @returns {String|boolean}\n     */\n\n  }, {\n    key: 'getValue',\n    value: function getValue() {\n      if (!this.hasInput()) {\n        return false;\n      }\n\n      return this.input.val();\n    }\n\n    /**\n     * If the input element is present, it updates the value with the current color object color string.\n     * If the value is changed, this method fires a \"change\" event on the input element.\n     *\n     * @param {String} val\n     *\n     * @fires Colorpicker#change\n     */\n\n  }, {\n    key: 'setValue',\n    value: function setValue(val) {\n      if (!this.hasInput()) {\n        return;\n      }\n\n      var inputVal = this.input.prop('value');\n\n      val = val ? val : '';\n\n      if (val === (inputVal ? inputVal : '')) {\n        // No need to set value or trigger any event if nothing changed\n        return;\n      }\n\n      this.input.prop('value', val);\n\n      /**\n       * (Input) Triggered on the input element when a new color is selected.\n       *\n       * @event Colorpicker#change\n       */\n      this.input.trigger({\n        type: 'change',\n        colorpicker: this.colorpicker,\n        color: this.colorpicker.color,\n        value: val\n      });\n    }\n\n    /**\n     * Returns the formatted color string, with the formatting options applied\n     * (e.g. useHashPrefix)\n     *\n     * @param {String|null} val\n     *\n     * @returns {String}\n     */\n\n  }, {\n    key: 'getFormattedColor',\n    value: function getFormattedColor() {\n      var val = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n\n      val = val ? val : this.colorpicker.colorHandler.getColorString();\n\n      if (!val) {\n        return '';\n      }\n\n      val = this.colorpicker.colorHandler.resolveColorDelegate(val, false);\n\n      if (this.colorpicker.options.useHashPrefix === false) {\n        val = val.replace(/^#/g, '');\n      }\n\n      return val;\n    }\n\n    /**\n     * Returns true if the widget has an associated input element, false otherwise\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasInput',\n    value: function hasInput() {\n      return this.input !== false;\n    }\n\n    /**\n     * Returns true if the input exists and is disabled\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isEnabled',\n    value: function isEnabled() {\n      return this.hasInput() && !this.isDisabled();\n    }\n\n    /**\n     * Returns true if the input exists and is disabled\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isDisabled',\n    value: function isDisabled() {\n      return this.hasInput() && this.input.prop('disabled') === true;\n    }\n\n    /**\n     * Disables the input if any\n     *\n     * @fires Colorpicker#colorpickerDisable\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'disable',\n    value: function disable() {\n      if (this.hasInput()) {\n        this.input.prop('disabled', true);\n      }\n    }\n\n    /**\n     * Enables the input if any\n     *\n     * @fires Colorpicker#colorpickerEnable\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'enable',\n    value: function enable() {\n      if (this.hasInput()) {\n        this.input.prop('disabled', false);\n      }\n    }\n\n    /**\n     * Calls setValue with the current internal color value\n     *\n     * @fires Colorpicker#change\n     */\n\n  }, {\n    key: 'update',\n    value: function update() {\n      if (!this.hasInput()) {\n        return;\n      }\n\n      if (this.colorpicker.options.autoInputFallback === false && this.colorpicker.colorHandler.isInvalidColor()) {\n        // prevent update if color is invalid, autoInputFallback is disabled and the last event is keyup.\n        return;\n      }\n\n      this.setValue(this.getFormattedColor());\n    }\n\n    /**\n     * Function triggered when the input has changed, so the colorpicker gets updated.\n     *\n     * @private\n     * @param {Event} e\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'onchange',\n    value: function onchange(e) {\n      this.colorpicker.lastEvent.alias = 'input.change';\n      this.colorpicker.lastEvent.e = e;\n\n      var val = this.getValue();\n\n      if (val !== e.value) {\n        this.colorpicker.setValue(val);\n      }\n    }\n\n    /**\n     * Function triggered after a keyboard key has been released.\n     *\n     * @private\n     * @param {Event} e\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'onkeyup',\n    value: function onkeyup(e) {\n      this.colorpicker.lastEvent.alias = 'input.keyup';\n      this.colorpicker.lastEvent.e = e;\n\n      var val = this.getValue();\n\n      if (val !== e.value) {\n        this.colorpicker.setValue(val);\n      }\n    }\n  }]);\n\n  return InputHandler;\n}();\n\nexports.default = InputHandler;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar colorString = __webpack_require__(17);\nvar convert = __webpack_require__(20);\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (typeof obj === 'undefined') {\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* MIT license */\nvar colorNames = __webpack_require__(5);\nvar swizzle = __webpack_require__(18);\n\nvar reverseNames = {};\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (colorNames.hasOwnProperty(name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar keyword = /(\\D+)/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha + hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\n\t\tif (!rgb) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d*\\.)?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = (parseFloat(match[1]) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d*[\\.]?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = num.toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar isArrayish = __webpack_require__(19);\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nmodule.exports = function isArrayish(obj) {\n\tif (!obj) {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && obj.splice instanceof Function);\n};\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar conversions = __webpack_require__(6);\nvar route = __webpack_require__(21);\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar conversions = __webpack_require__(6);\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nvar _ColorItem = __webpack_require__(2);\n\nvar _ColorItem2 = _interopRequireDefault(_ColorItem);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Handles everything related to the colorpicker color\n * @ignore\n */\nvar ColorHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  function ColorHandler(colorpicker) {\n    _classCallCheck(this, ColorHandler);\n\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n  }\n\n  /**\n   * @returns {*|String|ColorItem}\n   */\n\n\n  _createClass(ColorHandler, [{\n    key: 'bind',\n    value: function bind() {\n      // if the color option is set\n      if (this.colorpicker.options.color) {\n        this.color = this.createColor(this.colorpicker.options.color);\n        return;\n      }\n\n      // if element[color] is empty and the input has a value\n      if (!this.color && !!this.colorpicker.inputHandler.getValue()) {\n        this.color = this.createColor(this.colorpicker.inputHandler.getValue(), this.colorpicker.options.autoInputFallback);\n      }\n    }\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      this.colorpicker.element.removeData('color');\n    }\n\n    /**\n     * Returns the color string from the input value or the 'data-color' attribute of the input or element.\n     * If empty, it returns the defaultValue parameter.\n     *\n     * @returns {String|*}\n     */\n\n  }, {\n    key: 'getColorString',\n    value: function getColorString() {\n      if (!this.hasColor()) {\n        return '';\n      }\n\n      return this.color.string(this.format);\n    }\n\n    /**\n     * Sets the color value\n     *\n     * @param {String|ColorItem} val\n     */\n\n  }, {\n    key: 'setColorString',\n    value: function setColorString(val) {\n      var color = val ? this.createColor(val) : null;\n\n      this.color = color ? color : null;\n    }\n\n    /**\n     * Creates a new color using the widget instance options (fallbackColor, format).\n     *\n     * @fires Colorpicker#colorpickerInvalid\n     * @param {*} val\n     * @param {boolean} fallbackOnInvalid\n     * @returns {ColorItem}\n     */\n\n  }, {\n    key: 'createColor',\n    value: function createColor(val) {\n      var fallbackOnInvalid = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n      var color = new _ColorItem2.default(this.resolveColorDelegate(val), this.format);\n\n      if (!color.isValid()) {\n        if (fallbackOnInvalid) {\n          color = this.getFallbackColor();\n        }\n\n        /**\n         * (Colorpicker) Fired when the color is invalid and the fallback color is going to be used.\n         *\n         * @event Colorpicker#colorpickerInvalid\n         */\n        this.colorpicker.trigger('colorpickerInvalid', color, val);\n      }\n\n      if (!this.isAlphaEnabled()) {\n        // Alpha is disabled\n        color.alpha = 1;\n      }\n\n      return color;\n    }\n  }, {\n    key: 'getFallbackColor',\n    value: function getFallbackColor() {\n      if (this.fallback && this.fallback === this.color) {\n        return this.color;\n      }\n\n      var fallback = this.resolveColorDelegate(this.fallback);\n      var color = new _ColorItem2.default(fallback, this.format);\n\n      if (!color.isValid()) {\n        console.warn('The fallback color is invalid. Falling back to the previous color or black if any.');\n        return this.color ? this.color : new _ColorItem2.default('#000000', this.format);\n      }\n\n      return color;\n    }\n\n    /**\n     * @returns {ColorItem}\n     */\n\n  }, {\n    key: 'assureColor',\n    value: function assureColor() {\n      if (!this.hasColor()) {\n        this.color = this.getFallbackColor();\n      }\n\n      return this.color;\n    }\n\n    /**\n     * Delegates the color resolution to the colorpicker extensions.\n     *\n     * @param {String|*} color\n     * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n     * @returns {ColorItem|String|*|null}\n     */\n\n  }, {\n    key: 'resolveColorDelegate',\n    value: function resolveColorDelegate(color) {\n      var realColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n      var extResolvedColor = false;\n\n      _jquery2.default.each(this.colorpicker.extensions, function (name, ext) {\n        if (extResolvedColor !== false) {\n          // skip if resolved\n          return;\n        }\n        extResolvedColor = ext.resolveColor(color, realColor);\n      });\n\n      return extResolvedColor ? extResolvedColor : color;\n    }\n\n    /**\n     * Checks if there is a color object, that it is valid and it is not a fallback\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isInvalidColor',\n    value: function isInvalidColor() {\n      return !this.hasColor() || !this.color.isValid();\n    }\n\n    /**\n     * Returns true if the useAlpha option is exactly true, false otherwise\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'isAlphaEnabled',\n    value: function isAlphaEnabled() {\n      return this.colorpicker.options.useAlpha !== false;\n    }\n\n    /**\n     * Returns true if the current color object is an instance of Color, false otherwise.\n     * @returns {boolean}\n     */\n\n  }, {\n    key: 'hasColor',\n    value: function hasColor() {\n      return this.color instanceof _ColorItem2.default;\n    }\n  }, {\n    key: 'fallback',\n    get: function get() {\n      return this.colorpicker.options.fallbackColor ? this.colorpicker.options.fallbackColor : this.hasColor() ? this.color : null;\n    }\n\n    /**\n     * @returns {String|null}\n     */\n\n  }, {\n    key: 'format',\n    get: function get() {\n      if (this.colorpicker.options.format) {\n        return this.colorpicker.options.format;\n      }\n\n      if (this.hasColor() && this.color.hasTransparency() && this.color.format.match(/^hex/)) {\n        return this.isAlphaEnabled() ? 'rgba' : 'hex';\n      }\n\n      if (this.hasColor()) {\n        return this.color.format;\n      }\n\n      return 'rgb';\n    }\n\n    /**\n     * Internal color getter\n     *\n     * @type {ColorItem|null}\n     */\n\n  }, {\n    key: 'color',\n    get: function get() {\n      return this.colorpicker.element.data('color');\n    }\n\n    /**\n     * Internal color setter\n     *\n     * @ignore\n     * @param {ColorItem|null} value\n     */\n    ,\n    set: function set(value) {\n      this.colorpicker.element.data('color', value);\n\n      if (value instanceof _ColorItem2.default && this.colorpicker.options.format === 'auto') {\n        // If format is 'auto', use the first parsed one from now on\n        this.colorpicker.options.format = this.color.format;\n      }\n    }\n  }]);\n\n  return ColorHandler;\n}();\n\nexports.default = ColorHandler;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _jquery = __webpack_require__(0);\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Handles everything related to the colorpicker UI\n * @ignore\n */\nvar PickerHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  function PickerHandler(colorpicker) {\n    _classCallCheck(this, PickerHandler);\n\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.picker = null;\n  }\n\n  _createClass(PickerHandler, [{\n    key: 'bind',\n    value: function bind() {\n      /**\n       * @type {jQuery|HTMLElement}\n       */\n      var picker = this.picker = (0, _jquery2.default)(this.options.template);\n\n      if (this.options.customClass) {\n        picker.addClass(this.options.customClass);\n      }\n\n      if (this.options.horizontal) {\n        picker.addClass('colorpicker-horizontal');\n      }\n\n      if (this._supportsAlphaBar()) {\n        this.options.useAlpha = true;\n        picker.addClass('colorpicker-with-alpha');\n      } else {\n        this.options.useAlpha = false;\n      }\n    }\n  }, {\n    key: 'attach',\n    value: function attach() {\n      // Inject the colorpicker element into the DOM\n      var pickerParent = this.colorpicker.container ? this.colorpicker.container : null;\n\n      if (pickerParent) {\n        this.picker.appendTo(pickerParent);\n      }\n    }\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      this.picker.remove();\n    }\n  }, {\n    key: '_supportsAlphaBar',\n    value: function _supportsAlphaBar() {\n      return (this.options.useAlpha || this.colorpicker.colorHandler.hasColor() && this.color.hasTransparency()) && this.options.useAlpha !== false && (!this.options.format || this.options.format && !this.options.format.match(/^hex([36])?$/i));\n    }\n\n    /**\n     * Changes the color adjustment bars using the current color object information.\n     */\n\n  }, {\n    key: 'update',\n    value: function update() {\n      if (!this.colorpicker.colorHandler.hasColor()) {\n        return;\n      }\n\n      var vertical = this.options.horizontal !== true,\n          slider = vertical ? this.options.sliders : this.options.slidersHorz;\n\n      var saturationGuide = this.picker.find('.colorpicker-saturation .colorpicker-guide'),\n          hueGuide = this.picker.find('.colorpicker-hue .colorpicker-guide'),\n          alphaGuide = this.picker.find('.colorpicker-alpha .colorpicker-guide');\n\n      var hsva = this.color.toHsvaRatio();\n\n      // Set guides position\n      if (hueGuide.length) {\n        hueGuide.css(vertical ? 'top' : 'left', (vertical ? slider.hue.maxTop : slider.hue.maxLeft) * (1 - hsva.h));\n      }\n      if (alphaGuide.length) {\n        alphaGuide.css(vertical ? 'top' : 'left', (vertical ? slider.alpha.maxTop : slider.alpha.maxLeft) * (1 - hsva.a));\n      }\n      if (saturationGuide.length) {\n        saturationGuide.css({\n          'top': slider.saturation.maxTop - hsva.v * slider.saturation.maxTop,\n          'left': hsva.s * slider.saturation.maxLeft\n        });\n      }\n\n      // Set saturation hue background\n      this.picker.find('.colorpicker-saturation').css('backgroundColor', this.color.getCloneHueOnly().toHexString()); // we only need hue\n\n      // Set alpha color gradient\n      var hexColor = this.color.toHexString();\n      var alphaBg = '';\n\n      if (this.options.horizontal) {\n        alphaBg = 'linear-gradient(to right, ' + hexColor + ' 0%, transparent 100%)';\n      } else {\n        alphaBg = 'linear-gradient(to bottom, ' + hexColor + ' 0%, transparent 100%)';\n      }\n\n      this.picker.find('.colorpicker-alpha-color').css('background', alphaBg);\n    }\n  }, {\n    key: 'options',\n    get: function get() {\n      return this.colorpicker.options;\n    }\n  }, {\n    key: 'color',\n    get: function get() {\n      return this.colorpicker.colorHandler.color;\n    }\n  }]);\n\n  return PickerHandler;\n}();\n\nexports.default = PickerHandler;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * Handles everything related to the colorpicker addon\n * @ignore\n */\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar AddonHandler = function () {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  function AddonHandler(colorpicker) {\n    _classCallCheck(this, AddonHandler);\n\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.addon = null;\n  }\n\n  _createClass(AddonHandler, [{\n    key: 'hasAddon',\n    value: function hasAddon() {\n      return !!this.addon;\n    }\n  }, {\n    key: 'bind',\n    value: function bind() {\n      /**\n       * @type {*|jQuery}\n       */\n      this.addon = this.colorpicker.options.addon ? this.colorpicker.element.find(this.colorpicker.options.addon) : null;\n\n      if (this.addon && this.addon.length === 0) {\n        // not found\n        this.addon = null;\n      }\n    }\n  }, {\n    key: 'unbind',\n    value: function unbind() {\n      if (this.hasAddon()) {\n        this.addon.off('.colorpicker');\n      }\n    }\n\n    /**\n     * If the addon element is present, its background color is updated\n     */\n\n  }, {\n    key: 'update',\n    value: function update() {\n      if (!this.colorpicker.colorHandler.hasColor() || !this.hasAddon()) {\n        return;\n      }\n\n      var colorStr = this.colorpicker.colorHandler.getColorString();\n      var styles = { 'background': colorStr };\n\n      var icn = this.addon.find('i').eq(0);\n\n      if (icn.length > 0) {\n        icn.css(styles);\n      } else {\n        this.addon.css(styles);\n      }\n    }\n  }]);\n\n  return AddonHandler;\n}();\n\nexports.default = AddonHandler;\n\n/***/ })\n/******/ ]);\n});\n\n\n// WEBPACK FOOTER //\n// bootstrap-colorpicker.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 7);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap e5fc9649974c93b0b79b", "module.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external {\"root\":\"jQuery\",\"commonjs2\":\"jquery\",\"commonjs\":\"jquery\",\"amd\":\"jquery\"}\n// module id = 0\n// module chunks = 0 1", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Colorpicker extension class.\n */\nclass Extension {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Object} options\n   */\n  constructor(colorpicker, options = {}) {\n    /**\n     * The colorpicker instance\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * Extension options\n     *\n     * @type {Object}\n     */\n    this.options = options;\n\n    if (!(this.colorpicker.element && this.colorpicker.element.length)) {\n      throw new Error('Extension: this.colorpicker.element is not valid');\n    }\n\n    this.colorpicker.element.on('colorpickerCreate.colorpicker-ext', $.proxy(this.onCreate, this));\n    this.colorpicker.element.on('colorpickerDestroy.colorpicker-ext', $.proxy(this.onDestroy, this));\n    this.colorpicker.element.on('colorpickerUpdate.colorpicker-ext', $.proxy(this.onUpdate, this));\n    this.colorpicker.element.on('colorpickerChange.colorpicker-ext', $.proxy(this.onChange, this));\n    this.colorpicker.element.on('colorpickerInvalid.colorpicker-ext', $.proxy(this.onInvalid, this));\n    this.colorpicker.element.on('colorpickerShow.colorpicker-ext', $.proxy(this.onShow, this));\n    this.colorpicker.element.on('colorpickerHide.colorpicker-ext', $.proxy(this.onHide, this));\n    this.colorpicker.element.on('colorpickerEnable.colorpicker-ext', $.proxy(this.onEnable, this));\n    this.colorpicker.element.on('colorpickerDisable.colorpicker-ext', $.proxy(this.onDisable, this));\n  }\n\n  /**\n   * Function called every time a new color needs to be created.\n   * Return false to skip this resolver and continue with other extensions' ones\n   * or return anything else to consider the color resolved.\n   *\n   * @param {ColorItem|String|*} color\n   * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n   * @return {ColorItem|String|*}\n   */\n  resolveColor(color, realColor = true) {\n    return false;\n  }\n\n  /**\n   * Method called after the colorpicker is created\n   *\n   * @listens Colorpicker#colorpickerCreate\n   * @param {Event} event\n   */\n  onCreate(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is destroyed\n   *\n   * @listens Colorpicker#colorpickerDestroy\n   * @param {Event} event\n   */\n  onDestroy(event) {\n    this.colorpicker.element.off('.colorpicker-ext');\n  }\n\n  /**\n   * Method called after the colorpicker is updated\n   *\n   * @listens Colorpicker#colorpickerUpdate\n   * @param {Event} event\n   */\n  onUpdate(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker color is changed\n   *\n   * @listens Colorpicker#colorpickerChange\n   * @param {Event} event\n   */\n  onChange(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called when the colorpicker color is invalid\n   *\n   * @listens Colorpicker#colorpickerInvalid\n   * @param {Event} event\n   */\n  onInvalid(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is hidden\n   *\n   * @listens Colorpicker#colorpickerHide\n   * @param {Event} event\n   */\n  onHide(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is shown\n   *\n   * @listens Colorpicker#colorpickerShow\n   * @param {Event} event\n   */\n  onShow(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is disabled\n   *\n   * @listens Colorpicker#colorpickerDisable\n   * @param {Event} event\n   */\n  onDisable(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is enabled\n   *\n   * @listens Colorpicker#colorpickerEnable\n   * @param {Event} event\n   */\n  onEnable(event) {\n    // to be extended\n  }\n}\n\nexport default Extension;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/Extension.js", "/**\n * Color manipulation class, specific for Bootstrap Colorpicker\n */\nimport QixColor from 'color';\n\n/**\n * HSVA color data class, containing the hue, saturation, value and alpha\n * information.\n */\nclass HSVAColor {\n  /**\n   * @param {number|int} h\n   * @param {number|int} s\n   * @param {number|int} v\n   * @param {number|int} a\n   */\n  constructor(h, s, v, a) {\n    this.h = isNaN(h) ? 0 : h;\n    this.s = isNaN(s) ? 0 : s;\n    this.v = isNaN(v) ? 0 : v;\n    this.a = isNaN(h) ? 1 : a;\n  }\n\n  toString() {\n    return `${this.h}, ${this.s}%, ${this.v}%, ${this.a}`;\n  }\n}\n\n/**\n * HSVA color manipulation\n */\nclass ColorItem {\n\n  /**\n   * Returns the HSVAColor class\n   *\n   * @static\n   * @example let colorData = new ColorItem.HSVAColor(360, 100, 100, 1);\n   * @returns {HSVAColor}\n   */\n  static get HSVAColor() {\n    return HSVAColor;\n  }\n\n  /**\n   * Applies a method of the QixColor API and returns a new Color object or\n   * the return value of the method call.\n   *\n   * If no argument is provided, the internal QixColor object is returned.\n   *\n   * @param {String} fn QixColor function name\n   * @param args QixColor function arguments\n   * @example let darkerColor = color.api('darken', 0.25);\n   * @example let luminosity = color.api('luminosity');\n   * @example color = color.api('negate');\n   * @example let qColor = color.api().negate();\n   * @returns {ColorItem|QixColor|*}\n   */\n  api(fn, ...args) {\n    if (arguments.length === 0) {\n      return this._color;\n    }\n\n    let result = this._color[fn].apply(this._color, args);\n\n    if (!(result instanceof QixColor)) {\n      // return result of the method call\n      return result;\n    }\n\n    return new ColorItem(result, this.format);\n  }\n\n  /**\n   * Returns the original ColorItem constructor data,\n   * plus a 'valid' flag to know if it's valid or not.\n   *\n   * @returns {{color: *, format: String, valid: boolean}}\n   */\n  get original() {\n    return this._original;\n  }\n\n  /**\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n   */\n  constructor(color = null, format = null) {\n    this.replace(color, format);\n  }\n\n  /**\n   * Replaces the internal QixColor object with a new one.\n   * This also replaces the internal original color data.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data to be parsed (if needed)\n   * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n   * @example color.replace('rgb(255,0,0)', 'hsl');\n   * @example color.replace(hsvaColorData);\n   */\n  replace(color, format = null) {\n    format = ColorItem.sanitizeFormat(format);\n\n    /**\n     * @type {{color: *, format: String}}\n     * @private\n     */\n    this._original = {\n      color: color,\n      format: format,\n      valid: true\n    };\n    /**\n     * @type {QixColor}\n     * @private\n     */\n    this._color = ColorItem.parse(color);\n\n    if (this._color === null) {\n      this._color = QixColor();\n      this._original.valid = false;\n      return;\n    }\n\n    /**\n     * @type {*|string}\n     * @private\n     */\n    this._format = format ? format :\n      (ColorItem.isHex(color) ? 'hex' : this._color.model);\n  }\n\n  /**\n   * Parses the color returning a Qix Color object or null if cannot be\n   * parsed.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   * @example let qColor = ColorItem.parse('rgb(255,0,0)');\n   * @static\n   * @returns {QixColor|null}\n   */\n  static parse(color) {\n    if (color instanceof QixColor) {\n      return color;\n    }\n\n    if (color instanceof ColorItem) {\n      return color._color;\n    }\n\n    let format = null;\n\n    if (color instanceof HSVAColor) {\n      color = [color.h, color.s, color.v, isNaN(color.a) ? 1 : color.a];\n    } else {\n      color = ColorItem.sanitizeString(color);\n    }\n\n    if (color === null) {\n      return null;\n    }\n\n    if (Array.isArray(color)) {\n      format = 'hsv';\n    }\n\n    try {\n      return QixColor(color, format);\n    } catch (e) {\n      return null;\n    }\n  }\n\n  /**\n   * Sanitizes a color string, adding missing hash to hexadecimal colors\n   * and converting 'transparent' to a color code.\n   *\n   * @param {String|*} str Color string\n   * @example let colorStr = ColorItem.sanitizeString('ffaa00');\n   * @static\n   * @returns {String|*}\n   */\n  static sanitizeString(str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n      return str;\n    }\n\n    if (str.match(/^[0-9a-f]{2,}$/i)) {\n      return `#${str}`;\n    }\n\n    if (str.toLowerCase() === 'transparent') {\n      return '#FFFFFF00';\n    }\n\n    return str;\n  }\n\n  /**\n   * Detects if a value is a string and a color in hexadecimal format (in any variant).\n   *\n   * @param {String} str\n   * @example ColorItem.isHex('rgba(0,0,0)'); // false\n   * @example ColorItem.isHex('ffaa00'); // true\n   * @example ColorItem.isHex('#ffaa00'); // true\n   * @static\n   * @returns {boolean}\n   */\n  static isHex(str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n      return false;\n    }\n\n    return !!str.match(/^#?[0-9a-f]{2,}$/i);\n  }\n\n  /**\n   * Sanitizes a color format to one supported by web browsers.\n   * Returns an empty string of the format can't be recognised.\n   *\n   * @param {String|*} format\n   * @example ColorItem.sanitizeFormat('rgba'); // 'rgb'\n   * @example ColorItem.isHex('hex8'); // 'hex'\n   * @example ColorItem.isHex('invalid'); // ''\n   * @static\n   * @returns {String} 'rgb', 'hsl', 'hex' or ''.\n   */\n  static sanitizeFormat(format) {\n    switch (format) {\n      case 'hex':\n      case 'hex3':\n      case 'hex4':\n      case 'hex6':\n      case 'hex8':\n        return 'hex';\n      case 'rgb':\n      case 'rgba':\n      case 'keyword':\n      case 'name':\n        return 'rgb';\n      case 'hsl':\n      case 'hsla':\n      case 'hsv':\n      case 'hsva':\n      case 'hwb': // HWB this is supported by Qix Color, but not by browsers\n      case 'hwba':\n        return 'hsl';\n      default :\n        return '';\n    }\n  }\n\n  /**\n   * Returns true if the color is valid, false if not.\n   *\n   * @returns {boolean}\n   */\n  isValid() {\n    return this._original.valid === true;\n  }\n\n  /**\n   * Hue value from 0 to 360\n   *\n   * @returns {int}\n   */\n  get hue() {\n    return this._color.hue();\n  }\n\n  /**\n   * Saturation value from 0 to 100\n   *\n   * @returns {int}\n   */\n  get saturation() {\n    return this._color.saturationv();\n  }\n\n  /**\n   * Value channel value from 0 to 100\n   *\n   * @returns {int}\n   */\n  get value() {\n    return this._color.value();\n  }\n\n  /**\n   * Alpha value from 0.0 to 1.0\n   *\n   * @returns {number}\n   */\n  get alpha() {\n    let a = this._color.alpha();\n\n    return isNaN(a) ? 1 : a;\n  }\n\n  /**\n   * Default color format to convert to when calling toString() or string()\n   *\n   * @returns {String} 'rgb', 'hsl', 'hex' or ''\n   */\n  get format() {\n    return this._format ? this._format : this._color.model;\n  }\n\n  /**\n   * Sets the hue value\n   *\n   * @param {int} value Integer from 0 to 360\n   */\n  set hue(value) {\n    this._color = this._color.hue(value);\n  }\n\n  /**\n   * Sets the hue ratio, where 1.0 is 0, 0.5 is 180 and 0.0 is 360.\n   *\n   * @ignore\n   * @param {number} h Ratio from 1.0 to 0.0\n   */\n  setHueRatio(h) {\n    this.hue = ((1 - h) * 360);\n  }\n\n  /**\n   * Sets the saturation value\n   *\n   * @param {int} value Integer from 0 to 100\n   */\n  set saturation(value) {\n    this._color = this._color.saturationv(value);\n  }\n\n  /**\n   * Sets the saturation ratio, where 1.0 is 100 and 0.0 is 0.\n   *\n   * @ignore\n   * @param {number} s Ratio from 0.0 to 1.0\n   */\n  setSaturationRatio(s) {\n    this.saturation = (s * 100);\n  }\n\n  /**\n   * Sets the 'value' channel value\n   *\n   * @param {int} value Integer from 0 to 100\n   */\n  set value(value) {\n    this._color = this._color.value(value);\n  }\n\n  /**\n   * Sets the value ratio, where 1.0 is 0 and 0.0 is 100.\n   *\n   * @ignore\n   * @param {number} v Ratio from 1.0 to 0.0\n   */\n  setValueRatio(v) {\n    this.value = ((1 - v) * 100);\n  }\n\n  /**\n   * Sets the alpha value. It will be rounded to 2 decimals.\n   *\n   * @param {int} value Float from 0.0 to 1.0\n   */\n  set alpha(value) {\n    // 2 decimals max\n    this._color = this._color.alpha(Math.round(value * 100) / 100);\n  }\n\n  /**\n   * Sets the alpha ratio, where 1.0 is 0.0 and 0.0 is 1.0.\n   *\n   * @ignore\n   * @param {number} a Ratio from 1.0 to 0.0\n   */\n  setAlphaRatio(a) {\n    this.alpha = 1 - a;\n  }\n\n  /**\n   * Sets the default color format\n   *\n   * @param {String} value Supported: 'rgb', 'hsl', 'hex'\n   */\n  set format(value) {\n    this._format = ColorItem.sanitizeFormat(value);\n  }\n\n  /**\n   * Returns true if the saturation value is zero, false otherwise\n   *\n   * @returns {boolean}\n   */\n  isDesaturated() {\n    return this.saturation === 0;\n  }\n\n  /**\n   * Returns true if the alpha value is zero, false otherwise\n   *\n   * @returns {boolean}\n   */\n  isTransparent() {\n    return this.alpha === 0;\n  }\n\n  /**\n   * Returns true if the alpha value is numeric and less than 1, false otherwise\n   *\n   * @returns {boolean}\n   */\n  hasTransparency() {\n    return this.hasAlpha() && (this.alpha < 1);\n  }\n\n  /**\n   * Returns true if the alpha value is numeric, false otherwise\n   *\n   * @returns {boolean}\n   */\n  hasAlpha() {\n    return !isNaN(this.alpha);\n  }\n\n  /**\n   * Returns a new HSVAColor object, based on the current color\n   *\n   * @returns {HSVAColor}\n   */\n  toObject() {\n    return new HSVAColor(this.hue, this.saturation, this.value, this.alpha);\n  }\n\n  /**\n   * Alias of toObject()\n   *\n   * @returns {HSVAColor}\n   */\n  toHsva() {\n    return this.toObject();\n  }\n\n  /**\n   * Returns a new HSVAColor object with the ratio values (from 0.0 to 1.0),\n   * based on the current color.\n   *\n   * @ignore\n   * @returns {HSVAColor}\n   */\n  toHsvaRatio() {\n    return new HSVAColor(\n      this.hue / 360,\n      this.saturation / 100,\n      this.value / 100,\n      this.alpha\n    );\n  }\n\n  /**\n   * Converts the current color to its string representation,\n   * using the internal format of this instance.\n   *\n   * @returns {String}\n   */\n  toString() {\n    return this.string();\n  }\n\n  /**\n   * Converts the current color to its string representation,\n   * using the given format.\n   *\n   * @param {String|null} format Format to convert to. If empty or null, the internal format will be used.\n   * @returns {String}\n   */\n  string(format = null) {\n    format = ColorItem.sanitizeFormat(format ? format : this.format);\n\n    if (!format) {\n      return this._color.round().string();\n    }\n\n    if (this._color[format] === undefined) {\n      throw new Error(`Unsupported color format: '${format}'`);\n    }\n\n    let str = this._color[format]();\n\n    return str.round ? str.round().string() : str;\n  }\n\n  /**\n   * Returns true if the given color values equals this one, false otherwise.\n   * The format is not compared.\n   * If any of the colors is invalid, the result will be false.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   *\n   * @returns {boolean}\n   */\n  equals(color) {\n    color = (color instanceof ColorItem) ? color : new ColorItem(color);\n\n    if (!color.isValid() || !this.isValid()) {\n      return false;\n    }\n\n    return (\n      this.hue === color.hue &&\n      this.saturation === color.saturation &&\n      this.value === color.value &&\n      this.alpha === color.alpha\n    );\n  }\n\n  /**\n   * Creates a copy of this instance\n   *\n   * @returns {ColorItem}\n   */\n  getClone() {\n    return new ColorItem(this._color, this.format);\n  }\n\n  /**\n   * Creates a copy of this instance, only copying the hue value,\n   * and setting the others to its max value.\n   *\n   * @returns {ColorItem}\n   */\n  getCloneHueOnly() {\n    return new ColorItem([this.hue, 100, 100, 1], this.format);\n  }\n\n  /**\n   * Creates a copy of this instance setting the alpha to the max.\n   *\n   * @returns {ColorItem}\n   */\n  getCloneOpaque() {\n    return new ColorItem(this._color.alpha(1), this.format);\n  }\n\n  /**\n   * Converts the color to a RGB string\n   *\n   * @returns {String}\n   */\n  toRgbString() {\n    return this.string('rgb');\n  }\n\n  /**\n   * Converts the color to a Hexadecimal string\n   *\n   * @returns {String}\n   */\n  toHexString() {\n    return this.string('hex');\n  }\n\n  /**\n   * Converts the color to a HSL string\n   *\n   * @returns {String}\n   */\n  toHslString() {\n    return this.string('hsl');\n  }\n\n  /**\n   * Returns true if the color is dark, false otherwhise.\n   * This is useful to decide a text color.\n   *\n   * @returns {boolean}\n   */\n  isDark() {\n    return this._color.isDark();\n  }\n\n  /**\n   * Returns true if the color is light, false otherwhise.\n   * This is useful to decide a text color.\n   *\n   * @returns {boolean}\n   */\n  isLight() {\n    return this._color.isLight();\n  }\n\n  /**\n   * Generates a list of colors using the given hue-based formula or the given array of hue values.\n   * Hue formulas can be extended using ColorItem.colorFormulas static property.\n   *\n   * @param {String|Number[]} formula Examples: 'complementary', 'triad', 'tetrad', 'splitcomplement', [180, 270]\n   * @example let colors = color.generate('triad');\n   * @example let colors = color.generate([45, 80, 112, 200]);\n   * @returns {ColorItem[]}\n   */\n  generate(formula) {\n    let hues = [];\n\n    if (Array.isArray(formula)) {\n      hues = formula;\n    } else if (!ColorItem.colorFormulas.hasOwnProperty(formula)) {\n      throw new Error(`No color formula found with the name '${formula}'.`);\n    } else {\n      hues = ColorItem.colorFormulas[formula];\n    }\n\n    let colors = [], mainColor = this._color, format = this.format;\n\n    hues.forEach(function (hue) {\n      let levels = [\n        hue ? ((mainColor.hue() + hue) % 360) : mainColor.hue(),\n        mainColor.saturationv(),\n        mainColor.value(),\n        mainColor.alpha()\n      ];\n\n      colors.push(new ColorItem(levels, format));\n    });\n\n    return colors;\n  }\n}\n\n/**\n * List of hue-based color formulas used by ColorItem.prototype.generate()\n *\n * @static\n * @type {{complementary: number[], triad: number[], tetrad: number[], splitcomplement: number[]}}\n */\nColorItem.colorFormulas = {\n  complementary: [180],\n  triad: [0, 120, 240],\n  tetrad: [0, 90, 180, 270],\n  splitcomplement: [0, 72, 216]\n};\n\nexport default ColorItem;\n\nexport {\n  HSVAColor,\n  ColorItem\n};\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/ColorItem.js", "'use strict';\n/**\n * @module\n */\n\n// adjust these values accordingly to the sass vars\nlet sassVars = {\n  'bar_size_short': 16,\n  'base_margin': 6,\n  'columns': 6\n};\n\nlet sliderSize = (sassVars.bar_size_short * sassVars.columns) + (sassVars.base_margin * (sassVars.columns - 1));\n\n/**\n * Colorpicker default options\n */\nexport default {\n  /**\n   * Custom class to be added to the `.colorpicker-element` element\n   *\n   * @type {String|null}\n   * @default null\n   */\n  customClass: null,\n  /**\n   * Sets a initial color, ignoring the one from the element/input value or the data-color attribute.\n   *\n   * @type {(String|ColorItem|boolean)}\n   * @default false\n   */\n  color: false,\n  /**\n   * Fallback color to use when the given color is invalid.\n   * If false, the latest valid color will be used as a fallback.\n   *\n   * @type {String|ColorItem|boolean}\n   * @default false\n   */\n  fallbackColor: false,\n  /**\n   * Forces an specific color format. If 'auto', it will be automatically detected the first time only,\n   * but if null it will be always recalculated.\n   *\n   * Note that the ending 'a' of the format meaning \"alpha\" has currently no effect, meaning that rgb is the same as\n   * rgba excepting if the alpha channel is disabled (see useAlpha).\n   *\n   * @type {('rgb'|'hex'|'hsl'|'auto'|null)}\n   * @default 'auto'\n   */\n  format: 'auto',\n  /**\n   * Horizontal mode layout.\n   *\n   * If true, the hue and alpha channel bars will be rendered horizontally, above the saturation selector.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  horizontal: false,\n  /**\n   * Forces to show the colorpicker as an inline element.\n   *\n   * Note that if there is no container specified, the inline element\n   * will be added to the body, so you may want to set the container option.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  inline: false,\n  /**\n   * Container where the colorpicker is appended to in the DOM.\n   *\n   * If is a string (CSS selector), the colorpicker will be placed inside this container.\n   * If true, the `.colorpicker-element` element itself will be used as the container.\n   * If false, the document body is used as the container, unless it is a popover (in this case it is appended to the\n   * popover body instead).\n   *\n   * @type {String|boolean}\n   * @default false\n   */\n  container: false,\n  /**\n   * Bootstrap Popover options.\n   * The trigger, content and html options are always ignored.\n   *\n   * @type {boolean}\n   * @default Object\n   */\n  popover: {\n    animation: true,\n    placement: 'bottom',\n    fallbackPlacement: 'flip'\n  },\n  /**\n   * If true, loads the 'debugger' extension automatically, which logs the events in the console\n   * @type {boolean}\n   * @default false\n   */\n  debug: false,\n  /**\n   * Child CSS selector for the colorpicker input.\n   *\n   * @type {String}\n   * @default 'input'\n   */\n  input: 'input',\n  /**\n   * Child CSS selector for the colorpicker addon.\n   * If it exists, the child <i> element background will be changed on color change.\n   *\n   * @type {String}\n   * @default '.colorpicker-trigger, .colorpicker-input-addon'\n   */\n  addon: '.colorpicker-input-addon',\n  /**\n   * If true, the input content will be replaced always with a valid color,\n   * if false, the invalid color will be left in the input,\n   *   while the internal color object will still resolve into a valid one.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  autoInputFallback: true,\n  /**\n   * If true a hash will be prepended to hexadecimal colors.\n   * If false, the hash will be removed.\n   * This only affects the input values in hexadecimal format.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useHashPrefix: true,\n  /**\n   * If true, the alpha channel bar will be displayed no matter what.\n   *\n   * If false, it will be always hidden and alpha channel will be disabled also programmatically, meaning that\n   * the selected or typed color will be always opaque.\n   *\n   * If null, the alpha channel will be automatically disabled/enabled depending if the initial color format supports\n   * alpha or not.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useAlpha: true,\n  /**\n   * Colorpicker widget template\n   * @type {String}\n   * @example\n   * <!-- This is the default template: -->\n   * <div class=\"colorpicker\">\n   *   <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-alpha\">\n   *     <div class=\"colorpicker-alpha-color\"></div>\n   *     <i class=\"colorpicker-guide\"></i>\n   *   </div>\n   * </div>\n   */\n  template: `<div class=\"colorpicker\">\n      <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\n      <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\n      <div class=\"colorpicker-alpha\">\n        <div class=\"colorpicker-alpha-color\"></div>\n        <i class=\"colorpicker-guide\"></i>\n      </div>\n    </div>`,\n  /**\n   *\n   * Associative object with the extension class name and its config.\n   * Colorpicker comes with many bundled extensions: debugger, palette, preview and swatches (a superset of palette).\n   *\n   * @type {Object[]}\n   * @example\n   *   extensions: [\n   *     {\n   *       name: 'swatches'\n   *       options: {\n   *         colors: {\n   *           'primary': '#337ab7',\n   *           'success': '#5cb85c',\n   *           'info': '#5bc0de',\n   *           'warning': '#f0ad4e',\n   *           'danger': '#d9534f'\n   *         },\n   *         namesAsValues: true\n   *       }\n   *     }\n   *   ]\n   */\n  extensions: [\n    {\n      name: 'preview',\n      options: {\n        showText: true\n      }\n    }\n  ],\n  /**\n   * Vertical sliders configuration\n   * @type {Object}\n   */\n  sliders: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setHueRatio'\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setAlphaRatio'\n    }\n  },\n  /**\n   * Horizontal sliders configuration\n   * @type {Object}\n   */\n  slidersHorz: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setHueRatio',\n      callTop: false\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setAlphaRatio',\n      callTop: false\n    }\n  }\n};\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/options.js", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\nlet defaults = {\n  /**\n   * Key-value pairs defining a color alias and its CSS color representation.\n   *\n   * They can also be just an array of values. In that case, no special names are used, only the real colors.\n   *\n   * @type {Object|Array}\n   * @default null\n   * @example\n   *  {\n   *   'black': '#000000',\n   *   'white': '#ffffff',\n   *   'red': '#FF0000',\n   *   'default': '#777777',\n   *   'primary': '#337ab7',\n   *   'success': '#5cb85c',\n   *   'info': '#5bc0de',\n   *   'warning': '#f0ad4e',\n   *   'danger': '#d9534f'\n   *  }\n   *\n   * @example ['#f0ad4e', '#337ab7', '#5cb85c']\n   */\n  colors: null,\n  /**\n   * If true, when a color swatch is selected the name (alias) will be used as input value,\n   * otherwise the swatch real color value will be used.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  namesAsValues: true\n};\n\n/**\n * Palette extension\n * @ignore\n */\nclass Palette extends Extension {\n\n  /**\n   * @returns {Object|Array}\n   */\n  get colors() {\n    return this.options.colors;\n  }\n\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {}, defaults, options));\n\n    if ((!Array.isArray(this.options.colors)) && (typeof this.options.colors !== 'object')) {\n      this.options.colors = null;\n    }\n  }\n\n  /**\n   * @returns {int}\n   */\n  getLength() {\n    if (!this.options.colors) {\n      return 0;\n    }\n\n    if (Array.isArray(this.options.colors)) {\n      return this.options.colors.length;\n    }\n\n    if (typeof this.options.colors === 'object') {\n      return Object.keys(this.options.colors).length;\n    }\n\n    return 0;\n  }\n\n  resolveColor(color, realColor = true) {\n    if (this.getLength() <= 0) {\n      return false;\n    }\n\n    // Array of colors\n    if (Array.isArray(this.options.colors)) {\n      if (this.options.colors.indexOf(color) >= 0) {\n        return color;\n      }\n      if (this.options.colors.indexOf(color.toUpperCase()) >= 0) {\n        return color.toUpperCase();\n      }\n      if (this.options.colors.indexOf(color.toLowerCase()) >= 0) {\n        return color.toLowerCase();\n      }\n      return false;\n    }\n\n    if (typeof this.options.colors !== 'object') {\n      return false;\n    }\n\n    // Map of objects\n    if (!this.options.namesAsValues || realColor) {\n      return this.getValue(color, false);\n    }\n    return this.getName(color, this.getName('#' + color));\n  }\n\n  /**\n   * Given a color value, returns the corresponding color name or defaultValue.\n   *\n   * @param {String} value\n   * @param {*} defaultValue\n   * @returns {*}\n   */\n  getName(value, defaultValue = false) {\n    if (!(typeof value === 'string') || !this.options.colors) {\n      return defaultValue;\n    }\n    for (let name in this.options.colors) {\n      if (!this.options.colors.hasOwnProperty(name)) {\n        continue;\n      }\n      if (this.options.colors[name].toLowerCase() === value.toLowerCase()) {\n        return name;\n      }\n    }\n    return defaultValue;\n  }\n\n  /**\n   * Given a color name, returns the corresponding color value or defaultValue.\n   *\n   * @param {String} name\n   * @param {*} defaultValue\n   * @returns {*}\n   */\n  getValue(name, defaultValue = false) {\n    if (!(typeof name === 'string') || !this.options.colors) {\n      return defaultValue;\n    }\n    if (this.options.colors.hasOwnProperty(name)) {\n      return this.options.colors[name];\n    }\n    return defaultValue;\n  }\n}\n\nexport default Palette;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/extensions/Palette.js", "'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color-name/index.js\n// module id = 5\n// module chunks = 0 1", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color-convert/conversions.js\n// module id = 6\n// module chunks = 0 1", "'use strict';\n\nimport Colorpicker from './Colorpicker';\nimport $ from 'jquery';\n\nlet plugin = 'colorpicker';\n\n$[plugin] = Colorpicker;\n\n// Colorpicker jQuery Plugin API\n$.fn[plugin] = function (option) {\n  let fnArgs = Array.prototype.slice.call(arguments, 1),\n    isSingleElement = (this.length === 1),\n    returnValue = null;\n\n  let $elements = this.each(function () {\n    let $this = $(this),\n      inst = $this.data(plugin),\n      options = ((typeof option === 'object') ? option : {});\n\n    // Create instance if does not exist\n    if (!inst) {\n      inst = new Colorpicker(this, options);\n      $this.data(plugin, inst);\n    }\n\n    if (!isSingleElement) {\n      return;\n    }\n\n    returnValue = $this;\n\n    if (typeof option === 'string') {\n      if (option === 'colorpicker') {\n        // Return colorpicker instance: e.g. .colorpicker('colorpicker')\n        returnValue = inst;\n      } else if ($.isFunction(inst[option])) {\n        // Return method call return value: e.g. .colorpicker('isEnabled')\n        returnValue = inst[option].apply(inst, fnArgs);\n      } else {\n        // Return property value: e.g. .colorpicker('element')\n        returnValue = inst[option];\n      }\n    }\n  });\n\n  return isSingleElement ? returnValue : $elements;\n};\n\n$.fn[plugin].constructor = Colorpicker;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/plugin.js", "'use strict';\n\nimport Extension from './Extension';\nimport defaults from './options';\nimport coreExtensions from 'extensions';\nimport $ from 'jquery';\nimport Slider<PERSON>andler from './SliderHandler';\nimport PopupHandler from './PopupHandler';\nimport InputHandler from './InputHandler';\nimport ColorHandler from './ColorHandler';\nimport PickerHandler from './PickerHandler';\nimport AddonHandler from './AddonHandler';\nimport ColorItem from './ColorItem';\n\nlet colorPickerIdCounter = 0;\nlet root = (typeof self !== 'undefined' ? self : this); // window\n\n/**\n * Colorpicker widget class\n */\nclass Colorpicker {\n  /**\n   * Color class\n   *\n   * @static\n   * @type {Color}\n   */\n  static get Color() {\n    return ColorItem;\n  }\n\n  /**\n   * Extension class\n   *\n   * @static\n   * @type {Extension}\n   */\n  static get Extension() {\n    return Extension;\n  }\n\n  /**\n   * Internal color object\n   *\n   * @type {Color|null}\n   */\n  get color() {\n    return this.colorHandler.color;\n  }\n\n  /**\n   * Internal color format\n   *\n   * @type {String|null}\n   */\n  get format() {\n    return this.colorHandler.format;\n  }\n\n  /**\n   * Getter of the picker element\n   *\n   * @returns {jQuery|HTMLElement}\n   */\n  get picker() {\n    return this.pickerHandler.picker;\n  }\n\n  /**\n   * @fires Colorpicker#colorpickerCreate\n   * @param {Object|String} element\n   * @param {Object} options\n   * @constructor\n   */\n  constructor(element, options) {\n    colorPickerIdCounter += 1;\n    /**\n     * The colorpicker instance number\n     * @type {number}\n     */\n    this.id = colorPickerIdCounter;\n\n    /**\n     * Latest colorpicker event\n     *\n     * @type {{name: String, e: *}}\n     */\n    this.lastEvent = {\n      alias: null,\n      e: null\n    };\n\n    /**\n     * The element that the colorpicker is bound to\n     *\n     * @type {*|jQuery}\n     */\n    this.element = $(element)\n      .addClass('colorpicker-element')\n      .attr('data-colorpicker-id', this.id);\n\n    /**\n     * @type {defaults}\n     */\n    this.options = $.extend(true, {}, defaults, options, this.element.data());\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.disabled = false;\n\n    /**\n     * Extensions added to this instance\n     *\n     * @type {Extension[]}\n     */\n    this.extensions = [];\n\n    /**\n     * The element where the\n     * @type {*|jQuery}\n     */\n    this.container = (\n      this.options.container === true ||\n      (this.options.container !== true && this.options.inline === true)\n    ) ? this.element : this.options.container;\n\n    this.container = (this.container !== false) ? $(this.container) : false;\n\n    /**\n     * @type {InputHandler}\n     */\n    this.inputHandler = new InputHandler(this);\n    /**\n     * @type {ColorHandler}\n     */\n    this.colorHandler = new ColorHandler(this);\n    /**\n     * @type {SliderHandler}\n     */\n    this.sliderHandler = new SliderHandler(this);\n    /**\n     * @type {PopupHandler}\n     */\n    this.popupHandler = new PopupHandler(this, root);\n    /**\n     * @type {PickerHandler}\n     */\n    this.pickerHandler = new PickerHandler(this);\n    /**\n     * @type {AddonHandler}\n     */\n    this.addonHandler = new AddonHandler(this);\n\n    this.init();\n\n    // Emit a create event\n    $($.proxy(function () {\n      /**\n       * (Colorpicker) When the Colorpicker instance has been created and the DOM is ready.\n       *\n       * @event Colorpicker#colorpickerCreate\n       */\n      this.trigger('colorpickerCreate');\n    }, this));\n  }\n\n  /**\n   * Initializes the plugin\n   * @private\n   */\n  init() {\n    // Init addon\n    this.addonHandler.bind();\n\n    // Init input\n    this.inputHandler.bind();\n\n    // Init extensions (before initializing the color)\n    this.initExtensions();\n\n    // Init color\n    this.colorHandler.bind();\n\n    // Init picker\n    this.pickerHandler.bind();\n\n    // Init sliders and popup\n    this.sliderHandler.bind();\n    this.popupHandler.bind();\n\n    // Inject into the DOM (this may make it visible)\n    this.pickerHandler.attach();\n\n    // Update all components\n    this.update();\n\n    if (this.inputHandler.isDisabled()) {\n      this.disable();\n    }\n  }\n\n  /**\n   * Initializes the plugin extensions\n   * @private\n   */\n  initExtensions() {\n    if (!Array.isArray(this.options.extensions)) {\n      this.options.extensions = [];\n    }\n\n    if (this.options.debug) {\n      this.options.extensions.push({name: 'debugger'});\n    }\n\n    // Register and instantiate extensions\n    this.options.extensions.forEach((ext) => {\n      this.registerExtension(Colorpicker.extensions[ext.name.toLowerCase()], ext.options || {});\n    });\n  }\n\n  /**\n   * Creates and registers the given extension\n   *\n   * @param {Extension} ExtensionClass The extension class to instantiate\n   * @param {Object} [config] Extension configuration\n   * @returns {Extension}\n   */\n  registerExtension(ExtensionClass, config = {}) {\n    let ext = new ExtensionClass(this, config);\n\n    this.extensions.push(ext);\n    return ext;\n  }\n\n  /**\n   * Destroys the current instance\n   *\n   * @fires Colorpicker#colorpickerDestroy\n   */\n  destroy() {\n    let color = this.color;\n\n    this.sliderHandler.unbind();\n    this.inputHandler.unbind();\n    this.popupHandler.unbind();\n    this.colorHandler.unbind();\n    this.addonHandler.unbind();\n    this.pickerHandler.unbind();\n\n    this.element\n      .removeClass('colorpicker-element')\n      .removeData('colorpicker', 'color')\n      .off('.colorpicker');\n\n    /**\n     * (Colorpicker) When the instance is destroyed with all events unbound.\n     *\n     * @event Colorpicker#colorpickerDestroy\n     */\n    this.trigger('colorpickerDestroy', color);\n  }\n\n  /**\n   * Shows the colorpicker widget if hidden.\n   * If the colorpicker is disabled this call will be ignored.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @param {Event} [e]\n   */\n  show(e) {\n    this.popupHandler.show(e);\n  }\n\n  /**\n   * Hides the colorpicker widget.\n   *\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  hide(e) {\n    this.popupHandler.hide(e);\n  }\n\n  /**\n   * Toggles the colorpicker between visible and hidden.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  toggle(e) {\n    this.popupHandler.toggle(e);\n  }\n\n  /**\n   * Returns the current color value as string\n   *\n   * @param {String|*} [defaultValue]\n   * @returns {String|*}\n   */\n  getValue(defaultValue = null) {\n    let val = this.colorHandler.color;\n\n    val = (val instanceof ColorItem) ? val : defaultValue;\n\n    if (val instanceof ColorItem) {\n      return val.string(this.format);\n    }\n\n    return val;\n  }\n\n  /**\n   * Sets the color manually\n   *\n   * @fires Colorpicker#colorpickerChange\n   * @param {String|Color} val\n   */\n  setValue(val) {\n    if (this.isDisabled()) {\n      return;\n    }\n    let ch = this.colorHandler;\n\n    if (\n      (ch.hasColor() && !!val && ch.color.equals(val)) ||\n      (!ch.hasColor() && !val)\n    ) {\n      // same color or still empty\n      return;\n    }\n\n    ch.color = val ? ch.createColor(val, this.options.autoInputFallback) : null;\n\n    /**\n     * (Colorpicker) When the color is set programmatically with setValue().\n     *\n     * @event Colorpicker#colorpickerChange\n     */\n    this.trigger('colorpickerChange', ch.color, val);\n\n    // force update if color has changed to empty\n    this.update();\n  }\n\n  /**\n   * Updates the UI and the input color according to the internal color.\n   *\n   * @fires Colorpicker#colorpickerUpdate\n   */\n  update() {\n    if (this.colorHandler.hasColor()) {\n      this.inputHandler.update();\n    } else {\n      this.colorHandler.assureColor();\n    }\n\n    this.addonHandler.update();\n    this.pickerHandler.update();\n\n    /**\n     * (Colorpicker) Fired when the widget is updated.\n     *\n     * @event Colorpicker#colorpickerUpdate\n     */\n    this.trigger('colorpickerUpdate');\n  }\n\n  /**\n   * Enables the widget and the input if any\n   *\n   * @fires Colorpicker#colorpickerEnable\n   * @returns {boolean}\n   */\n  enable() {\n    this.inputHandler.enable();\n    this.disabled = false;\n    this.picker.removeClass('colorpicker-disabled');\n\n    /**\n     * (Colorpicker) When the widget has been enabled.\n     *\n     * @event Colorpicker#colorpickerEnable\n     */\n    this.trigger('colorpickerEnable');\n    return true;\n  }\n\n  /**\n   * Disables the widget and the input if any\n   *\n   * @fires Colorpicker#colorpickerDisable\n   * @returns {boolean}\n   */\n  disable() {\n    this.inputHandler.disable();\n    this.disabled = true;\n    this.picker.addClass('colorpicker-disabled');\n\n    /**\n     * (Colorpicker) When the widget has been disabled.\n     *\n     * @event Colorpicker#colorpickerDisable\n     */\n    this.trigger('colorpickerDisable');\n    return true;\n  }\n\n  /**\n   * Returns true if this instance is enabled\n   * @returns {boolean}\n   */\n  isEnabled() {\n    return !this.isDisabled();\n  }\n\n  /**\n   * Returns true if this instance is disabled\n   * @returns {boolean}\n   */\n  isDisabled() {\n    return this.disabled === true;\n  }\n\n  /**\n   * Triggers a Colorpicker event.\n   *\n   * @param eventName\n   * @param color\n   * @param value\n   */\n  trigger(eventName, color = null, value = null) {\n    this.element.trigger({\n      type: eventName,\n      colorpicker: this,\n      color: color ? color : this.color,\n      value: value ? value : this.getValue()\n    });\n  }\n}\n\n/**\n * Colorpicker extension classes, indexed by extension name\n *\n * @static\n * @type {Object} a map between the extension name and its class\n */\nColorpicker.extensions = coreExtensions;\n\nexport default Colorpicker;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/Colorpicker.js", "import Debugger from './Debugger';\nimport Preview from './Preview';\nimport Swatches from './Swatches';\nimport Palette from './Palette';\n\nexport {\n  Debugger, Preview, Swatches, Palette\n};\n\nexport default {\n  'debugger': Debugger,\n  'preview': Preview,\n  'swatches': Swatches,\n  'palette': Palette\n};\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/extensions/index.js", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\n/**\n * Debugger extension class\n * @alias DebuggerExtension\n * @ignore\n */\nclass Debugger extends Extension {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, options);\n\n    /**\n     * @type {number}\n     */\n    this.eventCounter = 0;\n    if (this.colorpicker.inputHandler.hasInput()) {\n      this.colorpicker.inputHandler.input.on('change.colorpicker-ext', $.proxy(this.onChangeInput, this));\n    }\n  }\n\n  /**\n   * @fires DebuggerExtension#colorpickerDebug\n   * @param {string} eventName\n   * @param {*} args\n   */\n  log(eventName, ...args) {\n    this.eventCounter += 1;\n\n    let logMessage = `#${this.eventCounter}: Colorpicker#${this.colorpicker.id} [${eventName}]`;\n\n    console.debug(logMessage, ...args);\n\n    /**\n     * Whenever the debugger logs an event, this other event is emitted.\n     *\n     * @event DebuggerExtension#colorpickerDebug\n     * @type {object} The event object\n     * @property {Colorpicker} colorpicker The Colorpicker instance\n     * @property {ColorItem} color The color instance\n     * @property {{debugger: DebuggerExtension, eventName: String, logArgs: Array, logMessage: String}} debug\n     *  The debug info\n     */\n    this.colorpicker.element.trigger({\n      type: 'colorpickerDebug',\n      colorpicker: this.colorpicker,\n      color: this.color,\n      value: null,\n      debug: {\n        debugger: this,\n        eventName: eventName,\n        logArgs: args,\n        logMessage: logMessage\n      }\n    });\n  }\n\n  resolveColor(color, realColor = true) {\n    this.log('resolveColor()', color, realColor);\n    return false;\n  }\n\n  onCreate(event) {\n    this.log('colorpickerCreate');\n    return super.onCreate(event);\n  }\n\n  onDestroy(event) {\n    this.log('colorpickerDestroy');\n    this.eventCounter = 0;\n\n    if (this.colorpicker.inputHandler.hasInput()) {\n      this.colorpicker.inputHandler.input.off('.colorpicker-ext');\n    }\n\n    return super.onDestroy(event);\n  }\n\n  onUpdate(event) {\n    this.log('colorpickerUpdate');\n  }\n\n  /**\n   * @listens Colorpicker#change\n   * @param {Event} event\n   */\n  onChangeInput(event) {\n    this.log('input:change.colorpicker', event.value, event.color);\n  }\n\n  onChange(event) {\n    this.log('colorpickerChange', event.value, event.color);\n  }\n\n  onInvalid(event) {\n    this.log('colorpickerInvalid', event.value, event.color);\n  }\n\n  onHide(event) {\n    this.log('colorpickerHide');\n    this.eventCounter = 0;\n  }\n\n  onShow(event) {\n    this.log('colorpickerShow');\n  }\n\n  onDisable(event) {\n    this.log('colorpickerDisable');\n  }\n\n  onEnable(event) {\n    this.log('colorpickerEnable');\n  }\n}\n\nexport default Debugger;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/extensions/Debugger.js", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\n/**\n * Color preview extension\n * @ignore\n */\nclass Preview extends Extension {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {},\n      {\n        template: '<div class=\"colorpicker-bar colorpicker-preview\"><div /></div>',\n        showText: true,\n        format: colorpicker.format\n      },\n      options\n    ));\n\n    this.element = $(this.options.template);\n    this.elementInner = this.element.find('div');\n  }\n\n  onCreate(event) {\n    super.onCreate(event);\n    this.colorpicker.picker.append(this.element);\n  }\n\n  onUpdate(event) {\n    super.onUpdate(event);\n\n    if (!event.color) {\n      this.elementInner\n        .css('backgroundColor', null)\n        .css('color', null)\n        .html('');\n      return;\n    }\n\n    this.elementInner\n      .css('backgroundColor', event.color.toRgbString());\n\n    if (this.options.showText) {\n      this.elementInner\n        .html(event.color.string(this.options.format || this.colorpicker.format));\n\n      if (event.color.isDark() && (event.color.alpha > 0.5)) {\n        this.elementInner.css('color', 'white');\n      } else {\n        this.elementInner.css('color', 'black');\n      }\n    }\n  }\n}\n\nexport default Preview;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/extensions/Preview.js", "'use strict';\n\nimport Palette from './Palette';\nimport $ from 'jquery';\n\nlet defaults = {\n  barTemplate: `<div class=\"colorpicker-bar colorpicker-swatches\">\n                    <div class=\"colorpicker-swatches--inner\"></div>\n                </div>`,\n  swatchTemplate: '<i class=\"colorpicker-swatch\"><i class=\"colorpicker-swatch--inner\"></i></i>'\n};\n\n/**\n * Color swatches extension\n * @ignore\n */\nclass Swatches extends Palette {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {}, defaults, options));\n    this.element = null;\n  }\n\n  isEnabled() {\n    return this.getLength() > 0;\n  }\n\n  onCreate(event) {\n    super.onCreate(event);\n\n    if (!this.isEnabled()) {\n      return;\n    }\n\n    this.element = $(this.options.barTemplate);\n    this.load();\n    this.colorpicker.picker.append(this.element);\n  }\n\n  load() {\n    let colorpicker = this.colorpicker,\n      swatchContainer = this.element.find('.colorpicker-swatches--inner'),\n      isAliased = (this.options.namesAsValues === true) && !Array.isArray(this.colors);\n\n    swatchContainer.empty();\n\n    $.each(this.colors, (name, value) => {\n      let $swatch = $(this.options.swatchTemplate)\n        .attr('data-name', name)\n        .attr('data-value', value)\n        .attr('title', isAliased ? `${name}: ${value}` : value)\n        .on('mousedown.colorpicker touchstart.colorpicker',\n          function (e) {\n            let $sw = $(this);\n\n            // e.preventDefault();\n\n            colorpicker.setValue(isAliased ? $sw.attr('data-name') : $sw.attr('data-value'));\n          }\n        );\n\n      $swatch.find('.colorpicker-swatch--inner')\n        .css('background-color', value);\n\n      swatchContainer.append($swatch);\n    });\n\n    swatchContainer.append($('<i class=\"colorpicker-clear\"></i>'));\n  }\n}\n\nexport default Swatches;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/extensions/Swatches.js", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Class that handles all configured sliders on mouse or touch events.\n * @ignore\n */\nclass SliderHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {*|String}\n     * @private\n     */\n    this.currentSlider = null;\n    /**\n     * @type {{left: number, top: number}}\n     * @private\n     */\n    this.mousePointer = {\n      left: 0,\n      top: 0\n    };\n\n    /**\n     * @type {Function}\n     */\n    this.onMove = $.proxy(this.defaultOnMove, this);\n  }\n\n  /**\n   * This function is called every time a slider guide is moved\n   * The scope of \"this\" is the SliderHandler object.\n   *\n   * @param {int} top\n   * @param {int} left\n   */\n  defaultOnMove(top, left) {\n    if (!this.currentSlider) {\n      return;\n    }\n\n    let slider = this.currentSlider, cp = this.colorpicker, ch = cp.colorHandler;\n\n    // Create a color object\n    let color = !ch.hasColor() ? ch.getFallbackColor() : ch.color.getClone();\n\n    // Adjust the guide position\n    slider.guideStyle.left = left + 'px';\n    slider.guideStyle.top = top + 'px';\n\n    // Adjust the color\n    if (slider.callLeft) {\n      color[slider.callLeft](left / slider.maxLeft);\n    }\n    if (slider.callTop) {\n      color[slider.callTop](top / slider.maxTop);\n    }\n\n    // Set the new color\n    cp.setValue(color);\n    cp.popupHandler.focus();\n  }\n\n  /**\n   * Binds the colorpicker sliders to the mouse/touch events\n   */\n  bind() {\n    let sliders = this.colorpicker.options.horizontal ? this.colorpicker\n      .options.slidersHorz : this.colorpicker.options.sliders;\n    let sliderClasses = [];\n\n    for (let sliderName in sliders) {\n      if (!sliders.hasOwnProperty(sliderName)) {\n        continue;\n      }\n\n      sliderClasses.push(sliders[sliderName].selector);\n    }\n\n    this.colorpicker.picker.find(sliderClasses.join(', '))\n      .on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.pressed, this));\n  }\n\n  /**\n   * Unbinds any event bound by this handler\n   */\n  unbind() {\n    $(this.colorpicker.picker).off({\n      'mousemove.colorpicker': $.proxy(this.moved, this),\n      'touchmove.colorpicker': $.proxy(this.moved, this),\n      'mouseup.colorpicker': $.proxy(this.released, this),\n      'touchend.colorpicker': $.proxy(this.released, this)\n    });\n  }\n\n  /**\n   * Function triggered when clicking in one of the color adjustment bars\n   *\n   * @private\n   * @fires Colorpicker#mousemove\n   * @param {Event} e\n   */\n  pressed(e) {\n    if (this.colorpicker.isDisabled()) {\n      return;\n    }\n    this.colorpicker.lastEvent.alias = 'pressed';\n    this.colorpicker.lastEvent.e = e;\n\n    if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n      e.pageX = e.originalEvent.touches[0].pageX;\n      e.pageY = e.originalEvent.touches[0].pageY;\n    }\n    // e.stopPropagation();\n    // e.preventDefault();\n\n    let target = $(e.target);\n\n    // detect the slider and set the limits and callbacks\n    let zone = target.closest('div');\n    let sliders = this.colorpicker.options.horizontal ? this.colorpicker\n      .options.slidersHorz : this.colorpicker.options.sliders;\n\n    if (zone.is('.colorpicker')) {\n      return;\n    }\n\n    this.currentSlider = null;\n\n    for (let sliderName in sliders) {\n      if (!sliders.hasOwnProperty(sliderName)) {\n        continue;\n      }\n\n      let slider = sliders[sliderName];\n\n      if (zone.is(slider.selector)) {\n        this.currentSlider = $.extend({}, slider, {name: sliderName});\n        break;\n      } else if (slider.childSelector !== undefined && zone.is(slider.childSelector)) {\n        this.currentSlider = $.extend({}, slider, {name: sliderName});\n        zone = zone.parent(); // zone.parents(slider.selector).first() ?\n        break;\n      }\n    }\n\n    let guide = zone.find('.colorpicker-guide').get(0);\n\n    if (this.currentSlider === null || guide === null) {\n      return;\n    }\n\n    let offset = zone.offset();\n\n    // reference to guide's style\n    this.currentSlider.guideStyle = guide.style;\n    this.currentSlider.left = e.pageX - offset.left;\n    this.currentSlider.top = e.pageY - offset.top;\n    this.mousePointer = {\n      left: e.pageX,\n      top: e.pageY\n    };\n\n    // TODO: fix moving outside the picker makes the guides to keep moving. The event needs to be bound to the window.\n    /**\n     * (window.document) Triggered on mousedown for the document object,\n     * so the color adjustment guide is moved to the clicked position.\n     *\n     * @event Colorpicker#mousemove\n     */\n    $(this.colorpicker.picker).on({\n      'mousemove.colorpicker': $.proxy(this.moved, this),\n      'touchmove.colorpicker': $.proxy(this.moved, this),\n      'mouseup.colorpicker': $.proxy(this.released, this),\n      'touchend.colorpicker': $.proxy(this.released, this)\n    }).trigger('mousemove');\n  }\n\n  /**\n   * Function triggered when dragging a guide inside one of the color adjustment bars.\n   *\n   * @private\n   * @param {Event} e\n   */\n  moved(e) {\n    this.colorpicker.lastEvent.alias = 'moved';\n    this.colorpicker.lastEvent.e = e;\n\n    if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n      e.pageX = e.originalEvent.touches[0].pageX;\n      e.pageY = e.originalEvent.touches[0].pageY;\n    }\n\n    // e.stopPropagation();\n    e.preventDefault(); // prevents scrolling on mobile\n\n    let left = Math.max(\n      0,\n      Math.min(\n        this.currentSlider.maxLeft,\n        this.currentSlider.left + ((e.pageX || this.mousePointer.left) - this.mousePointer.left)\n      )\n    );\n\n    let top = Math.max(\n      0,\n      Math.min(\n        this.currentSlider.maxTop,\n        this.currentSlider.top + ((e.pageY || this.mousePointer.top) - this.mousePointer.top)\n      )\n    );\n\n    this.onMove(top, left);\n  }\n\n  /**\n   * Function triggered when releasing the click in one of the color adjustment bars.\n   *\n   * @private\n   * @param {Event} e\n   */\n  released(e) {\n    this.colorpicker.lastEvent.alias = 'released';\n    this.colorpicker.lastEvent.e = e;\n\n    // e.stopPropagation();\n    // e.preventDefault();\n\n    $(this.colorpicker.picker).off({\n      'mousemove.colorpicker': this.moved,\n      'touchmove.colorpicker': this.moved,\n      'mouseup.colorpicker': this.released,\n      'touchend.colorpicker': this.released\n    });\n  }\n}\n\nexport default SliderHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/SliderHandler.js", "'use strict';\n\nimport $ from 'jquery';\nimport _defaults from './options';\n\n/**\n * Handles everything related to the UI of the colorpicker popup: show, hide, position,...\n * @ignore\n */\nclass PopupHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Window} root\n   */\n  constructor(colorpicker, root) {\n    /**\n     * @type {Window}\n     */\n    this.root = root;\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTarget = null;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTip = null;\n\n    /**\n     * If true, the latest click was inside the popover\n     * @type {boolean}\n     */\n    this.clicking = false;\n    /**\n     * @type {boolean}\n     */\n    this.hidding = false;\n    /**\n     * @type {boolean}\n     */\n    this.showing = false;\n  }\n\n  /**\n   * @private\n   * @returns {jQuery|false}\n   */\n  get input() {\n    return this.colorpicker.inputHandler.input;\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get hasInput() {\n    return this.colorpicker.inputHandler.hasInput();\n  }\n\n  /**\n   * @private\n   * @returns {jQuery|false}\n   */\n  get addon() {\n    return this.colorpicker.addonHandler.addon;\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get hasAddon() {\n    return this.colorpicker.addonHandler.hasAddon();\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get isPopover() {\n    return !this.colorpicker.options.inline && !!this.popoverTip;\n  }\n\n  /**\n   * Binds the different colorpicker elements to the focus/mouse/touch events so it reacts in order to show or\n   * hide the colorpicker popup accordingly. It also adds the proper classes.\n   */\n  bind() {\n    let cp = this.colorpicker;\n\n    if (cp.options.inline) {\n      cp.picker.addClass('colorpicker-inline colorpicker-visible');\n      return; // no need to bind show/hide events for inline elements\n    }\n\n    cp.picker.addClass('colorpicker-popup colorpicker-hidden');\n\n    // there is no input or addon\n    if (!this.hasInput && !this.hasAddon) {\n      return;\n    }\n\n    // create Bootstrap 4 popover\n    if (cp.options.popover) {\n      this.createPopover();\n    }\n\n    // bind addon show/hide events\n    if (this.hasAddon) {\n      // enable focus on addons\n      if (!this.addon.attr('tabindex')) {\n        this.addon.attr('tabindex', 0);\n      }\n\n      this.addon.on({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.toggle, this)\n      });\n\n      this.addon.on({\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n\n      this.addon.on({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    // bind input show/hide events\n    if (this.hasInput && !this.hasAddon) {\n      this.input.on({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.show, this),\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n\n      this.input.on({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    // reposition popup on window resize\n    $(this.root).on('resize.colorpicker', $.proxy(this.reposition, this));\n  }\n\n  /**\n   * Unbinds any event bound by this handler\n   */\n  unbind() {\n    if (this.hasInput) {\n      this.input.off({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.show, this),\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n      this.input.off({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    if (this.hasAddon) {\n      this.addon.off({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.toggle, this)\n      });\n      this.addon.off({\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n      this.addon.off({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('dispose');\n    }\n\n    $(this.root).off('resize.colorpicker', $.proxy(this.reposition, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n  }\n\n  isClickingInside(e) {\n    if (!e) {\n      return false;\n    }\n\n    return (\n      this.isOrIsInside(this.popoverTip, e.currentTarget) ||\n      this.isOrIsInside(this.popoverTip, e.target) ||\n      this.isOrIsInside(this.colorpicker.picker, e.currentTarget) ||\n      this.isOrIsInside(this.colorpicker.picker, e.target)\n    );\n  }\n\n  isOrIsInside(container, element) {\n    if (!container || !element) {\n      return false;\n    }\n\n    element = $(element);\n\n    return (\n      element.is(container) ||\n      container.find(element).length > 0\n    );\n  }\n\n  onClickingInside(e) {\n    this.clicking = this.isClickingInside(e);\n  }\n\n  createPopover() {\n    let cp = this.colorpicker;\n\n    this.popoverTarget = this.hasAddon ? this.addon : this.input;\n\n    cp.picker.addClass('colorpicker-bs-popover-content');\n\n    this.popoverTarget.popover(\n      $.extend(\n        true,\n        {},\n        _defaults.popover,\n        cp.options.popover,\n        {trigger: 'manual', content: cp.picker, html: true}\n      )\n    );\n\n    this.popoverTip = $(this.popoverTarget.popover('getTipElement').data('bs.popover').tip);\n    this.popoverTip.addClass('colorpicker-bs-popover');\n\n    this.popoverTarget.on('shown.bs.popover', $.proxy(this.fireShow, this));\n    this.popoverTarget.on('hidden.bs.popover', $.proxy(this.fireHide, this));\n  }\n\n  /**\n   * If the widget is not inside a container or inline, rearranges its position relative to its element offset.\n   *\n   * @param {Event} [e]\n   * @private\n   */\n  reposition(e) {\n    if (this.popoverTarget && this.isVisible()) {\n      this.popoverTarget.popover('update');\n    }\n  }\n\n  /**\n   * Toggles the colorpicker between visible or hidden\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  toggle(e) {\n    if (this.isVisible()) {\n      this.hide(e);\n    } else {\n      this.show(e);\n    }\n  }\n\n  /**\n   * Shows the colorpicker widget if hidden.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @param {Event} [e]\n   */\n  show(e) {\n    if (this.isVisible() || this.showing || this.hidding) {\n      return;\n    }\n\n    this.showing = true;\n    this.hidding = false;\n    this.clicking = false;\n\n    let cp = this.colorpicker;\n\n    cp.lastEvent.alias = 'show';\n    cp.lastEvent.e = e;\n\n    // Prevent showing browser native HTML5 colorpicker\n    if (\n      (e && (!this.hasInput || this.input.attr('type') === 'color')) &&\n      (e && e.preventDefault)\n    ) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n\n    // If it's a popover, add event to the document to hide the picker when clicking outside of it\n    if (this.isPopover) {\n      $(this.root).on('resize.colorpicker', $.proxy(this.reposition, this));\n    }\n\n    // add visible class before popover is shown\n    cp.picker.addClass('colorpicker-visible').removeClass('colorpicker-hidden');\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('show');\n    } else {\n      this.fireShow();\n    }\n  }\n\n  fireShow() {\n    this.hidding = false;\n    this.showing = false;\n\n    if (this.isPopover) {\n      // Add event to hide on outside click\n      $(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n      $(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n    }\n\n    /**\n     * (Colorpicker) When show() is called and the widget can be shown.\n     *\n     * @event Colorpicker#colorpickerShow\n     */\n    this.colorpicker.trigger('colorpickerShow');\n  }\n\n  /**\n   * Hides the colorpicker widget.\n   * Hide is prevented when it is triggered by an event whose target element has been clicked/touched.\n   *\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  hide(e) {\n    if (this.isHidden() || this.showing || this.hidding) {\n      return;\n    }\n\n    let cp = this.colorpicker, clicking = (this.clicking || this.isClickingInside(e));\n\n    this.hidding = true;\n    this.showing = false;\n    this.clicking = false;\n\n    cp.lastEvent.alias = 'hide';\n    cp.lastEvent.e = e;\n\n    // TODO: fix having to click twice outside when losing focus and last 2 clicks where inside the colorpicker\n\n    // Prevent hide if triggered by an event and an element inside the colorpicker has been clicked/touched\n    if (clicking) {\n      this.hidding = false;\n      return;\n    }\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('hide');\n    } else {\n      this.fireHide();\n    }\n  }\n\n  fireHide() {\n    this.hidding = false;\n    this.showing = false;\n\n    let cp = this.colorpicker;\n\n    // add hidden class after popover is hidden\n    cp.picker.addClass('colorpicker-hidden').removeClass('colorpicker-visible');\n\n    // Unbind window and document events, since there is no need to keep them while the popup is hidden\n    $(this.root).off('resize.colorpicker', $.proxy(this.reposition, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n\n    /**\n     * (Colorpicker) When hide() is called and the widget can be hidden.\n     *\n     * @event Colorpicker#colorpickerHide\n     */\n    cp.trigger('colorpickerHide');\n  }\n\n  focus() {\n    if (this.hasAddon) {\n      return this.addon.focus();\n    }\n    if (this.hasInput) {\n      return this.input.focus();\n    }\n    return false;\n  }\n\n  /**\n   * Returns true if the colorpicker element has the colorpicker-visible class and not the colorpicker-hidden one.\n   * False otherwise.\n   *\n   * @returns {boolean}\n   */\n  isVisible() {\n    return this.colorpicker.picker.hasClass('colorpicker-visible') &&\n      !this.colorpicker.picker.hasClass('colorpicker-hidden');\n  }\n\n  /**\n   * Returns true if the colorpicker element has the colorpicker-hidden class and not the colorpicker-visible one.\n   * False otherwise.\n   *\n   * @returns {boolean}\n   */\n  isHidden() {\n    return this.colorpicker.picker.hasClass('colorpicker-hidden') &&\n      !this.colorpicker.picker.hasClass('colorpicker-visible');\n  }\n}\n\nexport default PopupHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/PopupHandler.js", "'use strict';\n\nimport $ from 'jquery';\nimport ColorItem from './ColorItem';\n\n/**\n * Handles everything related to the colorpicker input\n * @ignore\n */\nclass InputHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery|false}\n     */\n    this.input = this.colorpicker.element.is('input') ? this.colorpicker.element : (this.colorpicker.options.input ?\n      this.colorpicker.element.find(this.colorpicker.options.input) : false);\n\n    if (this.input && (this.input.length === 0)) {\n      this.input = false;\n    }\n\n    this._initValue();\n  }\n\n  bind() {\n    if (!this.hasInput()) {\n      return;\n    }\n    this.input.on({\n      'keyup.colorpicker': $.proxy(this.onkeyup, this)\n    });\n    this.input.on({\n      'change.colorpicker': $.proxy(this.onchange, this)\n    });\n  }\n\n  unbind() {\n    if (!this.hasInput()) {\n      return;\n    }\n    this.input.off('.colorpicker');\n  }\n\n  _initValue() {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    let val = '';\n\n    [\n      // candidates:\n      this.input.val(),\n      this.input.data('color'),\n      this.input.attr('data-color')\n    ].map((item) => {\n      if (item && (val === '')) {\n        val = item;\n      }\n    });\n\n    if (val instanceof ColorItem) {\n      val = this.getFormattedColor(val.string(this.colorpicker.format));\n    } else if (!(typeof val === 'string' || val instanceof String)) {\n      val = '';\n    }\n\n    this.input.prop('value', val);\n  }\n\n  /**\n   * Returns the color string from the input value.\n   * If there is no input the return value is false.\n   *\n   * @returns {String|boolean}\n   */\n  getValue() {\n    if (!this.hasInput()) {\n      return false;\n    }\n\n    return this.input.val();\n  }\n\n  /**\n   * If the input element is present, it updates the value with the current color object color string.\n   * If the value is changed, this method fires a \"change\" event on the input element.\n   *\n   * @param {String} val\n   *\n   * @fires Colorpicker#change\n   */\n  setValue(val) {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    let inputVal = this.input.prop('value');\n\n    val = val ? val : '';\n\n    if (val === (inputVal ? inputVal : '')) {\n      // No need to set value or trigger any event if nothing changed\n      return;\n    }\n\n    this.input.prop('value', val);\n\n    /**\n     * (Input) Triggered on the input element when a new color is selected.\n     *\n     * @event Colorpicker#change\n     */\n    this.input.trigger({\n      type: 'change',\n      colorpicker: this.colorpicker,\n      color: this.colorpicker.color,\n      value: val\n    });\n  }\n\n  /**\n   * Returns the formatted color string, with the formatting options applied\n   * (e.g. useHashPrefix)\n   *\n   * @param {String|null} val\n   *\n   * @returns {String}\n   */\n  getFormattedColor(val = null) {\n    val = val ? val : this.colorpicker.colorHandler.getColorString();\n\n    if (!val) {\n      return '';\n    }\n\n    val = this.colorpicker.colorHandler.resolveColorDelegate(val, false);\n\n    if (this.colorpicker.options.useHashPrefix === false) {\n      val = val.replace(/^#/g, '');\n    }\n\n    return val;\n  }\n\n  /**\n   * Returns true if the widget has an associated input element, false otherwise\n   * @returns {boolean}\n   */\n  hasInput() {\n    return (this.input !== false);\n  }\n\n  /**\n   * Returns true if the input exists and is disabled\n   * @returns {boolean}\n   */\n  isEnabled() {\n    return this.hasInput() && !this.isDisabled();\n  }\n\n  /**\n   * Returns true if the input exists and is disabled\n   * @returns {boolean}\n   */\n  isDisabled() {\n    return this.hasInput() && (this.input.prop('disabled') === true);\n  }\n\n  /**\n   * Disables the input if any\n   *\n   * @fires Colorpicker#colorpickerDisable\n   * @returns {boolean}\n   */\n  disable() {\n    if (this.hasInput()) {\n      this.input.prop('disabled', true);\n    }\n  }\n\n  /**\n   * Enables the input if any\n   *\n   * @fires Colorpicker#colorpickerEnable\n   * @returns {boolean}\n   */\n  enable() {\n    if (this.hasInput()) {\n      this.input.prop('disabled', false);\n    }\n  }\n\n  /**\n   * Calls setValue with the current internal color value\n   *\n   * @fires Colorpicker#change\n   */\n  update() {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    if (\n      (this.colorpicker.options.autoInputFallback === false) &&\n      this.colorpicker.colorHandler.isInvalidColor()\n    ) {\n      // prevent update if color is invalid, autoInputFallback is disabled and the last event is keyup.\n      return;\n    }\n\n    this.setValue(this.getFormattedColor());\n  }\n\n  /**\n   * Function triggered when the input has changed, so the colorpicker gets updated.\n   *\n   * @private\n   * @param {Event} e\n   * @returns {boolean}\n   */\n  onchange(e) {\n    this.colorpicker.lastEvent.alias = 'input.change';\n    this.colorpicker.lastEvent.e = e;\n\n    let val = this.getValue();\n\n    if (val !== e.value) {\n      this.colorpicker.setValue(val);\n    }\n  }\n\n  /**\n   * Function triggered after a keyboard key has been released.\n   *\n   * @private\n   * @param {Event} e\n   * @returns {boolean}\n   */\n  onkeyup(e) {\n    this.colorpicker.lastEvent.alias = 'input.keyup';\n    this.colorpicker.lastEvent.e = e;\n\n    let val = this.getValue();\n\n    if (val !== e.value) {\n      this.colorpicker.setValue(val);\n    }\n  }\n}\n\nexport default InputHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/InputHandler.js", "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (typeof obj === 'undefined') {\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color/index.js\n// module id = 16\n// module chunks = 0 1", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\n\nvar reverseNames = {};\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (colorNames.hasOwnProperty(name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar keyword = /(\\D+)/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha + hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\n\t\tif (!rgb) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d*\\.)?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = (parseFloat(match[1]) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d*[\\.]?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = num.toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color-string/index.js\n// module id = 17\n// module chunks = 0 1", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/simple-swizzle/index.js\n// module id = 18\n// module chunks = 0 1", "'use strict';\n\nmodule.exports = function isArrayish(obj) {\n\tif (!obj) {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && obj.splice instanceof Function);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/is-arrayish/index.js\n// module id = 19\n// module chunks = 0 1", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color-convert/index.js\n// module id = 20\n// module chunks = 0 1", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/color-convert/route.js\n// module id = 21\n// module chunks = 0 1", "'use strict';\n\nimport $ from 'jquery';\nimport ColorItem from './ColorItem';\n\n/**\n * Handles everything related to the colorpicker color\n * @ignore\n */\nclass ColorHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n  }\n\n  /**\n   * @returns {*|String|ColorItem}\n   */\n  get fallback() {\n    return this.colorpicker.options.fallbackColor ?\n      this.colorpicker.options.fallbackColor : (this.hasColor() ? this.color : null);\n  }\n\n  /**\n   * @returns {String|null}\n   */\n  get format() {\n    if (this.colorpicker.options.format) {\n      return this.colorpicker.options.format;\n    }\n\n    if (this.hasColor() && this.color.hasTransparency() && this.color.format.match(/^hex/)) {\n      return this.isAlphaEnabled() ? 'rgba' : 'hex';\n    }\n\n    if (this.hasColor()) {\n      return this.color.format;\n    }\n\n    return 'rgb';\n  }\n\n  /**\n   * Internal color getter\n   *\n   * @type {ColorItem|null}\n   */\n  get color() {\n    return this.colorpicker.element.data('color');\n  }\n\n  /**\n   * Internal color setter\n   *\n   * @ignore\n   * @param {ColorItem|null} value\n   */\n  set color(value) {\n    this.colorpicker.element.data('color', value);\n\n    if ((value instanceof ColorItem) && (this.colorpicker.options.format === 'auto')) {\n      // If format is 'auto', use the first parsed one from now on\n      this.colorpicker.options.format = this.color.format;\n    }\n  }\n\n  bind() {\n    // if the color option is set\n    if (this.colorpicker.options.color) {\n      this.color = this.createColor(this.colorpicker.options.color);\n      return;\n    }\n\n    // if element[color] is empty and the input has a value\n    if (!this.color && !!this.colorpicker.inputHandler.getValue()) {\n      this.color = this.createColor(\n        this.colorpicker.inputHandler.getValue(), this.colorpicker.options.autoInputFallback\n      );\n    }\n  }\n\n  unbind() {\n    this.colorpicker.element.removeData('color');\n  }\n\n  /**\n   * Returns the color string from the input value or the 'data-color' attribute of the input or element.\n   * If empty, it returns the defaultValue parameter.\n   *\n   * @returns {String|*}\n   */\n  getColorString() {\n    if (!this.hasColor()) {\n      return '';\n    }\n\n    return this.color.string(this.format);\n  }\n\n  /**\n   * Sets the color value\n   *\n   * @param {String|ColorItem} val\n   */\n  setColorString(val) {\n    let color = val ? this.createColor(val) : null;\n\n    this.color = color ? color : null;\n  }\n\n  /**\n   * Creates a new color using the widget instance options (fallbackColor, format).\n   *\n   * @fires Colorpicker#colorpickerInvalid\n   * @param {*} val\n   * @param {boolean} fallbackOnInvalid\n   * @returns {ColorItem}\n   */\n  createColor(val, fallbackOnInvalid = true) {\n    let color = new ColorItem(this.resolveColorDelegate(val), this.format);\n\n    if (!color.isValid()) {\n      if (fallbackOnInvalid) {\n        color = this.getFallbackColor();\n      }\n\n      /**\n       * (Colorpicker) Fired when the color is invalid and the fallback color is going to be used.\n       *\n       * @event Colorpicker#colorpickerInvalid\n       */\n      this.colorpicker.trigger('colorpickerInvalid', color, val);\n    }\n\n    if (!this.isAlphaEnabled()) {\n      // Alpha is disabled\n      color.alpha = 1;\n    }\n\n    return color;\n  }\n\n  getFallbackColor() {\n    if (this.fallback && (this.fallback === this.color)) {\n      return this.color;\n    }\n\n    let fallback = this.resolveColorDelegate(this.fallback);\n    let color = new ColorItem(fallback, this.format);\n\n    if (!color.isValid()) {\n      console.warn('The fallback color is invalid. Falling back to the previous color or black if any.');\n      return this.color ? this.color : new ColorItem('#000000', this.format);\n    }\n\n    return color;\n  }\n\n  /**\n   * @returns {ColorItem}\n   */\n  assureColor() {\n    if (!this.hasColor()) {\n      this.color = this.getFallbackColor();\n    }\n\n    return this.color;\n  }\n\n  /**\n   * Delegates the color resolution to the colorpicker extensions.\n   *\n   * @param {String|*} color\n   * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n   * @returns {ColorItem|String|*|null}\n   */\n  resolveColorDelegate(color, realColor = true) {\n    let extResolvedColor = false;\n\n    $.each(this.colorpicker.extensions, function (name, ext) {\n      if (extResolvedColor !== false) {\n        // skip if resolved\n        return;\n      }\n      extResolvedColor = ext.resolveColor(color, realColor);\n    });\n\n    return extResolvedColor ? extResolvedColor : color;\n  }\n\n  /**\n   * Checks if there is a color object, that it is valid and it is not a fallback\n   * @returns {boolean}\n   */\n  isInvalidColor() {\n    return !this.hasColor() || !this.color.isValid();\n  }\n\n  /**\n   * Returns true if the useAlpha option is exactly true, false otherwise\n   * @returns {boolean}\n   */\n  isAlphaEnabled() {\n    return (this.colorpicker.options.useAlpha !== false);\n  }\n\n  /**\n   * Returns true if the current color object is an instance of Color, false otherwise.\n   * @returns {boolean}\n   */\n  hasColor() {\n    return this.color instanceof ColorItem;\n  }\n}\n\nexport default ColorHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/ColorHandler.js", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Handles everything related to the colorpicker UI\n * @ignore\n */\nclass PickerHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.picker = null;\n  }\n\n  get options() {\n    return this.colorpicker.options;\n  }\n\n  get color() {\n    return this.colorpicker.colorHandler.color;\n  }\n\n  bind() {\n    /**\n     * @type {jQuery|HTMLElement}\n     */\n    let picker = this.picker = $(this.options.template);\n\n    if (this.options.customClass) {\n      picker.addClass(this.options.customClass);\n    }\n\n    if (this.options.horizontal) {\n      picker.addClass('colorpicker-horizontal');\n    }\n\n    if (this._supportsAlphaBar()) {\n      this.options.useAlpha = true;\n      picker.addClass('colorpicker-with-alpha');\n    } else {\n      this.options.useAlpha = false;\n    }\n  }\n\n  attach() {\n    // Inject the colorpicker element into the DOM\n    let pickerParent = this.colorpicker.container ? this.colorpicker.container : null;\n\n    if (pickerParent) {\n      this.picker.appendTo(pickerParent);\n    }\n  }\n\n  unbind() {\n    this.picker.remove();\n  }\n\n  _supportsAlphaBar() {\n    return (\n      (this.options.useAlpha || (this.colorpicker.colorHandler.hasColor() && this.color.hasTransparency())) &&\n      (this.options.useAlpha !== false) &&\n      (!this.options.format || (this.options.format && !this.options.format.match(/^hex([36])?$/i)))\n    );\n  }\n\n  /**\n   * Changes the color adjustment bars using the current color object information.\n   */\n  update() {\n    if (!this.colorpicker.colorHandler.hasColor()) {\n      return;\n    }\n\n    let vertical = (this.options.horizontal !== true),\n      slider = vertical ? this.options.sliders : this.options.slidersHorz;\n\n    let saturationGuide = this.picker.find('.colorpicker-saturation .colorpicker-guide'),\n      hueGuide = this.picker.find('.colorpicker-hue .colorpicker-guide'),\n      alphaGuide = this.picker.find('.colorpicker-alpha .colorpicker-guide');\n\n    let hsva = this.color.toHsvaRatio();\n\n    // Set guides position\n    if (hueGuide.length) {\n      hueGuide.css(vertical ? 'top' : 'left', (vertical ? slider.hue.maxTop : slider.hue.maxLeft) * (1 - hsva.h));\n    }\n    if (alphaGuide.length) {\n      alphaGuide.css(vertical ? 'top' : 'left', (vertical ? slider.alpha.maxTop : slider.alpha.maxLeft) * (1 - hsva.a));\n    }\n    if (saturationGuide.length) {\n      saturationGuide.css({\n        'top': slider.saturation.maxTop - hsva.v * slider.saturation.maxTop,\n        'left': hsva.s * slider.saturation.maxLeft\n      });\n    }\n\n    // Set saturation hue background\n    this.picker.find('.colorpicker-saturation')\n      .css('backgroundColor', this.color.getCloneHueOnly().toHexString()); // we only need hue\n\n    // Set alpha color gradient\n    let hexColor = this.color.toHexString();\n    let alphaBg = '';\n\n    if (this.options.horizontal) {\n      alphaBg = `linear-gradient(to right, ${hexColor} 0%, transparent 100%)`;\n    } else {\n      alphaBg = `linear-gradient(to bottom, ${hexColor} 0%, transparent 100%)`;\n    }\n\n    this.picker.find('.colorpicker-alpha-color').css('background', alphaBg);\n  }\n}\n\nexport default PickerHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/PickerHandler.js", "'use strict';\n\n/**\n * Handles everything related to the colorpicker addon\n * @ignore\n */\nclass AddonHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.addon = null;\n  }\n\n  hasAddon() {\n    return !!this.addon;\n  }\n\n  bind() {\n    /**\n     * @type {*|jQuery}\n     */\n    this.addon = this.colorpicker.options.addon ?\n      this.colorpicker.element.find(this.colorpicker.options.addon) : null;\n\n    if (this.addon && (this.addon.length === 0)) {\n      // not found\n      this.addon = null;\n    }\n  }\n\n  unbind() {\n    if (this.hasAddon()) {\n      this.addon.off('.colorpicker');\n    }\n  }\n\n  /**\n   * If the addon element is present, its background color is updated\n   */\n  update() {\n    if (!this.colorpicker.colorHandler.hasColor() || !this.hasAddon()) {\n      return;\n    }\n\n    let colorStr = this.colorpicker.colorHandler.getColorString();\n    let styles = {'background': colorStr};\n\n    let icn = this.addon.find('i').eq(0);\n\n    if (icn.length > 0) {\n      icn.css(styles);\n    } else {\n      this.addon.css(styles);\n    }\n  }\n}\n\nexport default AddonHandler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/AddonHandler.js"], "sourceRoot": ""}