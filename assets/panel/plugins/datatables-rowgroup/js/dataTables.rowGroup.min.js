/*!
 RowGroup 1.1.0
 ©2017-2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(d){return c(d,window,document)}):"object"===typeof exports?module.exports=function(d,h){d||(d=window);if(!h||!h.fn.dataTable)h=require("datatables.net")(d,h).$;return c(h,d,d.document)}:c(jQuery,window,document)})(function(c,d,h,k){var i=c.fn.dataTable,e=function(a,b){if(!i.versionCheck||!i.versionCheck("1.10.8"))throw"RowGroup requires DataTables 1.10.8 or newer";this.c=c.extend(!0,{},i.defaults.rowGroup,
e.defaults,b);this.s={dt:new i.Api(a)};this.dom={};var m=this.s.dt.settings()[0],f=m.rowGroup;if(f)return f;m.rowGroup=this;this._constructor()};c.extend(e.prototype,{dataSrc:function(a){if(a===k)return this.c.dataSrc;var b=this.s.dt;this.c.dataSrc=a;c(b.table().node()).triggerHandler("rowgroup-datasrc.dt",[b,a]);return this},disable:function(){this.c.enable=!1;return this},enable:function(a){if(!1===a)return this.disable();this.c.enable=!0;return this},_constructor:function(){var a=this,b=this.s.dt;
b.on("draw.dtrg",function(){a.c.enable&&a._draw()});b.on("column-visibility.dt.dtrg responsive-resize.dt.dtrg",function(){a._adjustColspan()});b.on("destroy",function(){b.off(".dtrg")});b.on("responsive-resize.dt",function(){a._adjustColspan()})},_adjustColspan:function(){c("tr."+this.c.className,this.s.dt.table().body()).find("td").attr("colspan",this._colspan())},_colspan:function(){return this.s.dt.columns().visible().reduce(function(a,b){return a+b},0)},_draw:function(){var a=this._group(0,this.s.dt.rows({page:"current"}).indexes());
this._groupDisplay(0,a)},_group:function(a,b){for(var m=c.isArray(this.c.dataSrc)?this.c.dataSrc:[this.c.dataSrc],f=i.ext.oApi._fnGetObjectDataFn(m[a]),d=this.s.dt,j,e,l=[],g=0,h=b.length;g<h;g++){var n=b[g];j=d.row(n).data();j=f(j);if(null===j||j===k)j=that.c.emptyDataGroup;if(e===k||j!==e)l.push({dataPoint:j,rows:[]}),e=j;l[l.length-1].rows.push(n)}if(m[a+1]!==k){g=0;for(h=l.length;g<h;g++)l[g].children=this._group(a+1,l[g].rows)}return l},_groupDisplay:function(a,b){for(var c=this.s.dt,f,e=0,h=
b.length;e<h;e++){var d=b[e],i=d.dataPoint,g=d.rows;this.c.startRender&&(f=this.c.startRender.call(this,c.rows(g),i,a),(f=this._rowWrap(f,this.c.startClassName,a))&&f.insertBefore(c.row(g[0]).node()));this.c.endRender&&(f=this.c.endRender.call(this,c.rows(g),i,a),(f=this._rowWrap(f,this.c.endClassName,a))&&f.insertAfter(c.row(g[g.length-1]).node()));d.children&&this._groupDisplay(a+1,d.children)}},_rowWrap:function(a,b,e){if(null===a||""===a)a=this.c.emptyDataGroup;return a===k?null:("object"===typeof a&&
a.nodeName&&"tr"===a.nodeName.toLowerCase()?c(a):a instanceof c&&a.length&&"tr"===a[0].nodeName.toLowerCase()?a:c("<tr/>").append(c("<td/>").attr("colspan",this._colspan()).append(a))).addClass(this.c.className).addClass(b).addClass("dtrg-level-"+e)}});e.defaults={className:"dtrg-group",dataSrc:0,emptyDataGroup:"No group",enable:!0,endClassName:"dtrg-end",endRender:null,startClassName:"dtrg-start",startRender:function(a,b){return b}};e.version="1.1.0";c.fn.dataTable.RowGroup=e;c.fn.DataTable.RowGroup=
e;i.Api.register("rowGroup()",function(){return this});i.Api.register("rowGroup().disable()",function(){return this.iterator("table",function(a){a.rowGroup&&a.rowGroup.enable(!1)})});i.Api.register("rowGroup().enable()",function(a){return this.iterator("table",function(b){b.rowGroup&&b.rowGroup.enable(a===k?!0:a)})});i.Api.register("rowGroup().dataSrc()",function(a){return a===k?this.context[0].rowGroup.dataSrc():this.iterator("table",function(b){b.rowGroup&&b.rowGroup.dataSrc(a)})});c(h).on("preInit.dt.dtrg",
function(a,b){if("dt"===a.namespace){var d=b.oInit.rowGroup,f=i.defaults.rowGroup;if(d||f)f=c.extend({},f,d),!1!==d&&new e(b,f)}});return e});
