{"_args": [["jquery-mousewheel@3.1.13", "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE"]], "_from": "jquery-mousewheel@3.1.13", "_id": "jquery-mousewheel@3.1.13", "_inBundle": false, "_integrity": "sha1-BvAzXxbjU6aV5yBr9QUDy1I6buU=", "_location": "/jquery-mousewheel", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "jquery-mousewheel@3.1.13", "name": "jquery-mousewheel", "escapedName": "jquery-mousewheel", "rawSpec": "3.1.13", "saveSpec": null, "fetchSpec": "3.1.13"}, "_requiredBy": ["/", "/jquery-mapael"], "_resolved": "https://registry.npmjs.org/jquery-mousewheel/-/jquery-mousewheel-3.1.13.tgz", "_spec": "3.1.13", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "jQuery Foundation and other contributors", "url": "https://github.com/jquery/jquery-mousewheel/blob/master/AUTHORS.txt"}, "bugs": {"url": "https://github.com/jquery/jquery-mousewheel/issues"}, "description": "A jQuery plugin that adds cross-browser mouse wheel support.", "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-connect": "~0.5.0", "grunt-contrib-jshint": "~0.7.1", "grunt-contrib-uglify": "~0.2.7"}, "directories": {"test": "test"}, "files": ["ChangeLog.md", "jquery.mousewheel.js", "README.md", "LICENSE.txt"], "homepage": "https://github.com/jquery/jquery-mousewheel", "jam": {"dependencies": {"jquery": ">=1.2.2"}}, "keywords": ["j<PERSON>y", "mouse", "wheel", "event", "mousewheel", "jquery-plugin", "browser"], "licenses": [{"type": "MIT", "url": "https://github.com/jquery/jquery-mousewheel/blob/master/LICENSE.txt"}], "main": "./jquery.mousewheel.js", "name": "jquery-mousewheel", "repository": {"type": "git", "url": "git+https://github.com/jquery/jquery-mousewheel.git"}, "version": "3.1.13"}