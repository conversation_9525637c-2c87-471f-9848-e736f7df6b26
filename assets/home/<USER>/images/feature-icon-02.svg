<svg width="128" height="72" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient x1="49.078%" y1="100%" x2="0%" y2="7.585%" id="b">
      <stop stop-color="#006DCC" offset="0%"/>
      <stop stop-color="#338BF8" offset="39.97%"/>
      <stop stop-color="#23FAD2" offset="100%"/>
    </linearGradient>
    <circle id="a" cx="36" cy="36" r="36"/>
    <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="f">
      <stop stop-color="#EEE" offset="0%"/>
      <stop stop-color="#C7E5FF" offset="100%"/>
    </linearGradient>
    <path id="e" d="M10 18l-32 16 32 16 32-16z"/>
    <filter x="-40.6%" y="-68.8%" width="181.2%" height="262.5%" filterUnits="objectBoundingBox" id="d">
      <feOffset dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.207843137 0 0 0 0 0.145098039 0 0 0 0 0.82745098 0 0 0 0.16 0" in="shadowBlurOuter1"/>
    </filter>
    <linearGradient x1="0%" y1="0%" x2="50%" y2="100%" id="g">
      <stop stop-color="#EEE" offset="0%"/>
      <stop stop-color="#C7E5FF" stop-opacity="0" offset="100%"/>
    </linearGradient>
    <path id="i" d="M36 0L0 18l36 18 36-18z"/>
    <filter x="-36.1%" y="-61.1%" width="172.2%" height="244.4%" filterUnits="objectBoundingBox" id="h">
      <feOffset dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.207843137 0 0 0 0 0.145098039 0 0 0 0 0.82745098 0 0 0 0.16 0" in="shadowBlurOuter1"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g transform="translate(28)">
      <mask id="c" fill="#fff">
        <use xlink:href="#a"/>
      </mask>
      <use fill="url(#b)" xlink:href="#a"/>
      <g opacity=".24" mask="url(#c)">
        <use fill="#000" filter="url(#d)" xlink:href="#e"/>
        <use fill="url(#f)" xlink:href="#e"/>
      </g>
    </g>
    <path fill="url(#g)" d="M33 28L11 39l22 11 22-11z" transform="translate(43 12)"/>
    <g transform="translate(43 12)">
      <use fill="#000" filter="url(#h)" xlink:href="#i"/>
      <use fill="url(#f)" xlink:href="#i"/>
    </g>
  </g>
</svg>
