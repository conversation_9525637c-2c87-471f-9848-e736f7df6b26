<svg width="128" height="72" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient x1="49.078%" y1="100%" x2="0%" y2="7.585%" id="b">
      <stop stop-color="#006DCC" offset="0%"/>
      <stop stop-color="#338BF8" offset="39.97%"/>
      <stop stop-color="#23FAD2" offset="100%"/>
    </linearGradient>
    <circle id="a" cx="36" cy="36" r="36"/>
    <linearGradient x1="0%" y1="0%" x2="50%" y2="100%" id="e">
      <stop stop-color="#EEE" offset="0%"/>
      <stop stop-color="#C7E5FF" stop-opacity="0" offset="100%"/>
    </linearGradient>
    <rect id="d" width="50" height="32" rx="2"/>
    <filter x="-28%" y="-31.2%" width="156%" height="187.5%" filterUnits="objectBoundingBox" id="c">
      <feOffset dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.207843137 0 0 0 0 0.145098039 0 0 0 0 0.82745098 0 0 0 0.16 0" in="shadowBlurOuter1"/>
    </filter>
    <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="h">
      <stop stop-color="#EEE" offset="0%"/>
      <stop stop-color="#C7E5FF" offset="100%"/>
    </linearGradient>
    <rect id="g" x="11" y="10" width="50" height="32" rx="2"/>
    <filter x="-28%" y="-31.2%" width="156%" height="187.5%" filterUnits="objectBoundingBox" id="f">
      <feOffset dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.207843137 0 0 0 0 0.145098039 0 0 0 0 0.82745098 0 0 0 0.16 0" in="shadowBlurOuter1"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <use fill="url(#b)" xlink:href="#a" transform="translate(28)"/>
    <g transform="translate(41 14)">
      <use fill="#000" filter="url(#c)" xlink:href="#d"/>
      <use fill="url(#e)" xlink:href="#d"/>
    </g>
    <g transform="translate(41 14)">
      <use fill="#000" filter="url(#f)" xlink:href="#g"/>
      <use fill="url(#h)" xlink:href="#g"/>
    </g>
  </g>
</svg>
