# FRP 用户管理功能说明

本功能为 Sakura Panel 添加了与您的 FRP 服务器 API 集成的用户管理功能，可以直接在面板中管理 FRP 服务器的用户配置。

## 功能特性

- 🔗 **API 集成**: 与您的 frps-api 插件无缝集成
- 👥 **用户管理**: 创建、编辑、删除 FRP 用户
- 📊 **状态监控**: 实时查看服务器和 FRPS 服务状态
- 🔧 **配置管理**: 管理用户的端口、域名、子域名等配置
- 🔄 **热重载**: 支持配置文件热重载
- 🛡️ **权限控制**: 只有管理员可以访问 FRP 用户管理功能

## 安装配置

### 1. 确保 FRP 服务器已配置

首先确保您的 FRP 服务器已经安装并运行了 frps-api 插件。参考您提供的 API 文档进行配置。

### 2. 配置面板连接

编辑 `frp-config.php` 文件，配置您的 FRP API 服务器信息：

```php
$frp_config = [
    // FRP API 服务器地址
    'server_url' => 'http://your-frp-server.com:7300',
    
    // FRP API 访问令牌（与 frps-api 插件中的 API_TOKEN 一致）
    'api_token' => 'your-secure-token-here',
    
    // 其他配置...
];
```

### 3. 测试连接

访问 `http://your-panel-domain.com/test-frp-api.php` 测试与 FRP 服务器的连接是否正常。

### 4. 访问管理界面

以管理员身份登录面板，在侧边栏中找到 "FRP用户管理" 菜单项。

## 使用说明

### 服务器状态监控

在 FRP 用户管理页面顶部，您可以看到：
- **服务器状态**: CPU、内存、磁盘使用情况
- **FRPS 服务状态**: 进程状态、连接数等信息

### 用户管理操作

#### 创建用户
1. 点击 "创建用户" 按钮
2. 填写用户信息：
   - 用户名
   - Token（用户访问令牌）
   - 备注
   - 端口列表（多个端口用逗号分隔）
   - 域名列表（多个域名用逗号分隔）
   - 子域名列表（多个子域名用逗号分隔）
   - 启用状态
3. 点击 "创建" 完成

#### 查看用户详情
- 在用户列表中点击 "查看" 按钮
- 右侧会显示用户的详细信息

#### 编辑用户
- 在用户详情中点击 "编辑" 按钮
- 目前支持修改用户备注

#### 启用/禁用用户
- 在用户详情中点击 "启用用户" 或 "禁用用户" 按钮

#### 删除用户
- 在用户列表中点击 "删除" 按钮
- 确认后删除用户

#### 重载配置
- 点击 "重载配置" 按钮
- 手动触发 FRP 服务器配置重载

## API 接口

面板提供了以下 API 接口与 FRP 服务器通信：

### 基础 URL
```
http://your-panel-domain.com/api/?api=frp
```

### 可用操作

| 操作 | 说明 | 请求方式 |
|------|------|----------|
| `server_status` | 获取服务器状态 | GET |
| `frps_status` | 获取FRPS服务状态 | GET |
| `get_users` | 获取所有用户 | GET |
| `get_user` | 获取单个用户 | GET |
| `create_user` | 创建用户 | POST |
| `update_user` | 更新用户 | POST |
| `delete_user` | 删除用户 | POST |
| `reload_config` | 重载配置 | POST |

### 示例请求

```bash
# 获取服务器状态
curl -H "Authorization: Bearer your-panel-token" \
     "http://your-panel-domain.com/api/?api=frp&action=server_status"

# 获取所有用户
curl -H "Authorization: Bearer your-panel-token" \
     "http://your-panel-domain.com/api/?api=frp&action=get_users"
```

## 文件结构

```
/
├── api/
│   ├── index.php          # 主 API 入口（已修改）
│   └── frp.php           # FRP API 处理器（新增）
├── modules/
│   └── frpusers.php      # FRP 用户管理模块（新增）
├── pages/
│   └── panel.php         # 面板主页（已修改，添加菜单）
├── frp-config.php        # FRP 配置文件（新增）
├── test-frp-api.php      # API 连接测试页面（新增）
└── FRP-MANAGEMENT-README.md  # 本说明文件（新增）
```

## 故障排除

### 连接失败
1. 检查 `frp-config.php` 中的配置是否正确
2. 确认 FRP API 服务器正在运行
3. 检查网络连接和防火墙设置
4. 使用 `test-frp-api.php` 进行连接测试

### 权限错误
1. 确保以管理员身份登录面板
2. 检查用户组设置

### API 响应错误
1. 检查 FRP API Token 是否正确
2. 查看 FRP 服务器日志
3. 确认 API 版本兼容性

## 安全注意事项

1. **保护 API Token**: 不要在客户端代码中暴露 API Token
2. **网络安全**: 生产环境建议使用 HTTPS
3. **访问控制**: 只有管理员可以访问 FRP 用户管理功能
4. **定期更新**: 定期更换 API Token

## 版本兼容性

- **面板版本**: Sakura Panel 1.0.2+
- **PHP 版本**: PHP 7.0+
- **FRP API**: 兼容您提供的 frps-api 插件 1.0.0

## 技术支持

如果遇到问题，请检查：
1. PHP cURL 扩展是否已启用
2. 网络连接是否正常
3. FRP 服务器配置是否正确
4. API Token 是否匹配

## 更新日志

### v1.0.0 (2024-07-29)
- 初始版本发布
- 支持基本的 FRP 用户管理功能
- 集成服务器状态监控
- 提供 API 连接测试工具
