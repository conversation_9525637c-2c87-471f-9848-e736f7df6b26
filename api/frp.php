<?php
namespace SakuraPanel;

use SakuraPanel;

// FRP 服务器管理 API
// 用于与 FRP 服务器的 tokens 插件进行通信

// 加载 FRP 配置
$frp_config_file = ROOT . "/frp-config.php";
if (file_exists($frp_config_file)) {
    include($frp_config_file);
} else {
    // 默认配置
    $frp_config = [
        'server_url' => 'http://localhost:7300',
        'api_token' => 'frps-api-token-2024'
    ];
}

// FRP 服务器配置
define("FRP_SERVER_URL", $frp_config['server_url']);
define("FRP_API_TOKEN", $frp_config['api_token']);

class FrpApiClient {
    private $baseUrl;
    private $token;
    
    public function __construct($baseUrl = FRP_SERVER_URL, $token = FRP_API_TOKEN) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = $token;
    }
    
    /**
     * 发送 HTTP 请求到 FRP 服务器
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        switch(strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if($error) {
            return [
                'success' => false,
                'error' => 'Connection error: ' . $error,
                'http_code' => 0
            ];
        }
        
        $result = json_decode($response, true);
        if($result === null) {
            return [
                'success' => false,
                'error' => 'Invalid JSON response',
                'http_code' => $httpCode,
                'raw_response' => $response
            ];
        }
        
        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'data' => $result,
            'http_code' => $httpCode
        ];
    }
    
    /**
     * 获取服务器状态
     */
    public function getServerStatus() {
        return $this->makeRequest('GET', '/api/server/status');
    }
    
    /**
     * 获取 FRPS 服务状态
     */
    public function getFrpsStatus() {
        return $this->makeRequest('GET', '/api/frps/status');
    }
    
    /**
     * 获取所有用户
     */
    public function getUsers() {
        return $this->makeRequest('GET', '/api/users');
    }
    
    /**
     * 获取单个用户
     */
    public function getUser($username) {
        return $this->makeRequest('GET', '/api/users/' . urlencode($username));
    }
    
    /**
     * 创建用户
     */
    public function createUser($userData) {
        return $this->makeRequest('POST', '/api/users', $userData);
    }
    
    /**
     * 更新用户
     */
    public function updateUser($username, $userData) {
        return $this->makeRequest('PUT', '/api/users/' . urlencode($username), $userData);
    }
    
    /**
     * 删除用户
     */
    public function deleteUser($username) {
        return $this->makeRequest('DELETE', '/api/users/' . urlencode($username));
    }
    
    /**
     * 更新用户字段
     */
    public function updateUserField($username, $field, $value) {
        return $this->makeRequest('PUT', '/api/users/' . urlencode($username) . '/field/' . urlencode($field), ['value' => $value]);
    }
    
    /**
     * 添加端口
     */
    public function addPort($username, $port) {
        return $this->makeRequest('POST', '/api/users/' . urlencode($username) . '/ports', ['port' => $port]);
    }
    
    /**
     * 删除端口
     */
    public function removePort($username, $port) {
        return $this->makeRequest('DELETE', '/api/users/' . urlencode($username) . '/ports/' . urlencode($port));
    }
    
    /**
     * 添加域名
     */
    public function addDomain($username, $domain) {
        return $this->makeRequest('POST', '/api/users/' . urlencode($username) . '/domains', ['domain' => $domain]);
    }
    
    /**
     * 删除域名
     */
    public function removeDomain($username, $domain) {
        return $this->makeRequest('DELETE', '/api/users/' . urlencode($username) . '/domains/' . urlencode($domain));
    }
    
    /**
     * 添加子域名
     */
    public function addSubdomain($username, $subdomain) {
        return $this->makeRequest('POST', '/api/users/' . urlencode($username) . '/subdomains', ['subdomain' => $subdomain]);
    }
    
    /**
     * 删除子域名
     */
    public function removeSubdomain($username, $subdomain) {
        return $this->makeRequest('DELETE', '/api/users/' . urlencode($username) . '/subdomains/' . urlencode($subdomain));
    }
    
    /**
     * 手动重载配置
     */
    public function reloadConfig() {
        return $this->makeRequest('POST', '/api/reload');
    }
}

// 处理 API 请求
$frpClient = new FrpApiClient();

// 设置错误处理
error_reporting(0); // 关闭错误显示，避免影响JSON输出
ini_set('display_errors', 0);

// 设置内容类型
Header("Content-Type: application/json");

// 检查用户权限
$um = new SakuraPanel\UserManager();
if(!$um->isLogged()) {
    Header("HTTP/1.1 401 Unauthorized");
    echo json_encode(['success' => false, 'error' => '未登录']);
    exit;
}

$userInfo = SakuraPanel\Database::querySingleLine("users", ["username" => $_SESSION['user']]);
if(!$userInfo || $userInfo['group'] !== "admin") {
    Header("HTTP/1.1 403 Forbidden");
    echo json_encode(['success' => false, 'error' => '权限不足']);
    exit;
}

// 检查 CSRF Token
if($_SERVER['REQUEST_METHOD'] !== 'GET') {
    try {
        SakuraPanel\Utils::checkCsrf();
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'CSRF验证失败']);
        exit;
    }
}

$action = $_GET['action'] ?? '';

switch($action) {
    case 'test':
        echo json_encode([
            'success' => true,
            'message' => 'FRP API 工作正常',
            'timestamp' => date('Y-m-d H:i:s'),
            'user' => $_SESSION['user'] ?? 'unknown'
        ]);
        break;
    case 'server_status':
        $result = $frpClient->getServerStatus();
        echo json_encode($result);
        break;
        
    case 'frps_status':
        $result = $frpClient->getFrpsStatus();
        echo json_encode($result);
        break;
        
    case 'get_users':
        $result = $frpClient->getUsers();
        echo json_encode($result);
        break;
        
    case 'get_user':
        if(!isset($_GET['username'])) {
            echo json_encode(['success' => false, 'error' => '缺少用户名参数']);
            break;
        }
        $result = $frpClient->getUser($_GET['username']);
        echo json_encode($result);
        break;
        
    case 'create_user':
        if($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '请求方法错误']);
            break;
        }
        $input = json_decode(file_get_contents('php://input'), true);
        if(!$input) {
            echo json_encode(['success' => false, 'error' => '无效的JSON数据']);
            break;
        }
        $result = $frpClient->createUser($input);
        echo json_encode($result);
        break;
        
    case 'update_user':
        if($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '请求方法错误']);
            break;
        }
        if(!isset($_GET['username'])) {
            echo json_encode(['success' => false, 'error' => '缺少用户名参数']);
            break;
        }
        $input = json_decode(file_get_contents('php://input'), true);
        if(!$input) {
            echo json_encode(['success' => false, 'error' => '无效的JSON数据']);
            break;
        }
        $result = $frpClient->updateUser($_GET['username'], $input);
        echo json_encode($result);
        break;
        
    case 'delete_user':
        if($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '请求方法错误']);
            break;
        }
        if(!isset($_GET['username'])) {
            echo json_encode(['success' => false, 'error' => '缺少用户名参数']);
            break;
        }
        $result = $frpClient->deleteUser($_GET['username']);
        echo json_encode($result);
        break;
        
    case 'reload_config':
        if($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '请求方法错误']);
            break;
        }
        $result = $frpClient->reloadConfig();
        echo json_encode($result);
        break;

    case 'check_node_status':
        if(!isset($_GET['node_id'])) {
            echo json_encode(['success' => false, 'error' => '缺少节点ID参数']);
            break;
        }
        $result = checkSingleNodeStatus($_GET['node_id']);
        echo json_encode($result);
        break;

    case 'check_all_nodes_status':
        $result = checkAllNodesStatus();
        echo json_encode($result);
        break;

    default:
        echo json_encode([
            'success' => false,
            'error' => '未知的操作',
            'available_actions' => [
                'server_status' => '获取服务器状态',
                'frps_status' => '获取FRPS服务状态',
                'get_users' => '获取所有用户',
                'get_user' => '获取单个用户 (需要username参数)',
                'create_user' => '创建用户 (POST请求)',
                'update_user' => '更新用户 (POST请求，需要username参数)',
                'delete_user' => '删除用户 (POST请求，需要username参数)',
                'reload_config' => '重载配置 (POST请求)',
                'check_node_status' => '检测单个节点状态 (需要node_id参数)',
                'check_all_nodes_status' => '检测所有节点状态'
            ]
        ]);
        break;
}

/**
 * 检测单个节点状态
 */
function checkSingleNodeStatus($nodeId) {
    // 从数据库获取节点信息
    $node = SakuraPanel\Database::querySingleLine("nodes", ["id" => $nodeId]);
    if (!$node) {
        return [
            'success' => false,
            'error' => '节点不存在'
        ];
    }

    // 构建 FRP API URL
    $apiUrl = "http://{$node['hostname']}:{$node['admin_port']}";
    $apiToken = $node['admin_pass']; // 使用管理密码作为 API Token

    // 创建临时的 FRP 客户端
    $tempClient = new FrpApiClient($apiUrl, $apiToken);

    $startTime = microtime(true);

    // 尝试获取服务器状态
    $serverResult = $tempClient->getServerStatus();
    $frpsResult = $tempClient->getFrpsStatus();

    $endTime = microtime(true);
    $responseTime = round(($endTime - $startTime) * 1000); // 转换为毫秒

    // 判断节点状态 - 更严格的检查
    $isOnline = false;
    $message = '';

    // 首先检查HTTP请求是否成功
    if ($serverResult['success'] || $frpsResult['success']) {
        // 至少有一个API响应成功，检查数据格式
        $serverOk = $serverResult['success'] &&
                   isset($serverResult['data']['success']) &&
                   $serverResult['data']['success'] === true;

        $frpsOk = $frpsResult['success'] &&
                 isset($frpsResult['data']['success']) &&
                 $frpsResult['data']['success'] === true;

        if ($serverOk && $frpsOk) {
            $isOnline = true;
            $message = 'FRP 服务器运行正常';
        } else if ($serverOk || $frpsOk) {
            $isOnline = false;
            $message = 'FRP 服务部分正常 (服务器:' . ($serverOk ? '正常' : '异常') . ', FRPS:' . ($frpsOk ? '正常' : '异常') . ')';
        } else {
            $isOnline = false;
            $message = 'FRP API 响应格式错误';
        }
    } else {
        // 两个API都失败
        $errors = [];
        if (!$serverResult['success']) {
            $errors[] = '服务器状态: ' . ($serverResult['error'] ?: 'HTTP ' . $serverResult['http_code']);
        }
        if (!$frpsResult['success']) {
            $errors[] = 'FRPS状态: ' . ($frpsResult['error'] ?: 'HTTP ' . $frpsResult['http_code']);
        }
        $message = 'FRP 服务器连接失败: ' . implode(', ', $errors);
        $isOnline = false;
    }

    if ($isOnline) {
        $status = 'online';
        $statusCode = 200;
        // 更新数据库中的节点状态
        SakuraPanel\Database::update("nodes", ["status" => "200"], ["id" => $nodeId]);
    } else {
        $status = 'offline';
        $statusCode = 500;
        // 更新数据库中的节点状态
        SakuraPanel\Database::update("nodes", ["status" => "500"], ["id" => $nodeId]);
    }

    return [
        'success' => true,
        'status' => $status,
        'code' => $statusCode,
        'response_time' => $responseTime,
        'message' => $message,
        'server_data' => $serverResult['success'] ? $serverResult['data'] : null,
        'frps_data' => $frpsResult['success'] ? $frpsResult['data'] : null,
        'debug' => [
            'server_result' => $serverResult,
            'frps_result' => $frpsResult,
            'api_url' => $apiUrl,
            'api_token_prefix' => substr($apiToken, 0, 10) . '...'
        ]
    ];
}

/**
 * 检测所有节点状态
 */
function checkAllNodesStatus() {
    // 获取所有节点
    $result = SakuraPanel\Database::query("nodes", []);
    $nodes = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $nodes[] = $row;
        }
    }

    $results = [];

    foreach ($nodes as $node) {
        $nodeResult = checkSingleNodeStatus($node['id']);
        $results[$node['id']] = $nodeResult;
    }

    return [
        'success' => true,
        'data' => $results,
        'total_nodes' => count($nodes),
        'timestamp' => date('Y-m-d H:i:s')
    ];
}
