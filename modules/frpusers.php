<?php
namespace SakuraPanel;

use SakuraPanel;

$page_title = "FRP用户管理";
$um = new SakuraPanel\UserManager();
$rs = Database::querySingleLine("users", Array("username" => $_SESSION['user']));

if(!$rs || $rs['group'] !== "admin") {
	exit("<script>location='?page=panel';</script>");
}
?>
<style type="text/css">
.fix-text p {
	margin-bottom: 4px;
}
.sub-heading {
	width: calc(100% - 16px);
    height: 0!important;
    border-top: 1px solid #e9f1f1!important;
    text-align: center!important;
    margin-top: 32px!important;
    margin-bottom: 40px!important;
	margin-left: 7px;
}
.sub-heading span {
    display: inline-block;
    position: relative;
    padding: 0 17px;
    top: -11px;
    font-size: 16px;
    color: #058;
    background-color: #fff;
}
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}
.status-online { background-color: #28a745; }
.status-offline { background-color: #dc3545; }
.status-unknown { background-color: #6c757d; }
</style>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><?php echo $page_title; ?>&nbsp;&nbsp;<small class="text-muted text-xs">管理FRP服务器用户</small></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">
                        <a href="?">主页</a>
                    </li>
                    <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
                </ol>
            </div>
        </div>
	</div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- 服务器状态卡片 -->
        <div class="row mb-3">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">服务器状态</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-primary" onclick="refreshServerStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="server-status">
                            <p class="text-center text-muted">正在加载服务器状态...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">FRPS服务状态</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-primary" onclick="refreshFrpsStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="frps-status">
                            <p class="text-center text-muted">正在加载FRPS状态...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header border-0">
                        <div class="d-flex justify-content-between">
                            <h3 class="card-title">FRP用户列表</h3>
                            <div>
                                <button type="button" class="btn btn-sm btn-success" onclick="showCreateUserModal()">
                                    <i class="fas fa-plus"></i> 创建用户
                                </button>
                                <button type="button" class="btn btn-sm btn-primary" onclick="refreshUserList()">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                                <button type="button" class="btn btn-sm btn-warning" onclick="reloadConfig()">
                                    <i class="fas fa-redo"></i> 重载配置
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="user-list">
                            <p class="text-center text-muted p-3">正在加载用户列表...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header border-0">
                        <div class="d-flex justify-content-between">
                            <h3 class="card-title">用户详情</h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="user-details">
                            <p class="text-center text-muted">选择一个用户查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建FRP用户</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" class="form-control" name="user" required>
                    </div>
                    <div class="form-group">
                        <label>Token</label>
                        <input type="text" class="form-control" name="token" required>
                        <small class="form-text text-muted">用户的访问令牌</small>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <input type="text" class="form-control" name="comment">
                    </div>
                    <div class="form-group">
                        <label>端口列表</label>
                        <input type="text" class="form-control" name="ports" placeholder="8080,9090">
                        <small class="form-text text-muted">多个端口用逗号分隔</small>
                    </div>
                    <div class="form-group">
                        <label>域名列表</label>
                        <input type="text" class="form-control" name="domains" placeholder="example.com,test.com">
                        <small class="form-text text-muted">多个域名用逗号分隔</small>
                    </div>
                    <div class="form-group">
                        <label>子域名列表</label>
                        <input type="text" class="form-control" name="subdomains" placeholder="test,demo">
                        <small class="form-text text-muted">多个子域名用逗号分隔</small>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="enable" checked>
                            <label class="form-check-label">启用用户</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">创建</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
var csrf_token = "<?php echo $_SESSION['token']; ?>";
var currentUser = null;

// 页面加载时初始化
$(document).ready(function() {
    refreshServerStatus();
    refreshFrpsStatus();
    refreshUserList();
});

// 刷新服务器状态
function refreshServerStatus() {
    $.get('?api=frp&action=server_status&csrf=' + csrf_token)
        .done(function(data) {
            if(data.success && data.data) {
                var status = data.data;
                var html = '<div class="row">';
                html += '<div class="col-6"><strong>状态:</strong> <span class="status-indicator status-online"></span>在线</div>';
                html += '<div class="col-6"><strong>CPU:</strong> ' + status.cpu_percent + '%</div>';
                html += '<div class="col-6"><strong>内存:</strong> ' + status.memory.percent + '%</div>';
                html += '<div class="col-6"><strong>磁盘:</strong> ' + status.disk.percent + '%</div>';
                html += '</div>';
                $('#server-status').html(html);
            } else {
                $('#server-status').html('<p class="text-danger">无法获取服务器状态</p>');
            }
        })
        .fail(function() {
            $('#server-status').html('<p class="text-danger">连接服务器失败</p>');
        });
}

// 刷新FRPS状态
function refreshFrpsStatus() {
    $.get('?api=frp&action=frps_status&csrf=' + csrf_token)
        .done(function(data) {
            if(data.success && data.data) {
                var status = data.data;
                var html = '<div class="row">';
                html += '<div class="col-6"><strong>状态:</strong> <span class="status-indicator ' + 
                         (status.running ? 'status-online' : 'status-offline') + '"></span>' + 
                         (status.running ? '运行中' : '已停止') + '</div>';
                if(status.running) {
                    html += '<div class="col-6"><strong>PID:</strong> ' + status.pid + '</div>';
                    html += '<div class="col-6"><strong>CPU:</strong> ' + status.cpu_percent + '%</div>';
                    html += '<div class="col-6"><strong>内存:</strong> ' + status.memory_mb + 'MB</div>';
                    html += '<div class="col-12"><strong>连接数:</strong> ' + status.connections + '</div>';
                }
                html += '</div>';
                $('#frps-status').html(html);
            } else {
                $('#frps-status').html('<p class="text-danger">无法获取FRPS状态</p>');
            }
        })
        .fail(function() {
            $('#frps-status').html('<p class="text-danger">连接FRPS失败</p>');
        });
}

// 刷新用户列表
function refreshUserList() {
    $.get('?api=frp&action=get_users&csrf=' + csrf_token)
        .done(function(data) {
            if(data.success && data.data) {
                var users = data.data;
                var html = '<table class="table table-striped">';
                html += '<thead><tr><th>用户名</th><th>状态</th><th>端口数</th><th>域名数</th><th>操作</th></tr></thead><tbody>';
                
                for(var username in users) {
                    var user = users[username];
                    html += '<tr>';
                    html += '<td>' + username + '</td>';
                    html += '<td><span class="badge badge-' + (user.enable ? 'success' : 'danger') + '">' + 
                            (user.enable ? '启用' : '禁用') + '</span></td>';
                    html += '<td>' + (user.ports ? user.ports.length : 0) + '</td>';
                    html += '<td>' + (user.domains ? user.domains.length : 0) + '</td>';
                    html += '<td>';
                    html += '<button class="btn btn-sm btn-info" onclick="viewUser(\'' + username + '\')">查看</button> ';
                    html += '<button class="btn btn-sm btn-danger" onclick="deleteUser(\'' + username + '\')">删除</button>';
                    html += '</td>';
                    html += '</tr>';
                }
                
                html += '</tbody></table>';
                $('#user-list').html(html);
            } else {
                $('#user-list').html('<p class="text-center text-danger">无法获取用户列表</p>');
            }
        })
        .fail(function() {
            $('#user-list').html('<p class="text-center text-danger">连接失败</p>');
        });
}

// 查看用户详情
function viewUser(username) {
    currentUser = username;
    $.get('?api=frp&action=get_user&username=' + encodeURIComponent(username) + '&csrf=' + csrf_token)
        .done(function(data) {
            if(data.success && data.data) {
                var user = data.data;
                var html = '<h5>用户: ' + username + '</h5>';
                html += '<p><strong>Token:</strong> ' + user.token + '</p>';
                html += '<p><strong>状态:</strong> <span class="badge badge-' + (user.enable ? 'success' : 'danger') + '">' + 
                        (user.enable ? '启用' : '禁用') + '</span></p>';
                html += '<p><strong>备注:</strong> ' + (user.comment || '无') + '</p>';
                html += '<p><strong>端口:</strong> ' + (user.ports ? user.ports.join(', ') : '无') + '</p>';
                html += '<p><strong>域名:</strong> ' + (user.domains ? user.domains.join(', ') : '无') + '</p>';
                html += '<p><strong>子域名:</strong> ' + (user.subdomains ? user.subdomains.join(', ') : '无') + '</p>';
                
                html += '<div class="mt-3">';
                html += '<button class="btn btn-sm btn-' + (user.enable ? 'warning' : 'success') + '" onclick="toggleUser(\'' + username + '\', ' + !user.enable + ')">';
                html += (user.enable ? '禁用' : '启用') + '用户</button> ';
                html += '<button class="btn btn-sm btn-primary" onclick="editUser(\'' + username + '\')">编辑</button>';
                html += '</div>';
                
                $('#user-details').html(html);
            } else {
                $('#user-details').html('<p class="text-danger">无法获取用户详情</p>');
            }
        })
        .fail(function() {
            $('#user-details').html('<p class="text-danger">连接失败</p>');
        });
}

// 显示创建用户模态框
function showCreateUserModal() {
    $('#createUserModal').modal('show');
}

// 创建用户
function createUser() {
    var formData = $('#createUserForm').serializeArray();
    var userData = {};

    // 转换表单数据
    formData.forEach(function(item) {
        if(item.name === 'ports' || item.name === 'domains' || item.name === 'subdomains') {
            userData[item.name] = item.value ? item.value.split(',').map(s => s.trim()).filter(s => s) : [];
        } else if(item.name === 'enable') {
            userData[item.name] = true;
        } else {
            userData[item.name] = item.value;
        }
    });

    // 如果没有勾选enable，设为false
    if(!userData.enable) {
        userData.enable = false;
    }

    $.ajax({
        url: '?api=frp&action=create_user&csrf=' + csrf_token,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(userData),
        success: function(data) {
            if(data.success) {
                alert('用户创建成功！');
                $('#createUserModal').modal('hide');
                $('#createUserForm')[0].reset();
                refreshUserList();
            } else {
                alert('创建失败: ' + (data.error || '未知错误'));
            }
        },
        error: function() {
            alert('请求失败，请检查网络连接');
        }
    });
}

// 删除用户
function deleteUser(username) {
    if(!confirm('确定要删除用户 "' + username + '" 吗？此操作不可撤销！')) {
        return;
    }

    $.ajax({
        url: '?api=frp&action=delete_user&username=' + encodeURIComponent(username) + '&csrf=' + csrf_token,
        method: 'POST',
        success: function(data) {
            if(data.success) {
                alert('用户删除成功！');
                refreshUserList();
                if(currentUser === username) {
                    $('#user-details').html('<p class="text-center text-muted">选择一个用户查看详情</p>');
                    currentUser = null;
                }
            } else {
                alert('删除失败: ' + (data.error || '未知错误'));
            }
        },
        error: function() {
            alert('请求失败，请检查网络连接');
        }
    });
}

// 切换用户启用状态
function toggleUser(username, enable) {
    $.ajax({
        url: '?api=frp&action=update_user&username=' + encodeURIComponent(username) + '&csrf=' + csrf_token,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({enable: enable}),
        success: function(data) {
            if(data.success) {
                alert('用户状态更新成功！');
                refreshUserList();
                if(currentUser === username) {
                    viewUser(username);
                }
            } else {
                alert('更新失败: ' + (data.error || '未知错误'));
            }
        },
        error: function() {
            alert('请求失败，请检查网络连接');
        }
    });
}

// 编辑用户（简化版，只能修改备注）
function editUser(username) {
    var comment = prompt('请输入新的备注:', '');
    if(comment === null) return;

    $.ajax({
        url: '?api=frp&action=update_user&username=' + encodeURIComponent(username) + '&csrf=' + csrf_token,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({comment: comment}),
        success: function(data) {
            if(data.success) {
                alert('备注更新成功！');
                viewUser(username);
            } else {
                alert('更新失败: ' + (data.error || '未知错误'));
            }
        },
        error: function() {
            alert('请求失败，请检查网络连接');
        }
    });
}

// 重载配置
function reloadConfig() {
    if(!confirm('确定要重载FRP服务器配置吗？')) {
        return;
    }

    $.ajax({
        url: '?api=frp&action=reload_config&csrf=' + csrf_token,
        method: 'POST',
        success: function(data) {
            if(data.success) {
                alert('配置重载成功！');
            } else {
                alert('重载失败: ' + (data.error || '未知错误'));
            }
        },
        error: function() {
            alert('请求失败，请检查网络连接');
        }
    });
}
</script>
