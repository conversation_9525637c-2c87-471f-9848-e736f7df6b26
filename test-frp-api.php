<?php
/**
 * FRP API 连接测试页面
 * 
 * 用于测试与 FRP 服务器 API 的连接是否正常
 * 访问方式: http://your-domain.com/test-frp-api.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义根目录
define("ROOT", realpath(__DIR__));

// 加载配置
include(ROOT . "/frp-config.php");

// 简单的 API 测试函数
function testFrpApi($url, $token) {
    $testEndpoint = '/api/server/status';
    $fullUrl = rtrim($url, '/') . $testEndpoint;
    
    $headers = [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fullUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'url' => $fullUrl,
        'http_code' => $httpCode,
        'error' => $error,
        'response' => $response,
        'success' => $httpCode >= 200 && $httpCode < 300 && empty($error)
    ];
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FRP API 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .config-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px 10px 0;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FRP API 连接测试</h1>
        
        <h2>当前配置</h2>
        <div class="config-item">
            <strong>服务器地址:</strong> <?php echo htmlspecialchars($frp_config['server_url']); ?>
        </div>
        <div class="config-item">
            <strong>API Token:</strong> <?php echo htmlspecialchars(substr($frp_config['api_token'], 0, 10) . '...'); ?>
        </div>
        <div class="config-item">
            <strong>超时时间:</strong> <?php echo $frp_config['timeout']; ?> 秒
        </div>
        
        <h2>连接测试结果</h2>
        
        <?php
        $testResult = testFrpApi($frp_config['server_url'], $frp_config['api_token']);
        
        if ($testResult['success']) {
            echo '<div class="success">';
            echo '<strong>✓ 连接成功!</strong> FRP API 服务器响应正常。';
            echo '</div>';
            
            $responseData = json_decode($testResult['response'], true);
            if ($responseData) {
                echo '<h3>服务器响应数据:</h3>';
                echo '<pre>' . json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            }
        } else {
            echo '<div class="error">';
            echo '<strong>✗ 连接失败!</strong><br>';
            echo 'HTTP 状态码: ' . $testResult['http_code'] . '<br>';
            if (!empty($testResult['error'])) {
                echo 'cURL 错误: ' . htmlspecialchars($testResult['error']) . '<br>';
            }
            echo '请求地址: ' . htmlspecialchars($testResult['url']);
            echo '</div>';
            
            echo '<div class="info">';
            echo '<strong>可能的解决方案:</strong><br>';
            echo '1. 检查 FRP API 服务器是否正在运行<br>';
            echo '2. 确认服务器地址和端口是否正确<br>';
            echo '3. 检查 API Token 是否正确<br>';
            echo '4. 确认防火墙设置允许访问该端口<br>';
            echo '5. 如果使用 HTTPS，检查 SSL 证书是否有效';
            echo '</div>';
            
            if (!empty($testResult['response'])) {
                echo '<h3>原始响应:</h3>';
                echo '<pre>' . htmlspecialchars($testResult['response']) . '</pre>';
            }
        }
        ?>
        
        <h2>快速操作</h2>
        <a href="?refresh=1" class="btn">重新测试</a>
        <a href="?page=panel&module=frpusers" class="btn">进入FRP用户管理</a>
        
        <h2>配置说明</h2>
        <div class="info">
            <p><strong>如何配置:</strong></p>
            <p>1. 编辑 <code>frp-config.php</code> 文件</p>
            <p>2. 修改 <code>server_url</code> 为您的 FRP API 服务器地址</p>
            <p>3. 修改 <code>api_token</code> 为您在 frps-api 插件中设置的令牌</p>
            <p>4. 保存文件并刷新此页面进行测试</p>
        </div>
        
        <h2>FRP API 插件状态检查</h2>
        <div class="info">
            <p><strong>确保您的 FRP 服务器已正确配置:</strong></p>
            <p>1. FRP 服务器已安装并运行 frps-api 插件</p>
            <p>2. 插件监听在正确的地址和端口上</p>
            <p>3. API Token 已正确设置</p>
            <p>4. 网络连接正常，无防火墙阻拦</p>
        </div>
        
        <h2>技术信息</h2>
        <div class="config-item">
            <strong>PHP cURL 支持:</strong> <?php echo function_exists('curl_init') ? '✓ 已启用' : '✗ 未启用'; ?>
        </div>
        <div class="config-item">
            <strong>PHP 版本:</strong> <?php echo PHP_VERSION; ?>
        </div>
        <div class="config-item">
            <strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>
</body>
</html>
