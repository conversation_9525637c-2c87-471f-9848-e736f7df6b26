<?php
/**
 * 节点 API 测试页面
 * 
 * 用于测试节点状态检测功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义根目录
define("ROOT", realpath(__DIR__));

// 加载必要的文件
include(ROOT . "/configuration.php");
include(ROOT . "/core/Database.php");

// 简单的节点状态测试函数
function testNodeConnection($hostname, $port, $apiToken) {
    $url = "http://{$hostname}:{$port}/api/server/status";
    
    $headers = [
        'Authorization: Bearer ' . $apiToken,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $endTime = microtime(true);
    $responseTime = round(($endTime - $startTime) * 1000);
    
    return [
        'url' => $url,
        'http_code' => $httpCode,
        'error' => $error,
        'response' => $response,
        'response_time' => $responseTime,
        'success' => $httpCode >= 200 && $httpCode < 300 && empty($error)
    ];
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点 API 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .node-item {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .node-item.success {
            border-left-color: #28a745;
        }
        .node-item.error {
            border-left-color: #dc3545;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px 10px 0;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            flex: 1;
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>节点 API 连接测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong>此页面用于测试数据库中配置的节点是否能够正常连接到对应的 FRP API 服务器。
        </div>
        
        <?php
        // 获取所有节点
        $db = new SakuraPanel\Database();
        $result = SakuraPanel\Database::query("nodes", []);
        $nodes = [];
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $nodes[] = $row;
            }
        }
        
        $totalNodes = count($nodes);
        $onlineNodes = 0;
        $offlineNodes = 0;
        $testResults = [];
        
        if ($totalNodes > 0) {
            echo "<h2>节点连接测试结果</h2>";
            
            foreach ($nodes as $node) {
                $testResult = testNodeConnection($node['hostname'], $node['admin_port'], $node['admin_pass']);
                $testResults[] = $testResult;
                
                if ($testResult['success']) {
                    $onlineNodes++;
                    $statusClass = 'success';
                    $statusText = '✓ 连接成功';
                } else {
                    $offlineNodes++;
                    $statusClass = 'error';
                    $statusText = '✗ 连接失败';
                }
                
                echo "<div class='node-item {$statusClass}'>";
                echo "<h4>{$node['name']} (ID: {$node['id']}) - {$statusText}</h4>";
                echo "<p><strong>地址:</strong> {$node['hostname']}:{$node['admin_port']}</p>";
                echo "<p><strong>描述:</strong> " . ($node['description'] ?: '无') . "</p>";
                echo "<p><strong>响应时间:</strong> {$testResult['response_time']}ms</p>";
                echo "<p><strong>HTTP状态:</strong> {$testResult['http_code']}</p>";
                
                if (!empty($testResult['error'])) {
                    echo "<p><strong>错误信息:</strong> {$testResult['error']}</p>";
                }
                
                if ($testResult['success'] && $testResult['response']) {
                    $responseData = json_decode($testResult['response'], true);
                    if ($responseData) {
                        echo "<details>";
                        echo "<summary>查看服务器响应数据</summary>";
                        echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                        echo "</details>";
                    }
                }
                
                echo "</div>";
            }
        } else {
            echo "<div class='info'>";
            echo "<strong>提示：</strong>数据库中暂无节点配置。请先在节点管理页面添加节点。";
            echo "</div>";
        }
        ?>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number"><?php echo $totalNodes; ?></div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #28a745;"><?php echo $onlineNodes; ?></div>
                <div class="stat-label">在线节点</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #dc3545;"><?php echo $offlineNodes; ?></div>
                <div class="stat-label">离线节点</div>
            </div>
        </div>
        
        <h2>快速操作</h2>
        <a href="?refresh=1" class="btn">重新测试</a>
        <a href="?page=panel&module=nodes" class="btn">节点管理</a>
        <a href="test-frp-api.php" class="btn">FRP API 测试</a>
        
        <h2>配置说明</h2>
        <div class="info">
            <p><strong>节点配置要求：</strong></p>
            <ol>
                <li><strong>服务器地址：</strong>FRP 服务器的域名或 IP 地址</li>
                <li><strong>FRP 端口：</strong>Frps 服务运行的端口（通常是 7000）</li>
                <li><strong>API 端口：</strong>FRP API 插件运行的端口（通常是 7300）</li>
                <li><strong>API Token：</strong>与 FRP API 插件配置的令牌一致</li>
            </ol>
            
            <p><strong>故障排除：</strong></p>
            <ul>
                <li>确保 FRP 服务器已安装并运行 frps-api 插件</li>
                <li>检查服务器地址和端口是否正确</li>
                <li>验证 API Token 是否与服务器端配置一致</li>
                <li>确认防火墙设置允许访问 API 端口</li>
            </ul>
        </div>
        
        <h2>技术信息</h2>
        <div class="node-item">
            <p><strong>PHP cURL 支持：</strong> <?php echo function_exists('curl_init') ? '✓ 已启用' : '✗ 未启用'; ?></p>
            <p><strong>PHP 版本：</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>测试时间：</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>数据库连接：</strong> <?php echo class_exists('SakuraPanel\Database') ? '✓ 正常' : '✗ 异常'; ?></p>
        </div>
    </div>
</body>
</html>
